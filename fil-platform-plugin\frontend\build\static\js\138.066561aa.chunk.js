"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[138],{9138:(e,r,s)=>{s.r(r),s.d(r,{default:()=>b});var n=s(5043),t=s(4063),a=s(3519),i=s(1072),d=s(8602),l=s(8628),c=s(4282),o=s(3722),h=s(7994),m=s(4196),u=s(3083),x=s(1719),j=s(3204),_=s(4312),p=s(4117),g=s(579);const b=()=>{var e,r,s;const{t:b}=(0,p.Bd)(),[v,A]=(0,n.useState)([]),[f,w]=(0,n.useState)(!0),[y,k]=(0,n.useState)(""),[N,C]=(0,n.useState)(""),[S,F]=(0,n.useState)(""),[E,D]=(0,n.useState)(""),[L,B]=(0,n.useState)([]),[H,T]=(0,n.useState)(!1),[G,q]=(0,n.useState)(null),[I,R]=(0,n.useState)(!1),[z,O]=(0,n.useState)(""),[M,U]=(0,n.useState)(""),[$,K]=(0,n.useState)(!1),[P,J]=(0,n.useState)(null),[Q,W]=(0,n.useState)([]),[X,Y]=(0,n.useState)(""),[V,Z]=(0,n.useState)(!1),[ee,re]=(0,n.useState)(""),[se,ne]=(0,n.useState)(""),[te,ae]=(0,n.useState)(!1),[ie,de]=(0,n.useState)(""),[le,ce]=(0,n.useState)(""),[oe,he]=(0,n.useState)(""),[me,ue]=(0,n.useState)(!1),[xe,je]=(0,n.useState)(""),[_e,pe]=(0,n.useState)("");(0,n.useEffect)(()=>{(async()=>{const e=(0,_.b)();if(!e)return;w(!0);const{data:{user:r}}=await e.auth.getUser();if(!r)return void w(!1);const{data:s,error:n}=await e.from("customer_profiles").select("user_id, real_name, id_number, id_img_front, id_img_back, verify_status").eq("agent_id",r.id).order("created_at",{ascending:!1});if(n||!s)return console.error("Error fetching customer_profiles:",n),void w(!1);const t=s.map(e=>e.user_id).filter(Boolean),{data:a,error:i}=await e.from("users").select("id, email, created_at").in("id",t);i&&console.error("Error fetching users:",i);const d=new Map((a||[]).map(e=>[e.id,e])),l=s.map(e=>({...e,users:d.get(e.user_id)||{}}));A(l),w(!1)})()},[]),(0,n.useEffect)(()=>{let e=v;y&&(e=e.filter(e=>{var r,s,n;return(null===(r=e.users)||void 0===r||null===(s=r.email)||void 0===s?void 0:s.toLowerCase().includes(y.toLowerCase()))||(null===(n=e.real_name)||void 0===n?void 0:n.toLowerCase().includes(y.toLowerCase()))})),N&&(e=e.filter(e=>e.verify_status===N)),S&&(e=e.filter(e=>{var r;return new Date(null===(r=e.users)||void 0===r?void 0:r.created_at)>=new Date(S)})),E&&(e=e.filter(e=>{var r;return new Date(null===(r=e.users)||void 0===r?void 0:r.created_at)<=new Date(E)})),B(e)},[v,y,N,S,E]);const ge=e=>{switch(e){case"approved":return(0,g.jsx)(t.A,{bg:"success",children:b("approved")});case"pending":return(0,g.jsx)(t.A,{bg:"warning",children:b("pending_review")});case"rejected":return(0,g.jsx)(t.A,{bg:"danger",children:b("rejected")});case"under_review":return(0,g.jsx)(t.A,{bg:"info",children:b("under_review")});default:return(0,g.jsx)(t.A,{bg:"secondary",children:e||b("not_submitted")})}},be=()=>{ae(!1),de(""),ce(""),he(""),je(""),pe("")},ve=async e=>{if(G){R(!0),O(""),U("");try{const r=(0,_.b)();if(!r)throw new Error("Database connection failed");const{data:s,error:n}=await r.from("customer_profiles").update({verify_status:e}).eq("user_id",G.user_id).select();if(n)throw console.error("Database error:",n),n;if(!s||0===s.length){const{data:e,error:s}=await r.from("customer_profiles").select("*").eq("user_id",G.user_id);if(s)throw console.error("Error checking existing record:",s),s;if(!e||0===e.length)throw new Error("Customer profile not found")}A(r=>r.map(r=>r.user_id===G.user_id?{...r,verify_status:e}:r)),q(r=>({...r,verify_status:e})),U(b("approved"===e?"kyc_approved_success":"kyc_rejected_success")),setTimeout(()=>{T(!1),q(null)},1500)}catch(r){console.error("Error updating KYC status:",r),O(r.message||b("kyc_update_error"))}finally{R(!1)}}},Ae=()=>{T(!1),q(null),O(""),U("")},fe=async e=>{J(e),re(""),ne(""),Y(""),await(async()=>{const e=(0,_.b)();if(e)try{const{data:{user:r}}=await e.auth.getUser();if(!r)return;const{data:s,error:n}=await e.from("agent_profiles").select("\n                    user_id,\n                    brand_name,\n                    commission_pct,\n                    users:user_id (\n                        email\n                    )\n                ").neq("user_id",r.id);if(n)return void console.error("Error fetching agents:",n);W(s||[])}catch(r){console.error("Error in fetchAvailableAgents:",r)}})(),K(!0)},we=()=>{K(!1),J(null),Y(""),re(""),ne("")};return f?(0,g.jsx)("div",{children:b("loading_members")}):(0,g.jsxs)(a.A,{children:[(0,g.jsx)("h2",{className:"mb-4",children:b("member_list")}),(0,g.jsx)(i.A,{className:"mb-4",children:(0,g.jsx)(d.A,{children:(0,g.jsx)(l.A,{children:(0,g.jsx)(l.A.Body,{children:(0,g.jsxs)(i.A,{className:"align-items-end",children:[(0,g.jsx)(d.A,{md:2,children:(0,g.jsxs)(c.A,{variant:"primary",onClick:()=>{ae(!0),de(""),ce(""),he(""),je(""),pe("")},className:"mb-2",children:[(0,g.jsx)(j.OiG,{className:"me-1"}),b("add_member")]})}),(0,g.jsx)(d.A,{md:3,children:(0,g.jsxs)(o.A.Group,{children:[(0,g.jsx)(o.A.Label,{children:b("search_username")}),(0,g.jsx)(h.A,{children:(0,g.jsx)(o.A.Control,{type:"text",placeholder:b("please_enter_username"),value:y,onChange:e=>k(e.target.value)})})]})}),(0,g.jsx)(d.A,{md:2,children:(0,g.jsxs)(o.A.Group,{children:[(0,g.jsx)(o.A.Label,{children:b("status_filter")}),(0,g.jsxs)(o.A.Select,{value:N,onChange:e=>C(e.target.value),children:[(0,g.jsx)("option",{value:"",children:b("please_select_status")}),(0,g.jsx)("option",{value:"pending",children:b("pending_review")}),(0,g.jsx)("option",{value:"approved",children:b("approved")}),(0,g.jsx)("option",{value:"rejected",children:b("rejected")}),(0,g.jsx)("option",{value:"under_review",children:b("under_review")})]})]})}),(0,g.jsx)(d.A,{md:2,children:(0,g.jsxs)(o.A.Group,{children:[(0,g.jsx)(o.A.Label,{children:b("start_date")}),(0,g.jsx)(o.A.Control,{type:"date",value:S,onChange:e=>F(e.target.value)})]})}),(0,g.jsx)(d.A,{md:2,children:(0,g.jsxs)(o.A.Group,{children:[(0,g.jsx)(o.A.Label,{children:b("end_date")}),(0,g.jsx)(o.A.Control,{type:"date",value:E,onChange:e=>D(e.target.value)})]})}),(0,g.jsx)(d.A,{md:1,children:(0,g.jsx)(c.A,{variant:"outline-primary",onClick:()=>{console.log("Search triggered")},className:"mb-2",children:(0,g.jsx)(j.KSO,{})})})]})})})})}),(0,g.jsx)(i.A,{children:(0,g.jsx)(d.A,{children:(0,g.jsx)(l.A,{children:(0,g.jsx)(l.A.Body,{children:(0,g.jsxs)(m.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,g.jsx)("thead",{children:(0,g.jsxs)("tr",{children:[(0,g.jsx)("th",{children:b("username")}),(0,g.jsx)("th",{children:b("real_name")}),(0,g.jsx)("th",{children:b("id_number")}),(0,g.jsx)("th",{children:b("id_front_image")}),(0,g.jsx)("th",{children:b("id_back_image")}),(0,g.jsx)("th",{children:b("status")}),(0,g.jsx)("th",{children:b("registration_time")}),(0,g.jsx)("th",{children:b("actions")})]})}),(0,g.jsx)("tbody",{children:0===L.length?(0,g.jsx)("tr",{children:(0,g.jsx)("td",{colSpan:"9",className:"text-center",children:b("no_members_found")})}):L.map(e=>{var r,s;return(0,g.jsxs)("tr",{children:[(0,g.jsx)("td",{children:(null===(r=e.users)||void 0===r?void 0:r.email)||"-"}),(0,g.jsx)("td",{children:e.real_name||"-"}),(0,g.jsx)("td",{children:e.id_number||"-"}),(0,g.jsx)("td",{children:e.id_img_front?(0,g.jsx)("img",{src:e.id_img_front,alt:"ID Front",style:{width:"60px",height:"40px",objectFit:"cover",borderRadius:"4px",cursor:"pointer"},onClick:()=>window.open(e.id_img_front,"_blank")}):(0,g.jsx)("span",{className:"text-muted",children:"-"})}),(0,g.jsx)("td",{children:e.id_img_back?(0,g.jsx)("img",{src:e.id_img_back,alt:"ID Back",style:{width:"60px",height:"40px",objectFit:"cover",borderRadius:"4px",cursor:"pointer"},onClick:()=>window.open(e.id_img_back,"_blank")}):(0,g.jsx)("span",{className:"text-muted",children:"-"})}),(0,g.jsx)("td",{children:ge(e.verify_status)}),(0,g.jsx)("td",{children:null!==(s=e.users)&&void 0!==s&&s.created_at?new Date(e.users.created_at).toLocaleString():"-"}),(0,g.jsx)("td",{children:(0,g.jsxs)("div",{className:"d-flex justify-content-between",children:[(0,g.jsx)(c.A,{size:"sm",variant:"outline-primary",onClick:()=>(e=>{q(e),T(!0),O(""),U("")})(e),title:b("kyc_review"),children:(0,g.jsx)(j.BAG,{})}),(0,g.jsx)(c.A,{size:"sm",variant:"outline-warning",onClick:()=>fe(e),title:b("change_agent"),children:(0,g.jsx)(j.yk7,{})})]})})]},e.user_id)})})]})})})})}),(0,g.jsxs)(u.A,{show:H,onHide:Ae,size:"lg",children:[(0,g.jsx)(u.A.Header,{closeButton:!0,className:"custom-modal-header",children:(0,g.jsx)(u.A.Title,{children:b("kyc_review")})}),(0,g.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    "}}),(0,g.jsx)(u.A.Body,{children:G&&(0,g.jsxs)(g.Fragment,{children:[z&&(0,g.jsx)(x.A,{variant:"danger",className:"mb-3",children:z}),M&&(0,g.jsx)(x.A,{variant:"success",className:"mb-3",children:M}),(0,g.jsxs)(i.A,{children:[(0,g.jsx)(d.A,{md:6,children:(0,g.jsxs)(l.A,{className:"mb-3",children:[(0,g.jsx)(l.A.Header,{children:(0,g.jsx)("strong",{children:b("customer_info")})}),(0,g.jsxs)(l.A.Body,{children:[(0,g.jsxs)("p",{children:[(0,g.jsxs)("strong",{children:[b("username"),":"]})," ",(null===(e=G.users)||void 0===e?void 0:e.email)||"-"]}),(0,g.jsxs)("p",{children:[(0,g.jsxs)("strong",{children:[b("real_name"),":"]})," ",G.real_name||"-"]}),(0,g.jsxs)("p",{children:[(0,g.jsxs)("strong",{children:[b("id_number"),":"]})," ",G.id_number||"-"]}),(0,g.jsxs)("p",{children:[(0,g.jsxs)("strong",{children:[b("current_status"),":"]})," ",ge(G.verify_status)]}),(0,g.jsxs)("p",{children:[(0,g.jsxs)("strong",{children:[b("registration_time"),":"]})," ",null!==(r=G.users)&&void 0!==r&&r.created_at?new Date(G.users.created_at).toLocaleString():"-"]})]})]})}),(0,g.jsx)(d.A,{md:6,children:(0,g.jsxs)(l.A,{className:"mb-3",children:[(0,g.jsx)(l.A.Header,{children:(0,g.jsx)("strong",{children:b("id_documents")})}),(0,g.jsxs)(l.A.Body,{children:[(0,g.jsxs)("div",{className:"mb-3",children:[(0,g.jsxs)("strong",{children:[b("id_front_image"),":"]}),(0,g.jsx)("div",{className:"mt-2",children:G.id_img_front?(0,g.jsx)("img",{src:G.id_img_front,alt:"ID Front",style:{width:"100%",maxHeight:"150px",objectFit:"contain",borderRadius:"4px",cursor:"pointer",border:"1px solid #dee2e6"},onClick:()=>window.open(G.id_img_front,"_blank")}):(0,g.jsx)("div",{className:"text-muted text-center py-3",style:{border:"1px dashed #dee2e6",borderRadius:"4px"},children:b("no_image_uploaded")})})]}),(0,g.jsxs)("div",{className:"mb-3",children:[(0,g.jsxs)("strong",{children:[b("id_back_image"),":"]}),(0,g.jsx)("div",{className:"mt-2",children:G.id_img_back?(0,g.jsx)("img",{src:G.id_img_back,alt:"ID Back",style:{width:"100%",maxHeight:"150px",objectFit:"contain",borderRadius:"4px",cursor:"pointer",border:"1px solid #dee2e6"},onClick:()=>window.open(G.id_img_back,"_blank")}):(0,g.jsx)("div",{className:"text-muted text-center py-3",style:{border:"1px dashed #dee2e6",borderRadius:"4px"},children:b("no_image_uploaded")})})]})]})]})})]})]})}),(0,g.jsxs)(u.A.Footer,{children:[(0,g.jsx)(c.A,{variant:"secondary",onClick:Ae,disabled:I,children:b("cancel")}),(0,g.jsx)(c.A,{variant:"danger",onClick:()=>ve("rejected"),disabled:I||"rejected"===(null===G||void 0===G?void 0:G.verify_status),children:I?(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),b("processing")]}):(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(j.QCr,{className:"me-1"}),b("reject")]})}),(0,g.jsx)(c.A,{variant:"success",onClick:()=>ve("approved"),disabled:I||"approved"===(null===G||void 0===G?void 0:G.verify_status),children:I?(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),b("processing")]}):(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(j.CMH,{className:"me-1"}),b("approve")]})})]})]}),(0,g.jsxs)(u.A,{show:$,onHide:we,size:"md",children:[(0,g.jsx)(u.A.Header,{closeButton:!0,children:(0,g.jsx)(u.A.Title,{children:b("change_agent")})}),(0,g.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    "}}),(0,g.jsxs)(u.A.Body,{children:[ee&&(0,g.jsx)(x.A,{variant:"danger",className:"mb-3",children:ee}),se&&(0,g.jsx)(x.A,{variant:"success",className:"mb-3",children:se}),P&&(0,g.jsx)("div",{className:"mb-4",children:(0,g.jsxs)(l.A,{children:[(0,g.jsx)(l.A.Header,{children:(0,g.jsx)("strong",{children:b("customer_info")})}),(0,g.jsxs)(l.A.Body,{children:[(0,g.jsxs)("p",{children:[(0,g.jsxs)("strong",{children:[b("username"),":"]})," ",(null===(s=P.users)||void 0===s?void 0:s.email)||"-"]}),(0,g.jsxs)("p",{children:[(0,g.jsxs)("strong",{children:[b("real_name"),":"]})," ",P.real_name||"-"]}),(0,g.jsxs)("p",{children:[(0,g.jsxs)("strong",{children:[b("current_status"),":"]})," ",ge(P.verify_status)]})]})]})}),(0,g.jsxs)(o.A.Group,{className:"mb-3",children:[(0,g.jsx)(o.A.Label,{children:(0,g.jsx)("strong",{children:b("select_new_agent")})}),(0,g.jsxs)(o.A.Select,{value:X,onChange:e=>Y(e.target.value),disabled:V,children:[(0,g.jsx)("option",{value:"",children:b("please_select_agent")}),Q.map(e=>{var r;return(0,g.jsxs)("option",{value:e.user_id,children:[e.brand_name||(null===(r=e.users)||void 0===r?void 0:r.email)||e.user_id,e.commission_pct&&` (${e.commission_pct}%)`]},e.user_id)})]}),0===Q.length&&(0,g.jsx)(o.A.Text,{className:"text-muted",children:b("no_available_agents")})]})]}),(0,g.jsxs)(u.A.Footer,{children:[(0,g.jsx)(c.A,{variant:"secondary",onClick:we,disabled:V,children:b("cancel")}),(0,g.jsx)(c.A,{variant:"primary",onClick:async()=>{if(P&&X){Z(!0),re(""),ne("");try{const e=(0,_.b)();if(!e)throw new Error("Database connection failed");const{data:r,error:s}=await e.from("customer_profiles").update({agent_id:X}).eq("user_id",P.user_id).select();if(s)throw console.error("Database error:",s),s;if(!r||0===r.length)throw new Error("Failed to update agent assignment");A(e=>e.filter(e=>e.user_id!==P.user_id)),ne(b("agent_changed_successfully")),setTimeout(()=>{K(!1),J(null),Y("")},1500)}catch(e){console.error("Error changing agent:",e),re(e.message||b("agent_change_error"))}finally{Z(!1)}}else re(b("please_select_agent"))},disabled:V||!X||0===Q.length,children:V?(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),b("processing")]}):(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(j.yk7,{className:"me-1"}),b("confirm_change")]})})]})]}),(0,g.jsxs)(u.A,{show:te,onHide:be,size:"md",children:[(0,g.jsx)(u.A.Header,{closeButton:!0,children:(0,g.jsx)(u.A.Title,{children:b("add_member")})}),(0,g.jsxs)(u.A.Body,{children:[xe&&(0,g.jsx)(x.A,{variant:"danger",className:"mb-3",children:xe}),_e&&(0,g.jsx)(x.A,{variant:"success",className:"mb-3",children:_e}),(0,g.jsxs)(o.A,{children:[(0,g.jsxs)(o.A.Group,{className:"mb-3",children:[(0,g.jsx)(o.A.Label,{children:(0,g.jsx)("strong",{children:b("email_address")})}),(0,g.jsx)(o.A.Control,{type:"email",value:ie,onChange:e=>de(e.target.value),placeholder:b("enter_email_address"),disabled:me,required:!0}),(0,g.jsx)(o.A.Text,{className:"text-muted",children:b("member_email_help")})]}),(0,g.jsxs)(o.A.Group,{className:"mb-3",children:[(0,g.jsx)(o.A.Label,{children:(0,g.jsx)("strong",{children:b("password")})}),(0,g.jsx)(o.A.Control,{type:"password",value:le,onChange:e=>ce(e.target.value),placeholder:b("enter_password"),disabled:me,minLength:6,required:!0}),(0,g.jsx)(o.A.Text,{className:"text-muted",children:b("password_min_6_chars")})]}),(0,g.jsxs)(o.A.Group,{className:"mb-3",children:[(0,g.jsx)(o.A.Label,{children:(0,g.jsx)("strong",{children:b("invite_code")})}),(0,g.jsx)(o.A.Control,{type:"text",value:oe,onChange:e=>he(e.target.value),placeholder:b("enter_invite_code"),disabled:me,required:!0}),(0,g.jsx)(o.A.Text,{className:"text-muted",children:b("invite_code_help")})]})]})]}),(0,g.jsxs)(u.A.Footer,{children:[(0,g.jsx)(c.A,{variant:"secondary",onClick:be,disabled:me,children:b("cancel")}),(0,g.jsx)(c.A,{variant:"primary",onClick:async()=>{if(!ie||!le||!oe)return void je(b("all_fields_required"));if(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(ie))if(le.length<6)je(b("password_min_length"));else{ue(!0),je(""),pe("");try{const e=(0,_.b)();if(!e)throw new Error("Database connection failed");const{data:{user:r}}=await e.auth.getUser();if(!r)throw new Error("Agent not authenticated");const s=await fetch(`${window.wpData.apiUrl}create-member`,{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":window.wpData.nonce},body:JSON.stringify({email:ie,password:le,invite_code:oe,agent_id:r.id})});if(!s.ok){const e=await s.json();throw new Error(e.message||"Failed to create member")}const n=await s.json();if(!n.success)throw new Error(n.message||"Failed to create member");pe(b("member_created_successfully")),setTimeout(()=>{ae(!1),de(""),ce(""),he(""),window.location.reload()},2e3)}catch(e){console.error("Error creating member:",e),je(e.message||b("member_creation_error"))}finally{ue(!1)}}else je(b("invalid_email_format"))},disabled:me||!ie||!le||!oe,children:me?(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),b("creating")]}):(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(j.OiG,{className:"me-1"}),b("create_member")]})})]})]})]})}}}]);
//# sourceMappingURL=138.066561aa.chunk.js.map