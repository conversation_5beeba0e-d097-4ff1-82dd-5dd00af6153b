"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[93],{1719:(e,a,s)=>{s.d(a,{A:()=>A});var r=s(8139),t=s.n(r),l=s(5043),n=s(1969),i=s(6618),o=s(7852),d=s(4488),c=s(579);const u=(0,d.A)("h4");u.displayName="DivStyledAsH4";const h=l.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:l=u,...n}=e;return r=(0,o.oU)(r,"alert-heading"),(0,c.jsx)(l,{ref:a,className:t()(s,r),...n})});h.displayName="AlertHeading";const m=h;var f=s(7071);const x=l.forwardRef((e,a)=>{let{className:s,bsPrefix:r,as:l=f.A,...n}=e;return r=(0,o.oU)(r,"alert-link"),(0,c.jsx)(l,{ref:a,className:t()(s,r),...n})});x.displayName="AlertLink";const g=x;var p=s(8072),v=s(5632);const w=l.forwardRef((e,a)=>{const{bsPrefix:s,show:r=!0,closeLabel:l="Close alert",closeVariant:d,className:u,children:h,variant:m="primary",onClose:f,dismissible:x,transition:g=p.A,...w}=(0,n.Zw)(e,{show:"onClose"}),A=(0,o.oU)(s,"alert"),b=(0,i.A)(e=>{f&&f(!1,e)}),j=!0===g?p.A:g,N=(0,c.jsxs)("div",{role:"alert",...j?void 0:w,ref:a,className:t()(u,A,m&&`${A}-${m}`,x&&`${A}-dismissible`),children:[x&&(0,c.jsx)(v.A,{onClick:b,"aria-label":l,variant:d}),h]});return j?(0,c.jsx)(j,{unmountOnExit:!0,...w,ref:void 0,in:r,children:N}):r?N:null});w.displayName="Alert";const A=Object.assign(w,{Link:g,Heading:m})},9093:(e,a,s)=>{s.r(a),s.d(a,{default:()=>h});var r=s(5043),t=s(8628),l=s(1719),n=s(3722),i=s(4282),o=s(4117),d=s(4312),c=s(1283),u=s(579);const h=()=>{const{t:e}=(0,o.Bd)(),[a,s]=(0,r.useState)(""),[h,m]=(0,r.useState)(""),[f,x]=(0,r.useState)(""),[g,p]=(0,r.useState)(!1),v=(0,c.Zp)();return(0,u.jsx)("div",{className:"d-flex justify-content-center align-items-center vh-100",children:(0,u.jsx)(t.A,{style:{width:"400px"},children:(0,u.jsxs)(t.A.Body,{children:[(0,u.jsx)(t.A.Title,{className:"text-center mb-4",children:e("login")}),f&&(0,u.jsx)(l.A,{variant:"danger",children:f}),(0,u.jsxs)(n.A,{onSubmit:async s=>{s.preventDefault(),x(""),p(!0);try{var r;const e=(0,d.b)(),{error:s}=await e.auth.signInWithPassword({email:a,password:h});if(s)throw s;const{data:{user:t},error:l}=await e.auth.getUser();if(l)throw l;let n=null===t||void 0===t||null===(r=t.user_metadata)||void 0===r?void 0:r.role;if(!n){const{data:a,error:s}=await e.from("users").select("role").eq("id",t.id).single();if(s)throw s;n=a.role}switch(localStorage.setItem("user_role",n),window.dispatchEvent(new Event("roleUpdated")),n){case"maker":v("/maker",{replace:!0});break;case"agent":v("/agent",{replace:!0});break;default:v("/",{replace:!0})}}catch(t){console.error("Login Error:",t),x(t.message||e("login_failed"))}p(!1)},children:[(0,u.jsxs)(n.A.Group,{className:"mb-3",children:[(0,u.jsx)(n.A.Label,{children:e("email_address")}),(0,u.jsx)(n.A.Control,{type:"email",value:a,onChange:e=>s(e.target.value),required:!0})]}),(0,u.jsxs)(n.A.Group,{className:"mb-3",children:[(0,u.jsx)(n.A.Label,{children:e("password")}),(0,u.jsx)(n.A.Control,{type:"password",value:h,onChange:e=>m(e.target.value),required:!0})]}),(0,u.jsx)(i.A,{type:"submit",className:"w-100",disabled:g,children:e(g?"logging_in":"login")})]})]})})})}}}]);
//# sourceMappingURL=93.c49990f5.chunk.js.map