{"version": 3, "file": "static/js/254.b965d2af.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,sFCjCA,MAAMC,EAAqBzB,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRC,EAAS,QACTsB,EAAO,SACPC,EAAQ,WACRC,EAAU,MACVC,EAAK,KACLC,EAAI,QACJC,EAAO,WACPC,KACGzB,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,SACjDY,EAAUQ,IAAWnB,EAAWI,EAAmBuB,GAAW,GAAGvB,KAAqBuB,IAAWD,GAAQ,GAAGtB,KAAqBsB,IAAQJ,GAAW,GAAGlB,KAAwC,kBAAZkB,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGnB,aAA8BoB,GAAc,GAAGpB,eAAgCqB,GAAS,GAAGrB,WACxVyB,GAAqBX,EAAAA,EAAAA,KAAK,QAAS,IACpCf,EACHH,UAAWW,EACXb,IAAKA,IAEP,GAAI8B,EAAY,CACd,IAAIE,EAAkB,GAAG1B,eAIzB,MAH0B,kBAAfwB,IACTE,EAAkB,GAAGA,KAAmBF,MAEtBV,EAAAA,EAAAA,KAAK,MAAO,CAC9BlB,UAAW8B,EACXC,SAAUF,GAEd,CACA,OAAOA,IAETR,EAAMD,YAAc,QACpB,S,4IChCA,MA0FA,EA1F6BY,KACzB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GAkCvC,OAhCAG,EAAAA,EAAAA,WAAU,KACgBC,WAClB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAM,KAAEK,EAAI,MAAEC,SAAgBH,EACzBI,KAAK,YACLC,OAAO,2RAUPC,GAAG,eAAe,GAClBC,MAAM,aAAc,CAAEC,WAAW,IAElCL,EACAM,QAAQN,MAAM,2BAA4BA,GAE1CT,EAAYQ,GAEhBL,GAAW,IAGfa,IACD,IAECd,GACOpB,EAAAA,EAAAA,KAAA,OAAAa,SAAME,EAAE,uBAIfoB,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAvB,SAAA,EACNb,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAM+B,SAAEE,EAAE,uBACxBf,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAAoC,UACAb,EAAAA,EAAAA,KAACqC,EAAAA,EAAG,CAAAxB,UACAb,EAAAA,EAAAA,KAACsC,EAAAA,EAAI,CAAAzB,UACDb,EAAAA,EAAAA,KAACsC,EAAAA,EAAKC,KAAI,CAAA1B,UACNsB,EAAAA,EAAAA,MAAChC,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACE,OAAK,EAACG,YAAU,EAAAG,SAAA,EACpCb,EAAAA,EAAAA,KAAA,SAAAa,UACIsB,EAAAA,EAAAA,MAAA,MAAAtB,SAAA,EACIb,EAAAA,EAAAA,KAAA,MAAAa,SAAKE,EAAE,iBACPf,EAAAA,EAAAA,KAAA,MAAAa,SAAKE,EAAE,mBACPf,EAAAA,EAAAA,KAAA,MAAAa,SAAKE,EAAE,eACPf,EAAAA,EAAAA,KAAA,MAAAa,SAAKE,EAAE,YACPf,EAAAA,EAAAA,KAAA,MAAAa,SAAKE,EAAE,mBACPf,EAAAA,EAAAA,KAAA,MAAAa,SAAKE,EAAE,kBACPf,EAAAA,EAAAA,KAAA,MAAAa,SAAKE,EAAE,uBACPf,EAAAA,EAAAA,KAAA,MAAAa,SAAKE,EAAE,iBAGff,EAAAA,EAAAA,KAAA,SAAAa,SACyB,IAApBI,EAASuB,QACNxC,EAAAA,EAAAA,KAAA,MAAAa,UACIb,EAAAA,EAAAA,KAAA,MAAIyC,QAAQ,IAAI3D,UAAU,cAAa+B,SAAEE,EAAE,mBAG/CE,EAASyB,IAAIC,IAAO,IAAAC,EAAA,OAChBT,EAAAA,EAAAA,MAAA,MAAAtB,SAAA,EACIsB,EAAAA,EAAAA,MAAA,MAAAtB,SAAA,CAAK8B,EAAQE,GAAGC,UAAU,EAAG,GAAG,UAChC9C,EAAAA,EAAAA,KAAA,MAAAa,SAAK8B,EAAQI,QACb/C,EAAAA,EAAAA,KAAA,MAAAa,SAAK8B,EAAQK,YACbhD,EAAAA,EAAAA,KAAA,MAAAa,SAAK8B,EAAQM,SACbjD,EAAAA,EAAAA,KAAA,MAAAa,SAAK8B,EAAQO,gBACblD,EAAAA,EAAAA,KAAA,MAAAa,SAAK8B,EAAQQ,eACbnD,EAAAA,EAAAA,KAAA,MAAAa,SAAK8B,EAAQO,aAAeP,EAAQQ,eACpCnD,EAAAA,EAAAA,KAAA,MAAAa,UAA2B,QAAtB+B,EAAAD,EAAQS,sBAAc,IAAAR,OAAA,EAAtBA,EAAwBS,SAAU,UARlCV,EAAQE,sB,sFC5B7D,MAAMR,EAAmB3D,EAAAA,WAEzB,CAACO,EAAOL,KACN,OAAO,UACLE,KACGwE,IAEHvE,GAAIC,EAAY,MAAK,SACrBH,EAAQ,MACR0E,IAjDG,SAAe5E,GAKnB,IALoB,GACrBI,EAAE,SACFF,EAAQ,UACRC,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,OACxC,MAAMO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBgE,EAAQ,GACR9D,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAI6D,EACAC,EACA1B,SAHG9C,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjC4D,OACAC,SACA1B,SACEnC,GAEJ4D,EAAO5D,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxD6D,GAAMD,EAAMxD,MAAc,IAATyD,EAAgB,GAAG3E,IAAWiB,IAAU,GAAGjB,IAAWiB,KAAS0D,KACvE,MAATzB,GAAetC,EAAQM,KAAK,QAAQD,KAASiC,KACnC,MAAV0B,GAAgBhE,EAAQM,KAAK,SAASD,KAAS2D,OAE9C,CAAC,IACHxE,EACHH,UAAWmB,IAAWnB,KAAcyE,KAAU9D,IAC7C,CACDV,KACAF,WACA0E,SAEJ,CAWOG,CAAOzE,GACZ,OAAoBe,EAAAA,EAAAA,KAAKhB,EAAW,IAC/BsE,EACH1E,IAAKA,EACLE,UAAWmB,IAAWnB,GAAYyE,EAAMf,QAAU3D,OAGtDwD,EAAInC,YAAc,MAClB,S,sFC1DA,MAAMyD,EAAwBjF,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP0E,EAASzD,YAAc,WACvB,UCdM0D,EAA0BlF,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,gBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP2E,EAAW1D,YAAc,aACzB,U,cCZA,MAAM2D,EAA0BnF,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMmF,GAAS3E,EAAAA,EAAAA,IAAmBN,EAAU,eACtCkF,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBH,IAClB,CAACA,IACL,OAAoB9D,EAAAA,EAAAA,KAAKkE,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACPlD,UAAuBb,EAAAA,EAAAA,KAAKhB,EAAW,CACrCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWgF,SAIvCD,EAAW3D,YAAc,aACzB,UCvBMmE,EAAuB3F,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRC,EAAS,QACT2B,EACA1B,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMmF,GAAS3E,EAAAA,EAAAA,IAAmBN,EAAU,YAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWQ,EAAU,GAAGqD,KAAUrD,IAAYqD,EAAQhF,MAC9DG,MAGPoF,EAAQnE,YAAc,UACtB,UCjBMoE,EAA8B5F,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,qBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPqF,EAAepE,YAAc,iBAC7B,UCdMqE,EAAwB7F,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPsF,EAASrE,YAAc,WACvB,U,cCbA,MAAMsE,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4BhG,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAYwF,KACbvF,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPyF,EAAaxE,YAAc,eAC3B,UChBMyE,EAAwBjG,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP0F,EAASzE,YAAc,WACvB,UCbM0E,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyBnG,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAY4F,KACb3F,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP4F,EAAU3E,YAAc,YACxB,UCPMoC,EAAoB5D,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRC,EAAS,GACTgG,EAAE,KACFC,EAAI,OACJC,EAAM,KACNC,GAAO,EAAK,SACZpE,EAEA9B,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMmF,GAAS3E,EAAAA,EAAAA,IAAmBN,EAAU,QAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWgF,EAAQgB,GAAM,MAAMA,IAAMC,GAAQ,QAAQA,IAAQC,GAAU,UAAUA,KACvGnE,SAAUoE,GAAoBjF,EAAAA,EAAAA,KAAK2D,EAAU,CAC3C9C,SAAUA,IACPA,MAGTyB,EAAKpC,YAAc,OACnB,QAAegF,OAAOC,OAAO7C,EAAM,CACjC8C,IAAKf,EACLgB,MAAOR,EACPS,SAAUZ,EACVnC,KAAMoB,EACN4B,KAAMhB,EACNiB,KAAMb,EACNc,OAAQ5B,EACR6B,OAAQ9B,EACR+B,WAAYrB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Table.js", "pages/agent/AgentProductListPage.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst AgentProductListPage = () => {\n    const { t } = useTranslation();\n    const [products, setProducts] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchProducts = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data, error } = await supabase\n                .from('products')\n                .select(`\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    is_disabled,\n                    maker_profiles ( domain )\n                `)\n                .eq('is_disabled', false) // Only show active products\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching products:', error);\n            } else {\n                setProducts(data);\n            }\n            setLoading(false);\n        };\n\n        fetchProducts();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_products')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('products_on_sale')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('product_id')}</th>\n                                        <th>{t('product_name')}</th>\n                                        <th>{t('category')}</th>\n                                        <th>{t('price')}</th>\n                                        <th>{t('total_shares')}</th>\n                                        <th>{t('sold_shares')}</th>\n                                        <th>{t('remaining_shares')}</th>\n                                        <th>{t('maker')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {products.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"8\" className=\"text-center\">{t('no_products')}</td>\n                                        </tr>\n                                    ) : (\n                                        products.map(product => (\n                                            <tr key={product.id}>\n                                                <td>{product.id.substring(0, 8)}...</td>\n                                                <td>{product.name}</td>\n                                                <td>{product.category}</td>\n                                                <td>{product.price}</td>\n                                                <td>{product.total_shares}</td>\n                                                <td>{product.sold_shares}</td>\n                                                <td>{product.total_shares - product.sold_shares}</td>\n                                                <td>{product.maker_profiles?.domain || 'N/A'}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default AgentProductListPage;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "Table", "striped", "bordered", "borderless", "hover", "size", "variant", "responsive", "table", "responsiveClass", "children", "AgentProductListPage", "t", "useTranslation", "products", "setProducts", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "error", "from", "select", "eq", "order", "ascending", "console", "fetchProducts", "_jsxs", "Container", "Col", "Card", "Body", "length", "colSpan", "map", "product", "_product$maker_profil", "id", "substring", "name", "category", "price", "total_shares", "sold_shares", "maker_profiles", "domain", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}