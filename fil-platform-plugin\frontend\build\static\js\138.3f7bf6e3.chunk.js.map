{"version": 3, "file": "static/js/138.3f7bf6e3.chunk.js", "mappings": "oSAMA,MA0uBA,EA1uBgBA,KAAO,IAADC,EAAAC,EAAAC,EAClB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAASC,IAAcC,EAAAA,EAAAA,UAAS,KAChCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,KACtCK,EAAcC,IAAmBN,EAAAA,EAAAA,UAAS,KAC1CO,EAAWC,IAAgBR,EAAAA,EAAAA,UAAS,KACpCS,EAASC,IAAcV,EAAAA,EAAAA,UAAS,KAChCW,EAAiBC,IAAsBZ,EAAAA,EAAAA,UAAS,KAChDa,EAAcC,IAAmBd,EAAAA,EAAAA,WAAS,IAC1Ce,EAAgBC,IAAqBhB,EAAAA,EAAAA,UAAS,OAC9CiB,EAAYC,IAAiBlB,EAAAA,EAAAA,WAAS,IACtCmB,EAAUC,IAAepB,EAAAA,EAAAA,UAAS,KAClCqB,EAAYC,IAAiBtB,EAAAA,EAAAA,UAAS,KAGtCuB,EAAsBC,IAA2BxB,EAAAA,EAAAA,WAAS,IAC1DyB,EAAmBC,IAAwB1B,EAAAA,EAAAA,UAAS,OACpD2B,EAAiBC,IAAsB5B,EAAAA,EAAAA,UAAS,KAChD6B,EAAkBC,IAAuB9B,EAAAA,EAAAA,UAAS,KAClD+B,EAAoBC,IAAyBhC,EAAAA,EAAAA,WAAS,IACtDiC,GAAkBC,KAAuBlC,EAAAA,EAAAA,UAAS,KAClDmC,GAAoBC,KAAyBpC,EAAAA,EAAAA,UAAS,KAE7DqC,EAAAA,EAAAA,WAAU,KACmBC,WACjB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfrC,GAAW,GACX,MAAQuC,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAxC,GAAW,GAKf,MAAQuC,KAAMI,EAAWC,MAAOC,SAAuBR,EAClDS,KAAK,qBACLC,OAAO,2EACPC,GAAG,WAAYR,EAAKS,IACpBC,MAAM,aAAc,CAAEC,WAAW,IAEtC,GAAIN,IAAiBF,EAGjB,OAFAS,QAAQR,MAAM,oCAAqCC,QACnD7C,GAAW,GAKC2C,EAAUU,IAAIC,GAAKA,EAAEC,SAASC,OAAOC,SAArD,MAEQlB,KAAMmB,EAAcd,MAAOe,SAAoBtB,EAClDS,KAAK,SACLC,OAAO,yBAERY,GACAP,QAAQR,MAAM,wBAAyBe,GAI3C,MAAMC,EAAW,IAAIC,KAAKH,GAAgB,IAAIL,IAAIS,GAAK,CAACA,EAAEb,GAAIa,KAExDC,EAAkBpB,EAAUU,IAAIC,IAAC,IAChCA,EACHU,MAAOJ,EAASK,IAAIX,EAAEC,UAAY,CAAC,KAGvC1D,EAAWkE,GACX/D,GAAW,IAGnBkE,IACD,KAGH/B,EAAAA,EAAAA,WAAU,KACN,IAAIgC,EAAWvE,EAGXK,IACAkE,EAAWA,EAASX,OAAOY,IAAM,IAAAC,EAAAC,EAAAC,EAAA,OACjB,QAAZF,EAAAD,EAAOJ,aAAK,IAAAK,GAAO,QAAPC,EAAZD,EAAcG,aAAK,IAAAF,OAAP,EAAZA,EAAqBG,cAAcC,SAASzE,EAAWwE,kBACvC,QADqDF,EACrEH,EAAOO,iBAAS,IAAAJ,OAAA,EAAhBA,EAAkBE,cAAcC,SAASzE,EAAWwE,mBAKxDtE,IACAgE,EAAWA,EAASX,OAAOY,GAAUA,EAAOQ,gBAAkBzE,IAI9DE,IACA8D,EAAWA,EAASX,OAAOY,IAAM,IAAAS,EAAA,OAC7B,IAAIC,KAAiB,QAAbD,EAACT,EAAOJ,aAAK,IAAAa,OAAA,EAAZA,EAAcE,aAAe,IAAID,KAAKzE,MAGnDE,IACA4D,EAAWA,EAASX,OAAOY,IAAM,IAAAY,EAAA,OAC7B,IAAIF,KAAiB,QAAbE,EAACZ,EAAOJ,aAAK,IAAAgB,OAAA,EAAZA,EAAcD,aAAe,IAAID,KAAKvE,MAIvDG,EAAmByD,IACpB,CAACvE,EAASK,EAAYE,EAAcE,EAAWE,IAGlD,MAiCM0E,GAAkBC,IACpB,OAAQA,GACJ,IAAK,WACD,OAAOC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,GAAG,UAASC,SAAE5F,EAAE,cAClC,IAAK,UACD,OAAOyF,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,GAAG,UAASC,SAAE5F,EAAE,oBAClC,IAAK,WACD,OAAOyF,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,GAAG,SAAQC,SAAE5F,EAAE,cACjC,IAAK,eACD,OAAOyF,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,GAAG,OAAMC,SAAE5F,EAAE,kBAC/B,QACI,OAAOyF,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,GAAG,YAAWC,SAAEJ,GAAUxF,EAAE,qBAqBhD6F,GAAoBnD,UACtB,GAAKvB,EAAL,CAEAG,GAAc,GACdE,EAAY,IACZE,EAAc,IAEd,IACI,MAAMiB,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EACD,MAAM,IAAImD,MAAM,8BAGpB,MAAM,KAAEjD,EAAI,MAAEK,SAAgBP,EACzBS,KAAK,qBACL2C,OAAO,CAAEb,cAAec,IACxB1C,GAAG,UAAWnC,EAAe0C,SAC7BR,SAEL,GAAIH,EAEA,MADAQ,QAAQR,MAAM,kBAAmBA,GAC3BA,EAIV,IAAKL,GAAwB,IAAhBA,EAAKoD,OAAc,CAG5B,MAAQpD,KAAMqD,EAAgBhD,MAAOiD,SAAsBxD,EACtDS,KAAK,qBACLC,OAAO,KACPC,GAAG,UAAWnC,EAAe0C,SAElC,GAAIsC,EAEA,MADAzC,QAAQR,MAAM,kCAAmCiD,GAC3CA,EAGV,IAAKD,GAA4C,IAA1BA,EAAeD,OAClC,MAAM,IAAIH,MAAM,6BAExB,CAGA3F,EAAWiG,GACPA,EAAYzC,IAAIe,GACZA,EAAOb,UAAY1C,EAAe0C,QAC5B,IAAKa,EAAQQ,cAAec,GAC5BtB,IAKdtD,EAAkBiF,IAAI,IAAUA,EAAMnB,cAAec,KAErDtE,EAAwC1B,EAAb,aAAbgG,EAA4B,uBAA4B,yBAGtEM,WAAW,KACPpF,GAAgB,GAChBE,EAAkB,OACnB,KAEP,CAAE,MAAO8B,GACLQ,QAAQR,MAAM,6BAA8BA,GAC5C1B,EAAY0B,EAAMqD,SAAWvG,EAAE,oBACnC,CAAC,QACGsB,GAAc,EAClB,CAnE2B,GAsEzBkF,GAAgBA,KAClBtF,GAAgB,GAChBE,EAAkB,MAClBI,EAAY,IACZE,EAAc,KAGZ+E,GAAoB/D,UACtBZ,EAAqB4C,GACrBpC,GAAoB,IACpBE,GAAsB,IACtBN,EAAoB,SAnJKQ,WACzB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,GAAKD,EAEL,IAEI,MAAQE,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAC/C,IAAKF,EAAM,OAGX,MAAQD,KAAM6D,EAAM,MAAExD,SAAgBP,EACjCS,KAAK,kBACLC,OAAO,qNAQPsD,IAAI,UAAW7D,EAAKS,IAEzB,GAAIL,EAEA,YADAQ,QAAQR,MAAM,yBAA0BA,GAI5ClB,EAAmB0E,GAAU,GACjC,CAAE,MAAOxD,GACLQ,QAAQR,MAAM,iCAAkCA,EACpD,GAwHM0D,GAENhF,GAAwB,IAyDtBiF,GAAwBA,KAC1BjF,GAAwB,GACxBE,EAAqB,MACrBI,EAAoB,IACpBI,GAAoB,IACpBE,GAAsB,KAI1B,OAAInC,GACOoF,EAAAA,EAAAA,KAAA,OAAAG,SAAM5F,EAAE,sBAIf8G,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAnB,SAAA,EACNH,EAAAA,EAAAA,KAAA,MAAIuB,UAAU,OAAMpB,SAAE5F,EAAE,kBAGxByF,EAAAA,EAAAA,KAACwB,EAAAA,EAAG,CAACD,UAAU,OAAMpB,UACjBH,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAAAtB,UACAH,EAAAA,EAAAA,KAAC0B,EAAAA,EAAI,CAAAvB,UACDH,EAAAA,EAAAA,KAAC0B,EAAAA,EAAKC,KAAI,CAAAxB,UACNkB,EAAAA,EAAAA,MAACG,EAAAA,EAAG,CAACD,UAAU,kBAAiBpB,SAAA,EAC5BH,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAACG,GAAI,EAAEzB,UACPkB,EAAAA,EAAAA,MAACQ,EAAAA,EAAM,CACHC,QAAQ,UACRC,QAtLZC,KAEpBC,MAAM1H,EAAE,4BAqLwBgH,UAAU,OAAMpB,SAAA,EAEhBH,EAAAA,EAAAA,KAACkC,EAAAA,IAAM,CAACX,UAAU,SACjBhH,EAAE,oBAGXyF,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAACG,GAAI,EAAEzB,UACPkB,EAAAA,EAAAA,MAACc,EAAAA,EAAKC,MAAK,CAAAjC,SAAA,EACPH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,SAAE5F,EAAE,sBACfyF,EAAAA,EAAAA,KAACsC,EAAAA,EAAU,CAAAnC,UACPH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKI,QAAO,CACTC,KAAK,OACLC,YAAalI,EAAE,yBACfmI,MAAO5H,EACP6H,SAAWC,GAAM7H,EAAc6H,EAAEC,OAAOH,iBAKxD1C,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAACG,GAAI,EAAEzB,UACPkB,EAAAA,EAAAA,MAACc,EAAAA,EAAKC,MAAK,CAAAjC,SAAA,EACPH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,SAAE5F,EAAE,oBACf8G,EAAAA,EAAAA,MAACc,EAAAA,EAAKW,OAAM,CACRJ,MAAO1H,EACP2H,SAAWC,GAAM3H,EAAgB2H,EAAEC,OAAOH,OAAOvC,SAAA,EAEjDH,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,GAAEvC,SAAE5F,EAAE,2BACpByF,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,UAASvC,SAAE5F,EAAE,qBAC3ByF,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,WAAUvC,SAAE5F,EAAE,eAC5ByF,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,WAAUvC,SAAE5F,EAAE,eAC5ByF,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,eAAcvC,SAAE5F,EAAE,2BAI5CyF,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAACG,GAAI,EAAEzB,UACPkB,EAAAA,EAAAA,MAACc,EAAAA,EAAKC,MAAK,CAAAjC,SAAA,EACPH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,SAAE5F,EAAE,iBACfyF,EAAAA,EAAAA,KAACmC,EAAAA,EAAKI,QAAO,CACTC,KAAK,OACLE,MAAOxH,EACPyH,SAAWC,GAAMzH,EAAayH,EAAEC,OAAOH,eAInD1C,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAACG,GAAI,EAAEzB,UACPkB,EAAAA,EAAAA,MAACc,EAAAA,EAAKC,MAAK,CAAAjC,SAAA,EACPH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,SAAE5F,EAAE,eACfyF,EAAAA,EAAAA,KAACmC,EAAAA,EAAKI,QAAO,CACTC,KAAK,OACLE,MAAOtH,EACPuH,SAAWC,GAAMvH,EAAWuH,EAAEC,OAAOH,eAIjD1C,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAACG,GAAI,EAAEzB,UACPH,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CACHC,QAAQ,kBACRC,QArPfgB,KAEjB9E,QAAQ+E,IAAI,qBAoPoBzB,UAAU,OAAMpB,UAEhBH,EAAAA,EAAAA,KAACiD,EAAAA,IAAQ,oBAUrCjD,EAAAA,EAAAA,KAACwB,EAAAA,EAAG,CAAArB,UACAH,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAAAtB,UACAH,EAAAA,EAAAA,KAAC0B,EAAAA,EAAI,CAAAvB,UACDH,EAAAA,EAAAA,KAAC0B,EAAAA,EAAKC,KAAI,CAAAxB,UACNkB,EAAAA,EAAAA,MAAC6B,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAAnD,SAAA,EACpCH,EAAAA,EAAAA,KAAA,SAAAG,UACIkB,EAAAA,EAAAA,MAAA,MAAAlB,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,SAAK5F,EAAE,eACPyF,EAAAA,EAAAA,KAAA,MAAAG,SAAK5F,EAAE,gBACPyF,EAAAA,EAAAA,KAAA,MAAAG,SAAK5F,EAAE,gBACPyF,EAAAA,EAAAA,KAAA,MAAAG,SAAK5F,EAAE,qBACPyF,EAAAA,EAAAA,KAAA,MAAAG,SAAK5F,EAAE,oBACPyF,EAAAA,EAAAA,KAAA,MAAAG,SAAK5F,EAAE,aACPyF,EAAAA,EAAAA,KAAA,MAAAG,SAAK5F,EAAE,wBACPyF,EAAAA,EAAAA,KAAA,MAAAG,SAAK5F,EAAE,mBAGfyF,EAAAA,EAAAA,KAAA,SAAAG,SACgC,IAA3B7E,EAAgBkF,QACbR,EAAAA,EAAAA,KAAA,MAAAG,UACIH,EAAAA,EAAAA,KAAA,MAAIuD,QAAQ,IAAIhC,UAAU,cAAapB,SAAE5F,EAAE,wBAG/Ce,EAAgB4C,IAAIe,IAAM,IAAAuE,EAAAC,EAAA,OACtBpC,EAAAA,EAAAA,MAAA,MAAAlB,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAiB,QAAZqD,EAAAvE,EAAOJ,aAAK,IAAA2E,OAAA,EAAZA,EAAcnE,QAAS,OAC5BW,EAAAA,EAAAA,KAAA,MAAAG,SAAKlB,EAAOO,WAAa,OACzBQ,EAAAA,EAAAA,KAAA,MAAAG,SAAKlB,EAAOyE,WAAa,OACzB1D,EAAAA,EAAAA,KAAA,MAAAG,SACKlB,EAAO0E,cACJ3D,EAAAA,EAAAA,KAAA,OACI4D,IAAK3E,EAAO0E,aACZE,IAAI,WACJC,MAAO,CACHC,MAAO,OACPC,OAAQ,OACRC,UAAW,QACXC,aAAc,MACdC,OAAQ,WAEZpC,QAASA,IAAMqC,OAAOC,KAAKpF,EAAO0E,aAAc,aAGpD3D,EAAAA,EAAAA,KAAA,QAAMuB,UAAU,aAAYpB,SAAC,SAGrCH,EAAAA,EAAAA,KAAA,MAAAG,SACKlB,EAAOqF,aACJtE,EAAAA,EAAAA,KAAA,OACI4D,IAAK3E,EAAOqF,YACZT,IAAI,UACJC,MAAO,CACHC,MAAO,OACPC,OAAQ,OACRC,UAAW,QACXC,aAAc,MACdC,OAAQ,WAEZpC,QAASA,IAAMqC,OAAOC,KAAKpF,EAAOqF,YAAa,aAGnDtE,EAAAA,EAAAA,KAAA,QAAMuB,UAAU,aAAYpB,SAAC,SAGrCH,EAAAA,EAAAA,KAAA,MAAAG,SAAKL,GAAeb,EAAOQ,kBAC3BO,EAAAA,EAAAA,KAAA,MAAAG,SAAiB,QAAZsD,EAAAxE,EAAOJ,aAAK,IAAA4E,GAAZA,EAAc7D,WAAa,IAAID,KAAKV,EAAOJ,MAAMe,YAAY2E,iBAAmB,OACrFvE,EAAAA,EAAAA,KAAA,MAAAG,UACIkB,EAAAA,EAAAA,MAAA,OAAKE,UAAU,iCAAgCpB,SAAA,EAC3CH,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CACH2C,KAAK,KACL1C,QAAQ,kBACRC,QAASA,IA/TxC9C,KACrBtD,EAAkBsD,GAClBxD,GAAgB,GAChBM,EAAY,IACZE,EAAc,KA2TqDwI,CAAgBxF,GAC/ByF,MAAOnK,EAAE,cAAc4F,UAEvBH,EAAAA,EAAAA,KAAC2E,EAAAA,IAAW,OAEhB3E,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CACH2C,KAAK,KACL1C,QAAQ,kBACRC,QAASA,IAAMf,GAAkB/B,GACjCyF,MAAOnK,EAAE,gBAAgB4F,UAEzBH,EAAAA,EAAAA,KAAC4E,EAAAA,IAAa,aA1DrB3F,EAAOb,yBAyEhDiD,EAAAA,EAAAA,MAACwD,EAAAA,EAAK,CAACC,KAAMtJ,EAAcuJ,OAAQhE,GAAeyD,KAAK,KAAIrE,SAAA,EACvDH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMG,OAAM,CAACC,aAAW,EAAC1D,UAAU,sBAAqBpB,UACrDH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMK,MAAK,CAAA/E,SAAE5F,EAAE,mBAEpByF,EAAAA,EAAAA,KAAA,SAAOmF,wBAAyB,CAChCC,OAAQ,g/BAiBRpF,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMlD,KAAI,CAAAxB,SACNzE,IACG2F,EAAAA,EAAAA,MAAAgE,EAAAA,SAAA,CAAAlF,SAAA,CACKrE,IACGkE,EAAAA,EAAAA,KAACsF,EAAAA,EAAK,CAACxD,QAAQ,SAASP,UAAU,OAAMpB,SACnCrE,IAGRE,IACGgE,EAAAA,EAAAA,KAACsF,EAAAA,EAAK,CAACxD,QAAQ,UAAUP,UAAU,OAAMpB,SACpCnE,KAITqF,EAAAA,EAAAA,MAACG,EAAAA,EAAG,CAAArB,SAAA,EACAH,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAACG,GAAI,EAAEzB,UACPkB,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACH,UAAU,OAAMpB,SAAA,EAClBH,EAAAA,EAAAA,KAAC0B,EAAAA,EAAKsD,OAAM,CAAA7E,UACRH,EAAAA,EAAAA,KAAA,UAAAG,SAAS5F,EAAE,sBAEf8G,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,KAAI,CAAAxB,SAAA,EACNkB,EAAAA,EAAAA,MAAA,KAAAlB,SAAA,EAAGkB,EAAAA,EAAAA,MAAA,UAAAlB,SAAA,CAAS5F,EAAE,YAAY,OAAU,KAAsB,QAApBH,EAAAsB,EAAemD,aAAK,IAAAzE,OAAA,EAApBA,EAAsBiF,QAAS,QACrEgC,EAAAA,EAAAA,MAAA,KAAAlB,SAAA,EAAGkB,EAAAA,EAAAA,MAAA,UAAAlB,SAAA,CAAS5F,EAAE,aAAa,OAAU,IAAEmB,EAAe8D,WAAa,QACnE6B,EAAAA,EAAAA,MAAA,KAAAlB,SAAA,EAAGkB,EAAAA,EAAAA,MAAA,UAAAlB,SAAA,CAAS5F,EAAE,aAAa,OAAU,IAAEmB,EAAegI,WAAa,QACnErC,EAAAA,EAAAA,MAAA,KAAAlB,SAAA,EAAGkB,EAAAA,EAAAA,MAAA,UAAAlB,SAAA,CAAS5F,EAAE,kBAAkB,OAAU,IAAEuF,GAAepE,EAAe+D,mBAC1E4B,EAAAA,EAAAA,MAAA,KAAAlB,SAAA,EAAGkB,EAAAA,EAAAA,MAAA,UAAAlB,SAAA,CAAS5F,EAAE,qBAAqB,OAAU,IAAsB,QAApBF,EAAAqB,EAAemD,aAAK,IAAAxE,GAApBA,EAAsBuF,WAAa,IAAID,KAAKjE,EAAemD,MAAMe,YAAY2E,iBAAmB,gBAI3JvE,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAACG,GAAI,EAAEzB,UACPkB,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACH,UAAU,OAAMpB,SAAA,EAClBH,EAAAA,EAAAA,KAAC0B,EAAAA,EAAKsD,OAAM,CAAA7E,UACRH,EAAAA,EAAAA,KAAA,UAAAG,SAAS5F,EAAE,qBAEf8G,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,KAAI,CAAAxB,SAAA,EACNkB,EAAAA,EAAAA,MAAA,OAAKE,UAAU,OAAMpB,SAAA,EACjBkB,EAAAA,EAAAA,MAAA,UAAAlB,SAAA,CAAS5F,EAAE,kBAAkB,QAC7ByF,EAAAA,EAAAA,KAAA,OAAKuB,UAAU,OAAMpB,SAChBzE,EAAeiI,cACZ3D,EAAAA,EAAAA,KAAA,OACI4D,IAAKlI,EAAeiI,aACpBE,IAAI,WACJC,MAAO,CACHC,MAAO,OACPwB,UAAW,QACXtB,UAAW,UACXC,aAAc,MACdC,OAAQ,UACRqB,OAAQ,qBAEZzD,QAASA,IAAMqC,OAAOC,KAAK3I,EAAeiI,aAAc,aAG5D3D,EAAAA,EAAAA,KAAA,OAAKuB,UAAU,8BAA8BuC,MAAO,CAAE0B,OAAQ,qBAAsBtB,aAAc,OAAQ/D,SACrG5F,EAAE,6BAKnB8G,EAAAA,EAAAA,MAAA,OAAKE,UAAU,OAAMpB,SAAA,EACjBkB,EAAAA,EAAAA,MAAA,UAAAlB,SAAA,CAAS5F,EAAE,iBAAiB,QAC5ByF,EAAAA,EAAAA,KAAA,OAAKuB,UAAU,OAAMpB,SAChBzE,EAAe4I,aACZtE,EAAAA,EAAAA,KAAA,OACI4D,IAAKlI,EAAe4I,YACpBT,IAAI,UACJC,MAAO,CACHC,MAAO,OACPwB,UAAW,QACXtB,UAAW,UACXC,aAAc,MACdC,OAAQ,UACRqB,OAAQ,qBAEZzD,QAASA,IAAMqC,OAAOC,KAAK3I,EAAe4I,YAAa,aAG3DtE,EAAAA,EAAAA,KAAA,OAAKuB,UAAU,8BAA8BuC,MAAO,CAAE0B,OAAQ,qBAAsBtB,aAAc,OAAQ/D,SACrG5F,EAAE,6CAY/C8G,EAAAA,EAAAA,MAACwD,EAAAA,EAAMY,OAAM,CAAAtF,SAAA,EACTH,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAAShB,GAAe2E,SAAU9J,EAAWuE,SACpE5F,EAAE,aAEPyF,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CACHC,QAAQ,SACRC,QAASA,IAAM3B,GAAkB,YACjCsF,SAAU9J,GAAgD,cAApB,OAAdF,QAAc,IAAdA,OAAc,EAAdA,EAAgB+D,eAA6BU,SAEpEvE,GACGyF,EAAAA,EAAAA,MAAAgE,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAA,QAAMuB,UAAU,wCAAwCoE,KAAK,SAAS,cAAY,SACjFpL,EAAE,kBAGP8G,EAAAA,EAAAA,MAAAgE,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAC4F,EAAAA,IAAO,CAACrE,UAAU,SAClBhH,EAAE,gBAIfyF,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CACHC,QAAQ,UACRC,QAASA,IAAM3B,GAAkB,YACjCsF,SAAU9J,GAAgD,cAApB,OAAdF,QAAc,IAAdA,OAAc,EAAdA,EAAgB+D,eAA6BU,SAEpEvE,GACGyF,EAAAA,EAAAA,MAAAgE,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAA,QAAMuB,UAAU,wCAAwCoE,KAAK,SAAS,cAAY,SACjFpL,EAAE,kBAGP8G,EAAAA,EAAAA,MAAAgE,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAC6F,EAAAA,IAAO,CAACtE,UAAU,SAClBhH,EAAE,uBAQvB8G,EAAAA,EAAAA,MAACwD,EAAAA,EAAK,CAACC,KAAM5I,EAAsB6I,OAAQ3D,GAAuBoD,KAAK,KAAIrE,SAAA,EACvEH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMG,OAAM,CAACC,aAAW,EAAA9E,UACrBH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMK,MAAK,CAAA/E,SAAE5F,EAAE,qBAEpB8G,EAAAA,EAAAA,MAACwD,EAAAA,EAAMlD,KAAI,CAAAxB,SAAA,CACNvD,KACGoD,EAAAA,EAAAA,KAACsF,EAAAA,EAAK,CAACxD,QAAQ,SAASP,UAAU,OAAMpB,SACnCvD,KAGRE,KACGkD,EAAAA,EAAAA,KAACsF,EAAAA,EAAK,CAACxD,QAAQ,UAAUP,UAAU,OAAMpB,SACpCrD,KAIRV,IACG4D,EAAAA,EAAAA,KAAA,OAAKuB,UAAU,OAAMpB,UACjBkB,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAAAvB,SAAA,EACDH,EAAAA,EAAAA,KAAC0B,EAAAA,EAAKsD,OAAM,CAAA7E,UACRH,EAAAA,EAAAA,KAAA,UAAAG,SAAS5F,EAAE,sBAEf8G,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,KAAI,CAAAxB,SAAA,EACNkB,EAAAA,EAAAA,MAAA,KAAAlB,SAAA,EAAGkB,EAAAA,EAAAA,MAAA,UAAAlB,SAAA,CAAS5F,EAAE,YAAY,OAAU,KAAyB,QAAvBD,EAAA8B,EAAkByC,aAAK,IAAAvE,OAAA,EAAvBA,EAAyB+E,QAAS,QACxEgC,EAAAA,EAAAA,MAAA,KAAAlB,SAAA,EAAGkB,EAAAA,EAAAA,MAAA,UAAAlB,SAAA,CAAS5F,EAAE,aAAa,OAAU,IAAE6B,EAAkBoD,WAAa,QACtE6B,EAAAA,EAAAA,MAAA,KAAAlB,SAAA,EAAGkB,EAAAA,EAAAA,MAAA,UAAAlB,SAAA,CAAS5F,EAAE,kBAAkB,OAAU,IAAEuF,GAAe1D,EAAkBqD,2BAM7F4B,EAAAA,EAAAA,MAACc,EAAAA,EAAKC,MAAK,CAACb,UAAU,OAAMpB,SAAA,EACxBH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,UAACH,EAAAA,EAAAA,KAAA,UAAAG,SAAS5F,EAAE,yBACvB8G,EAAAA,EAAAA,MAACc,EAAAA,EAAKW,OAAM,CACRJ,MAAOlG,EACPmG,SAAWC,GAAMnG,EAAoBmG,EAAEC,OAAOH,OAC9CgD,SAAUhJ,EAAmByD,SAAA,EAE7BH,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,GAAEvC,SAAE5F,EAAE,yBACnB+B,EAAgB4B,IAAI4H,IAAK,IAAAC,EAAA,OACtB1E,EAAAA,EAAAA,MAAA,UAA4BqB,MAAOoD,EAAM1H,QAAQ+B,SAAA,CAC5C2F,EAAME,aAAyB,QAAfD,EAAID,EAAMjH,aAAK,IAAAkH,OAAA,EAAXA,EAAa1G,QAASyG,EAAM1H,QAChD0H,EAAMG,gBAAkB,KAAKH,EAAMG,qBAF3BH,EAAM1H,cAMC,IAA3B9B,EAAgBkE,SACbR,EAAAA,EAAAA,KAACmC,EAAAA,EAAK+D,KAAI,CAAC3E,UAAU,aAAYpB,SAC5B5F,EAAE,gCAKnB8G,EAAAA,EAAAA,MAACwD,EAAAA,EAAMY,OAAM,CAAAtF,SAAA,EACTH,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAASX,GAAuBsE,SAAUhJ,EAAmByD,SACpF5F,EAAE,aAEPyF,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CACHC,QAAQ,UACRC,QA7ca9E,UAC7B,GAAKb,GAAsBI,EAA3B,CAKAG,GAAsB,GACtBE,GAAoB,IACpBE,GAAsB,IAEtB,IACI,MAAMG,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EACD,MAAM,IAAImD,MAAM,8BAIpB,MAAM,KAAEjD,EAAI,MAAEK,SAAgBP,EACzBS,KAAK,qBACL2C,OAAO,CAAE6F,SAAU3J,IACnBqB,GAAG,UAAWzB,EAAkBgC,SAChCR,SAEL,GAAIH,EAEA,MADAQ,QAAQR,MAAM,kBAAmBA,GAC3BA,EAGV,IAAKL,GAAwB,IAAhBA,EAAKoD,OACd,MAAM,IAAIH,MAAM,qCAIpB3F,EAAWiG,GACPA,EAAYtC,OAAOY,GAAUA,EAAOb,UAAYhC,EAAkBgC,UAGtErB,GAAsBxC,EAAE,+BAGxBsG,WAAW,KACP1E,GAAwB,GACxBE,EAAqB,MACrBI,EAAoB,KACrB,KAEP,CAAE,MAAOgB,GACLQ,QAAQR,MAAM,wBAAyBA,GACvCZ,GAAoBY,EAAMqD,SAAWvG,EAAE,sBAC3C,CAAC,QACGoC,GAAsB,EAC1B,CA/CA,MAFIE,GAAoBtC,EAAE,yBA4cVmL,SAAUhJ,IAAuBF,GAA+C,IAA3BF,EAAgBkE,OAAaL,SAEjFzD,GACG2E,EAAAA,EAAAA,MAAAgE,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAA,QAAMuB,UAAU,wCAAwCoE,KAAK,SAAS,cAAY,SACjFpL,EAAE,kBAGP8G,EAAAA,EAAAA,MAAAgE,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAC4E,EAAAA,IAAa,CAACrD,UAAU,SACxBhH,EAAE,gC", "sources": ["pages/agent/Members.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge, Button, Form, InputGroup, Dropdown, Modal, Alert } from 'react-bootstrap';\nimport { FaSearch, FaPlus, FaUserCheck, FaExchangeAlt, FaCheck, FaTimes } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Members = () => {\n    const { t } = useTranslation();\n    const [members, setMembers] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [statusFilter, setStatusFilter] = useState('');\n    const [startDate, setStartDate] = useState('');\n    const [endDate, setEndDate] = useState('');\n    const [filteredMembers, setFilteredMembers] = useState([]);\n    const [showKycModal, setShowKycModal] = useState(false);\n    const [selectedMember, setSelectedMember] = useState(null);\n    const [kycLoading, setKycLoading] = useState(false);\n    const [kycError, setKycError] = useState('');\n    const [kycSuccess, setKycSuccess] = useState('');\n\n    // Change Agent Modal states\n    const [showChangeAgentModal, setShowChangeAgentModal] = useState(false);\n    const [changeAgentMember, setChangeAgentMember] = useState(null);\n    const [availableAgents, setAvailableAgents] = useState([]);\n    const [selectedNewAgent, setSelectedNewAgent] = useState('');\n    const [changeAgentLoading, setChangeAgentLoading] = useState(false);\n    const [changeAgentError, setChangeAgentError] = useState('');\n    const [changeAgentSuccess, setChangeAgentSuccess] = useState('');\n\n    useEffect(() => {\n            const fetchMembers = async () => {\n                const supabase = getSupabase();\n                if (!supabase) return;\n    \n                setLoading(true);\n                const { data: { user } } = await supabase.auth.getUser();\n    \n                if (!user) {\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 1: 查询 customer_profiles\n                const { data: customers, error: profileError } = await supabase\n                    .from('customer_profiles')\n                    .select('user_id, real_name, id_number, id_img_front, id_img_back, verify_status')\n                    .eq('agent_id', user.id)\n                    .order('created_at', { ascending: false });\n    \n                if (profileError || !customers) {\n                    console.error('Error fetching customer_profiles:', profileError);\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 2: 查询 users 表\n                const userIds = customers.map(c => c.user_id).filter(Boolean);\n    \n                const { data: userInfoList, error: userError } = await supabase\n                    .from('users')\n                    .select('id, email, created_at')\n\n                if (userError) {\n                    console.error('Error fetching users:', userError);\n                }\n    \n                // Step 3: 合并结果\n                const usersMap = new Map((userInfoList || []).map(u => [u.id, u]));\n    \n                const enrichedMembers = customers.map(c => ({\n                    ...c,\n                    users: usersMap.get(c.user_id) || {}\n                }));\n    \n                setMembers(enrichedMembers);\n                setLoading(false);\n            };\n\n        fetchMembers();\n    }, []);\n\n    // Filter members based on search criteria\n    useEffect(() => {\n        let filtered = members;\n\n        // Search by username (email)\n        if (searchTerm) {\n            filtered = filtered.filter(member =>\n                member.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                member.real_name?.toLowerCase().includes(searchTerm.toLowerCase())\n            );\n        }\n\n        // Filter by status\n        if (statusFilter) {\n            filtered = filtered.filter(member => member.verify_status === statusFilter);\n        }\n\n        // Filter by date range\n        if (startDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) >= new Date(startDate)\n            );\n        }\n        if (endDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) <= new Date(endDate)\n            );\n        }\n\n        setFilteredMembers(filtered);\n    }, [members, searchTerm, statusFilter, startDate, endDate]);\n\n    // Fetch available agents for change agent modal\n    const fetchAvailableAgents = async () => {\n        const supabase = getSupabase();\n        if (!supabase) return;\n\n        try {\n            // Get current user (should be an agent)\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) return;\n\n            // Query agent_profiles and join with users to get email\n            const { data: agents, error } = await supabase\n                .from('agent_profiles')\n                .select(`\n                    user_id,\n                    brand_name,\n                    commission_pct,\n                    users:user_id (\n                        email\n                    )\n                `)\n                .neq('user_id', user.id); // Exclude current agent\n\n            if (error) {\n                console.error('Error fetching agents:', error);\n                return;\n            }\n\n            setAvailableAgents(agents || []);\n        } catch (error) {\n            console.error('Error in fetchAvailableAgents:', error);\n        }\n    };\n\n    const getStatusBadge = (status) => {\n        switch (status) {\n            case 'approved':\n                return <Badge bg=\"success\">{t('approved')}</Badge>;\n            case 'pending':\n                return <Badge bg=\"warning\">{t('pending_review')}</Badge>;\n            case 'rejected':\n                return <Badge bg=\"danger\">{t('rejected')}</Badge>;\n            case 'under_review':\n                return <Badge bg=\"info\">{t('under_review')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{status || t('not_submitted')}</Badge>;\n        }\n    };\n\n    const handleSearch = () => {\n        // Search is handled by useEffect, this function can be used for additional logic if needed\n        console.log('Search triggered');\n    };\n\n    const handleAddMember = () => {\n        // TODO: Implement add member functionality\n        alert(t('add_member_coming_soon'));\n    };\n\n    const handleKycReview = (member) => {\n        setSelectedMember(member);\n        setShowKycModal(true);\n        setKycError('');\n        setKycSuccess('');\n    };\n\n    const handleKycDecision = async (decision) => {\n        if (!selectedMember) return;\n\n        setKycLoading(true);\n        setKycError('');\n        setKycSuccess('');\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error('Database connection failed');\n            }\n\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .update({ verify_status: decision })\n                .eq('user_id', selectedMember.user_id)\n                .select();\n\n            if (error) {\n                console.error('Database error:', error);\n                throw error;\n            }\n\n            // Check if any rows were updated\n            if (!data || data.length === 0) {\n                \n                // Try to find the record\n                const { data: existingRecord, error: selectError } = await supabase\n                    .from('customer_profiles')\n                    .select('*')\n                    .eq('user_id', selectedMember.user_id);\n\n                if (selectError) {\n                    console.error('Error checking existing record:', selectError);\n                    throw selectError;\n                }\n                \n                if (!existingRecord || existingRecord.length === 0) {\n                    throw new Error('Customer profile not found');\n                }\n            }\n\n            // Update local state\n            setMembers(prevMembers => \n                prevMembers.map(member => \n                    member.user_id === selectedMember.user_id \n                        ? { ...member, verify_status: decision }\n                        : member\n                )\n            );\n\n            // Also update the selected member\n            setSelectedMember(prev => ({ ...prev, verify_status: decision }));\n\n            setKycSuccess(decision === 'approved' ? t('kyc_approved_success') : t('kyc_rejected_success'));\n            \n            // Close modal after 1.5 seconds\n            setTimeout(() => {\n                setShowKycModal(false);\n                setSelectedMember(null);\n            }, 1500);\n\n        } catch (error) {\n            console.error('Error updating KYC status:', error);\n            setKycError(error.message || t('kyc_update_error'));\n        } finally {\n            setKycLoading(false);\n        }\n    };\n\n    const closeKycModal = () => {\n        setShowKycModal(false);\n        setSelectedMember(null);\n        setKycError('');\n        setKycSuccess('');\n    };\n\n    const handleChangeAgent = async (member) => {\n        setChangeAgentMember(member);\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n        setSelectedNewAgent('');\n        \n        // Fetch available agents\n        await fetchAvailableAgents();\n        \n        setShowChangeAgentModal(true);\n    };\n\n    const handleConfirmChangeAgent = async () => {\n        if (!changeAgentMember || !selectedNewAgent) {\n            setChangeAgentError(t('please_select_agent'));\n            return;\n        }\n\n        setChangeAgentLoading(true);\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error('Database connection failed');\n            }\n\n            // Update customer's agent_id\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .update({ agent_id: selectedNewAgent })\n                .eq('user_id', changeAgentMember.user_id)\n                .select();\n\n            if (error) {\n                console.error('Database error:', error);\n                throw error;\n            }\n\n            if (!data || data.length === 0) {\n                throw new Error('Failed to update agent assignment');\n            }\n\n            // Remove the member from current list since they're no longer assigned to current agent\n            setMembers(prevMembers => \n                prevMembers.filter(member => member.user_id !== changeAgentMember.user_id)\n            );\n\n            setChangeAgentSuccess(t('agent_changed_successfully'));\n            \n            // Close modal after 1.5 seconds\n            setTimeout(() => {\n                setShowChangeAgentModal(false);\n                setChangeAgentMember(null);\n                setSelectedNewAgent('');\n            }, 1500);\n\n        } catch (error) {\n            console.error('Error changing agent:', error);\n            setChangeAgentError(error.message || t('agent_change_error'));\n        } finally {\n            setChangeAgentLoading(false);\n        }\n    };\n\n    const closeChangeAgentModal = () => {\n        setShowChangeAgentModal(false);\n        setChangeAgentMember(null);\n        setSelectedNewAgent('');\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n    };\n\n\n    if (loading) {\n        return <div>{t('loading_members')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('member_list')}</h2>\n            \n            {/* Top Operation Bar */}\n            <Row className=\"mb-4\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={2}>\n                                    <Button \n                                        variant=\"primary\" \n                                        onClick={handleAddMember}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaPlus className=\"me-1\" />\n                                        {t('add_member')}\n                                    </Button>\n                                </Col>\n                                <Col md={3}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_username')}</Form.Label>\n                                        <InputGroup>\n                                            <Form.Control\n                                                type=\"text\"\n                                                placeholder={t('please_enter_username')}\n                                                value={searchTerm}\n                                                onChange={(e) => setSearchTerm(e.target.value)}\n                                            />\n                                        </InputGroup>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('status_filter')}</Form.Label>\n                                        <Form.Select\n                                            value={statusFilter}\n                                            onChange={(e) => setStatusFilter(e.target.value)}\n                                        >\n                                            <option value=\"\">{t('please_select_status')}</option>\n                                            <option value=\"pending\">{t('pending_review')}</option>\n                                            <option value=\"approved\">{t('approved')}</option>\n                                            <option value=\"rejected\">{t('rejected')}</option>\n                                            <option value=\"under_review\">{t('under_review')}</option>\n                                        </Form.Select>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('start_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={startDate}\n                                            onChange={(e) => setStartDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('end_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={endDate}\n                                            onChange={(e) => setEndDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={1}>\n                                    <Button \n                                        variant=\"outline-primary\" \n                                        onClick={handleSearch}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaSearch />\n                                    </Button>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* Members Table */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('username')}</th>\n                                        <th>{t('real_name')}</th>\n                                        <th>{t('id_number')}</th>\n                                        <th>{t('id_front_image')}</th>\n                                        <th>{t('id_back_image')}</th>\n                                        <th>{t('status')}</th>\n                                        <th>{t('registration_time')}</th>\n                                        <th>{t('actions')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {filteredMembers.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"9\" className=\"text-center\">{t('no_members_found')}</td>\n                                        </tr>\n                                    ) : (\n                                        filteredMembers.map(member => (\n                                            <tr key={member.user_id}>\n                                                <td>{member.users?.email || '-'}</td>\n                                                <td>{member.real_name || '-'}</td>\n                                                <td>{member.id_number || '-'}</td>\n                                                <td>\n                                                    {member.id_img_front ? (\n                                                        <img \n                                                            src={member.id_img_front} \n                                                            alt=\"ID Front\" \n                                                            style={{ \n                                                                width: '60px', \n                                                                height: '40px', \n                                                                objectFit: 'cover', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer'\n                                                            }}\n                                                            onClick={() => window.open(member.id_img_front, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <span className=\"text-muted\">-</span>\n                                                    )}\n                                                </td>\n                                                <td>\n                                                    {member.id_img_back ? (\n                                                        <img \n                                                            src={member.id_img_back} \n                                                            alt=\"ID Back\" \n                                                            style={{ \n                                                                width: '60px', \n                                                                height: '40px', \n                                                                objectFit: 'cover', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer'\n                                                            }}\n                                                            onClick={() => window.open(member.id_img_back, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <span className=\"text-muted\">-</span>\n                                                    )}\n                                                </td>\n                                                <td>{getStatusBadge(member.verify_status)}</td>\n                                                <td>{member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'}</td>\n                                                <td>\n                                                    <div className=\"d-flex justify-content-between\">\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-primary\"\n                                                            onClick={() => handleKycReview(member)}\n                                                            title={t('kyc_review')}\n                                                        >\n                                                            <FaUserCheck />\n                                                        </Button>\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-warning\"\n                                                            onClick={() => handleChangeAgent(member)}\n                                                            title={t('change_agent')}\n                                                        >\n                                                            <FaExchangeAlt />\n                                                        </Button>\n                                                    </div>\n                                                </td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* KYC Review Modal */}\n            <Modal show={showKycModal} onHide={closeKycModal} size=\"lg\">\n                <Modal.Header closeButton className=\"custom-modal-header\">\n                    <Modal.Title>{t('kyc_review')}</Modal.Title>\n                </Modal.Header>\n                <style dangerouslySetInnerHTML={{\n                __html: `\n                    .custom-modal-header .btn-close {\n                    background: none !important;\n                    border: none !important;\n                    opacity: 0.8 !important;\n                    background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 16 16'%3E%3Cpath d='M2.146 2.146a.5.5 0 0 1 .708 0L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E\") !important;\n                    background-size: 1em 1em !important;\n                    background-repeat: no-repeat !important;\n                    background-position: center !important;\n                    }\n                    .custom-modal-header .btn-close:hover {\n                    opacity: 1 !important;\n                    background-color: rgba(255, 255, 255, 0.1) !important;\n                    }\n                `\n                }} />\n\n                <Modal.Body>\n                    {selectedMember && (\n                        <>\n                            {kycError && (\n                                <Alert variant=\"danger\" className=\"mb-3\">\n                                    {kycError}\n                                </Alert>\n                            )}\n                            {kycSuccess && (\n                                <Alert variant=\"success\" className=\"mb-3\">\n                                    {kycSuccess}\n                                </Alert>\n                            )}\n                            \n                            <Row>\n                                <Col md={6}>\n                                    <Card className=\"mb-3\">\n                                        <Card.Header>\n                                            <strong>{t('customer_info')}</strong>\n                                        </Card.Header>\n                                        <Card.Body>\n                                            <p><strong>{t('username')}:</strong> {selectedMember.users?.email || '-'}</p>\n                                            <p><strong>{t('real_name')}:</strong> {selectedMember.real_name || '-'}</p>\n                                            <p><strong>{t('id_number')}:</strong> {selectedMember.id_number || '-'}</p>\n                                            <p><strong>{t('current_status')}:</strong> {getStatusBadge(selectedMember.verify_status)}</p>\n                                            <p><strong>{t('registration_time')}:</strong> {selectedMember.users?.created_at ? new Date(selectedMember.users.created_at).toLocaleString() : '-'}</p>\n                                        </Card.Body>\n                                    </Card>\n                                </Col>\n                                <Col md={6}>\n                                    <Card className=\"mb-3\">\n                                        <Card.Header>\n                                            <strong>{t('id_documents')}</strong>\n                                        </Card.Header>\n                                        <Card.Body>\n                                            <div className=\"mb-3\">\n                                                <strong>{t('id_front_image')}:</strong>\n                                                <div className=\"mt-2\">\n                                                    {selectedMember.id_img_front ? (\n                                                        <img \n                                                            src={selectedMember.id_img_front} \n                                                            alt=\"ID Front\" \n                                                            style={{ \n                                                                width: '100%', \n                                                                maxHeight: '150px', \n                                                                objectFit: 'contain', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer',\n                                                                border: '1px solid #dee2e6'\n                                                            }}\n                                                            onClick={() => window.open(selectedMember.id_img_front, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <div className=\"text-muted text-center py-3\" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>\n                                                            {t('no_image_uploaded')}\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                            <div className=\"mb-3\">\n                                                <strong>{t('id_back_image')}:</strong>\n                                                <div className=\"mt-2\">\n                                                    {selectedMember.id_img_back ? (\n                                                        <img \n                                                            src={selectedMember.id_img_back} \n                                                            alt=\"ID Back\" \n                                                            style={{ \n                                                                width: '100%', \n                                                                maxHeight: '150px', \n                                                                objectFit: 'contain', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer',\n                                                                border: '1px solid #dee2e6'\n                                                            }}\n                                                            onClick={() => window.open(selectedMember.id_img_back, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <div className=\"text-muted text-center py-3\" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>\n                                                            {t('no_image_uploaded')}\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                        </Card.Body>\n                                    </Card>\n                                </Col>\n                            </Row>\n                        </>\n                    )}\n                </Modal.Body>\n                <Modal.Footer>\n                    <Button variant=\"secondary\" onClick={closeKycModal} disabled={kycLoading}>\n                        {t('cancel')}\n                    </Button>\n                    <Button \n                        variant=\"danger\" \n                        onClick={() => handleKycDecision('rejected')}\n                        disabled={kycLoading || selectedMember?.verify_status === 'rejected'}\n                    >\n                        {kycLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaTimes className=\"me-1\" />\n                                {t('reject')}\n                            </>\n                        )}\n                    </Button>\n                    <Button \n                        variant=\"success\" \n                        onClick={() => handleKycDecision('approved')}\n                        disabled={kycLoading || selectedMember?.verify_status === 'approved'}\n                    >\n                        {kycLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaCheck className=\"me-1\" />\n                                {t('approve')}\n                            </>\n                        )}\n                    </Button>\n                </Modal.Footer>\n            </Modal>\n\n            {/* Change Agent Modal */}\n            <Modal show={showChangeAgentModal} onHide={closeChangeAgentModal} size=\"md\">\n                <Modal.Header closeButton>\n                    <Modal.Title>{t('change_agent')}</Modal.Title>\n                </Modal.Header>\n                <Modal.Body>\n                    {changeAgentError && (\n                        <Alert variant=\"danger\" className=\"mb-3\">\n                            {changeAgentError}\n                        </Alert>\n                    )}\n                    {changeAgentSuccess && (\n                        <Alert variant=\"success\" className=\"mb-3\">\n                            {changeAgentSuccess}\n                        </Alert>\n                    )}\n                    \n                    {changeAgentMember && (\n                        <div className=\"mb-4\">\n                            <Card>\n                                <Card.Header>\n                                    <strong>{t('customer_info')}</strong>\n                                </Card.Header>\n                                <Card.Body>\n                                    <p><strong>{t('username')}:</strong> {changeAgentMember.users?.email || '-'}</p>\n                                    <p><strong>{t('real_name')}:</strong> {changeAgentMember.real_name || '-'}</p>\n                                    <p><strong>{t('current_status')}:</strong> {getStatusBadge(changeAgentMember.verify_status)}</p>\n                                </Card.Body>\n                            </Card>\n                        </div>\n                    )}\n\n                    <Form.Group className=\"mb-3\">\n                        <Form.Label><strong>{t('select_new_agent')}</strong></Form.Label>\n                        <Form.Select\n                            value={selectedNewAgent}\n                            onChange={(e) => setSelectedNewAgent(e.target.value)}\n                            disabled={changeAgentLoading}\n                        >\n                            <option value=\"\">{t('please_select_agent')}</option>\n                            {availableAgents.map(agent => (\n                                <option key={agent.user_id} value={agent.user_id}>\n                                    {agent.brand_name || agent.users?.email || agent.user_id} \n                                    {agent.commission_pct && ` (${agent.commission_pct}%)`}\n                                </option>\n                            ))}\n                        </Form.Select>\n                        {availableAgents.length === 0 && (\n                            <Form.Text className=\"text-muted\">\n                                {t('no_available_agents')}\n                            </Form.Text>\n                        )}\n                    </Form.Group>\n                </Modal.Body>\n                <Modal.Footer>\n                    <Button variant=\"secondary\" onClick={closeChangeAgentModal} disabled={changeAgentLoading}>\n                        {t('cancel')}\n                    </Button>\n                    <Button \n                        variant=\"primary\" \n                        onClick={handleConfirmChangeAgent}\n                        disabled={changeAgentLoading || !selectedNewAgent || availableAgents.length === 0}\n                    >\n                        {changeAgentLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaExchangeAlt className=\"me-1\" />\n                                {t('confirm_change')}\n                            </>\n                        )}\n                    </Button>\n                </Modal.Footer>\n            </Modal>\n        </Container>\n    );\n};\n\nexport default Members;"], "names": ["Members", "_selectedMember$users", "_selectedMember$users2", "_changeAgentMember$us", "t", "useTranslation", "members", "setMembers", "useState", "loading", "setLoading", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "startDate", "setStartDate", "endDate", "setEndDate", "filteredMembers", "setFilteredMembers", "showKycModal", "setShowKycModal", "selected<PERSON><PERSON>ber", "setSelectedMember", "kycLoading", "setKycLoading", "kycError", "setKycError", "kycSuccess", "setKycSuccess", "showChangeAgentModal", "setShowChangeAgentModal", "changeAgentMember", "setChangeAgentMember", "availableAgents", "setAvailableAgents", "selectedNewAgent", "setSelectedNewAgent", "changeAgentLoading", "setChangeAgentLoading", "changeAgentError", "setChangeAgentError", "changeAgentSuccess", "setChangeAgentSuccess", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "customers", "error", "profileError", "from", "select", "eq", "id", "order", "ascending", "console", "map", "c", "user_id", "filter", "Boolean", "userInfoList", "userError", "usersMap", "Map", "u", "enrichedMembers", "users", "get", "fetchMembers", "filtered", "member", "_member$users", "_member$users$email", "_member$real_name", "email", "toLowerCase", "includes", "real_name", "verify_status", "_member$users2", "Date", "created_at", "_member$users3", "getStatusBadge", "status", "_jsx", "Badge", "bg", "children", "handleKycDecision", "Error", "update", "decision", "length", "existingRecord", "selectError", "prevMembers", "prev", "setTimeout", "message", "closeKycModal", "handleChangeAgent", "agents", "neq", "fetchAvailableAgents", "closeChangeAgentModal", "_jsxs", "Container", "className", "Row", "Col", "Card", "Body", "md", "<PERSON><PERSON>", "variant", "onClick", "handleAddMember", "alert", "FaPlus", "Form", "Group", "Label", "InputGroup", "Control", "type", "placeholder", "value", "onChange", "e", "target", "Select", "handleSearch", "log", "FaSearch", "Table", "striped", "bordered", "hover", "responsive", "colSpan", "_member$users4", "_member$users5", "id_number", "id_img_front", "src", "alt", "style", "width", "height", "objectFit", "borderRadius", "cursor", "window", "open", "id_img_back", "toLocaleString", "size", "handleKycReview", "title", "FaUserCheck", "FaExchangeAlt", "Modal", "show", "onHide", "Header", "closeButton", "Title", "dangerouslySetInnerHTML", "__html", "_Fragment", "<PERSON><PERSON>", "maxHeight", "border", "Footer", "disabled", "role", "FaTimes", "FaCheck", "agent", "_agent$users", "brand_name", "commission_pct", "Text", "agent_id"], "sourceRoot": ""}