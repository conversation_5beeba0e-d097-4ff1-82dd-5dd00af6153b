{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge,Button,Form,InputGroup,Dropdown,Modal,Alert}from'react-bootstrap';import{FaSearch,FaPlus,FaEye,FaUserCheck,FaExchangeAlt,FaCheck,FaTimes}from'react-icons/fa';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Members=()=>{var _selectedMember$users,_selectedMember$users2;const{t}=useTranslation();const[members,setMembers]=useState([]);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[statusFilter,setStatusFilter]=useState('');const[startDate,setStartDate]=useState('');const[endDate,setEndDate]=useState('');const[filteredMembers,setFilteredMembers]=useState([]);const[showKycModal,setShowKycModal]=useState(false);const[selectedMember,setSelectedMember]=useState(null);const[kycLoading,setKycLoading]=useState(false);const[kycError,setKycError]=useState('');const[kycSuccess,setKycSuccess]=useState('');useEffect(()=>{const fetchMembers=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;}// Step 1: 查询 customer_profiles\nconst{data:customers,error:profileError}=await supabase.from('customer_profiles').select('user_id, real_name, id_number, id_img_front, id_img_back, verify_status').eq('agent_id',user.id).order('created_at',{ascending:false});if(profileError||!customers){console.error('Error fetching customer_profiles:',profileError);setLoading(false);return;}// Step 2: 查询 users 表\nconst userIds=customers.map(c=>c.user_id).filter(Boolean);const{data:userInfoList,error:userError}=await supabase.from('users').select('id, email, created_at');if(userError){console.error('Error fetching users:',userError);}// Step 3: 合并结果\nconst usersMap=new Map((userInfoList||[]).map(u=>[u.id,u]));const enrichedMembers=customers.map(c=>({...c,users:usersMap.get(c.user_id)||{}}));setMembers(enrichedMembers);setLoading(false);};fetchMembers();},[]);// Filter members based on search criteria\nuseEffect(()=>{let filtered=members;// Search by username (email)\nif(searchTerm){filtered=filtered.filter(member=>{var _member$users,_member$users$email,_member$real_name;return((_member$users=member.users)===null||_member$users===void 0?void 0:(_member$users$email=_member$users.email)===null||_member$users$email===void 0?void 0:_member$users$email.toLowerCase().includes(searchTerm.toLowerCase()))||((_member$real_name=member.real_name)===null||_member$real_name===void 0?void 0:_member$real_name.toLowerCase().includes(searchTerm.toLowerCase()));});}// Filter by status\nif(statusFilter){filtered=filtered.filter(member=>member.verify_status===statusFilter);}// Filter by date range\nif(startDate){filtered=filtered.filter(member=>{var _member$users2;return new Date((_member$users2=member.users)===null||_member$users2===void 0?void 0:_member$users2.created_at)>=new Date(startDate);});}if(endDate){filtered=filtered.filter(member=>{var _member$users3;return new Date((_member$users3=member.users)===null||_member$users3===void 0?void 0:_member$users3.created_at)<=new Date(endDate);});}setFilteredMembers(filtered);},[members,searchTerm,statusFilter,startDate,endDate]);const getStatusBadge=status=>{switch(status){case'approved':return/*#__PURE__*/_jsx(Badge,{bg:\"success\",children:t('approved')});case'pending':return/*#__PURE__*/_jsx(Badge,{bg:\"warning\",children:t('pending_review')});case'rejected':return/*#__PURE__*/_jsx(Badge,{bg:\"danger\",children:t('rejected')});case'under_review':return/*#__PURE__*/_jsx(Badge,{bg:\"info\",children:t('under_review')});default:return/*#__PURE__*/_jsx(Badge,{bg:\"secondary\",children:status||t('not_submitted')});}};const handleSearch=()=>{// Search is handled by useEffect, this function can be used for additional logic if needed\nconsole.log('Search triggered');};const handleAddMember=()=>{// TODO: Implement add member functionality\nalert(t('add_member_coming_soon'));};const handleKycReview=member=>{console.log('Selected member for KYC review:',member);setSelectedMember(member);setShowKycModal(true);setKycError('');setKycSuccess('');};const handleKycDecision=async decision=>{if(!selectedMember)return;setKycLoading(true);setKycError('');setKycSuccess('');try{const supabase=getSupabase();if(!supabase){throw new Error('Database connection failed');}console.log('Updating KYC status for user:',selectedMember.user_id,'to:',decision);// Try to update using user_id first\nconst{data,error}=await supabase.from('customer_profiles').update({verify_status:decision}).eq('user_id',selectedMember.user_id).select();if(error){console.error('Database error:',error);throw error;}console.log('Update result:',data);// Check if any rows were updated\nif(!data||data.length===0){// If no rows updated, it might be because the record doesn't exist or user_id is different\nconsole.log('No rows updated, checking record existence...');// Try to find the record\nconst{data:existingRecord,error:selectError}=await supabase.from('customer_profiles').select('*').eq('user_id',selectedMember.user_id);if(selectError){console.error('Error checking existing record:',selectError);throw selectError;}console.log('Existing record:',existingRecord);if(!existingRecord||existingRecord.length===0){throw new Error('Customer profile not found');}}// Update local state\nsetMembers(prevMembers=>prevMembers.map(member=>member.user_id===selectedMember.user_id?{...member,verify_status:decision}:member));// Also update the selected member\nsetSelectedMember(prev=>({...prev,verify_status:decision}));setKycSuccess(decision==='approved'?t('kyc_approved_success'):t('kyc_rejected_success'));// Close modal after 1.5 seconds\nsetTimeout(()=>{setShowKycModal(false);setSelectedMember(null);},1500);}catch(error){console.error('Error updating KYC status:',error);setKycError(error.message||t('kyc_update_error'));}finally{setKycLoading(false);}};const closeKycModal=()=>{setShowKycModal(false);setSelectedMember(null);setKycError('');setKycSuccess('');};const handleChangeAgent=memberId=>{// TODO: Implement change agent functionality\nalert(t('change_agent_coming_soon'));};const handleViewDetails=memberId=>{// TODO: Implement view details functionality\nalert(t('view_details_coming_soon'));};if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_members')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('member_list')}),/*#__PURE__*/_jsx(Row,{className:\"mb-4\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{className:\"align-items-end\",children:[/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Button,{variant:\"primary\",onClick:handleAddMember,className:\"mb-2\",children:[/*#__PURE__*/_jsx(FaPlus,{className:\"me-1\"}),t('add_member')]})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('search_username')}),/*#__PURE__*/_jsx(InputGroup,{children:/*#__PURE__*/_jsx(Form.Control,{type:\"text\",placeholder:t('please_enter_username'),value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})})]})}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('status_filter')}),/*#__PURE__*/_jsxs(Form.Select,{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:t('please_select_status')}),/*#__PURE__*/_jsx(\"option\",{value:\"pending\",children:t('pending_review')}),/*#__PURE__*/_jsx(\"option\",{value:\"approved\",children:t('approved')}),/*#__PURE__*/_jsx(\"option\",{value:\"rejected\",children:t('rejected')}),/*#__PURE__*/_jsx(\"option\",{value:\"under_review\",children:t('under_review')})]})]})}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('start_date')}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:startDate,onChange:e=>setStartDate(e.target.value)})]})}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('end_date')}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:endDate,onChange:e=>setEndDate(e.target.value)})]})}),/*#__PURE__*/_jsx(Col,{md:1,children:/*#__PURE__*/_jsx(Button,{variant:\"outline-primary\",onClick:handleSearch,className:\"mb-2\",children:/*#__PURE__*/_jsx(FaSearch,{})})})]})})})})}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('username')}),/*#__PURE__*/_jsx(\"th\",{children:t('real_name')}),/*#__PURE__*/_jsx(\"th\",{children:t('id_number')}),/*#__PURE__*/_jsx(\"th\",{children:t('id_front_image')}),/*#__PURE__*/_jsx(\"th\",{children:t('id_back_image')}),/*#__PURE__*/_jsx(\"th\",{children:t('status')}),/*#__PURE__*/_jsx(\"th\",{children:t('registration_time')}),/*#__PURE__*/_jsx(\"th\",{children:t('actions')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:filteredMembers.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"9\",className:\"text-center\",children:t('no_members_found')})}):filteredMembers.map(member=>{var _member$users4,_member$users5;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:((_member$users4=member.users)===null||_member$users4===void 0?void 0:_member$users4.email)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:member.real_name||'-'}),/*#__PURE__*/_jsx(\"td\",{children:member.id_number||'-'}),/*#__PURE__*/_jsx(\"td\",{children:member.id_img_front?/*#__PURE__*/_jsx(\"img\",{src:member.id_img_front,alt:\"ID Front\",style:{width:'60px',height:'40px',objectFit:'cover',borderRadius:'4px',cursor:'pointer'},onClick:()=>window.open(member.id_img_front,'_blank')}):/*#__PURE__*/_jsx(\"span\",{className:\"text-muted\",children:\"-\"})}),/*#__PURE__*/_jsx(\"td\",{children:member.id_img_back?/*#__PURE__*/_jsx(\"img\",{src:member.id_img_back,alt:\"ID Back\",style:{width:'60px',height:'40px',objectFit:'cover',borderRadius:'4px',cursor:'pointer'},onClick:()=>window.open(member.id_img_back,'_blank')}):/*#__PURE__*/_jsx(\"span\",{className:\"text-muted\",children:\"-\"})}),/*#__PURE__*/_jsx(\"td\",{children:getStatusBadge(member.verify_status)}),/*#__PURE__*/_jsx(\"td\",{children:(_member$users5=member.users)!==null&&_member$users5!==void 0&&_member$users5.created_at?new Date(member.users.created_at).toLocaleString():'-'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-1\",children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-primary\",onClick:()=>handleKycReview(member),title:t('kyc_review'),children:/*#__PURE__*/_jsx(FaUserCheck,{})}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-warning\",onClick:()=>handleChangeAgent(member.user_id),title:t('change_agent'),children:/*#__PURE__*/_jsx(FaExchangeAlt,{})}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-info\",onClick:()=>handleViewDetails(member.user_id),title:t('view_details'),children:/*#__PURE__*/_jsx(FaEye,{})})]})})]},member.user_id);})})]})})})})}),/*#__PURE__*/_jsxs(Modal,{show:showKycModal,onHide:closeKycModal,size:\"lg\",children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,className:\"custom-modal-header\",children:/*#__PURE__*/_jsx(Modal.Title,{children:t('kyc_review')})}),/*#__PURE__*/_jsx(\"style\",{dangerouslySetInnerHTML:{__html:`\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    `}}),/*#__PURE__*/_jsx(Modal.Body,{children:selectedMember&&/*#__PURE__*/_jsxs(_Fragment,{children:[kycError&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"mb-3\",children:kycError}),kycSuccess&&/*#__PURE__*/_jsx(Alert,{variant:\"success\",className:\"mb-3\",children:kycSuccess}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('customer_info')})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('username'),\":\"]}),\" \",((_selectedMember$users=selectedMember.users)===null||_selectedMember$users===void 0?void 0:_selectedMember$users.email)||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('real_name'),\":\"]}),\" \",selectedMember.real_name||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('id_number'),\":\"]}),\" \",selectedMember.id_number||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('current_status'),\":\"]}),\" \",getStatusBadge(selectedMember.verify_status)]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('registration_time'),\":\"]}),\" \",(_selectedMember$users2=selectedMember.users)!==null&&_selectedMember$users2!==void 0&&_selectedMember$users2.created_at?new Date(selectedMember.users.created_at).toLocaleString():'-']})]})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('id_documents')})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('id_front_image'),\":\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:selectedMember.id_img_front?/*#__PURE__*/_jsx(\"img\",{src:selectedMember.id_img_front,alt:\"ID Front\",style:{width:'100%',maxHeight:'150px',objectFit:'contain',borderRadius:'4px',cursor:'pointer',border:'1px solid #dee2e6'},onClick:()=>window.open(selectedMember.id_img_front,'_blank')}):/*#__PURE__*/_jsx(\"div\",{className:\"text-muted text-center py-3\",style:{border:'1px dashed #dee2e6',borderRadius:'4px'},children:t('no_image_uploaded')})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('id_back_image'),\":\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:selectedMember.id_img_back?/*#__PURE__*/_jsx(\"img\",{src:selectedMember.id_img_back,alt:\"ID Back\",style:{width:'100%',maxHeight:'150px',objectFit:'contain',borderRadius:'4px',cursor:'pointer',border:'1px solid #dee2e6'},onClick:()=>window.open(selectedMember.id_img_back,'_blank')}):/*#__PURE__*/_jsx(\"div\",{className:\"text-muted text-center py-3\",style:{border:'1px dashed #dee2e6',borderRadius:'4px'},children:t('no_image_uploaded')})})]})]})]})})]})]})}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:closeKycModal,disabled:kycLoading,children:t('cancel')}),/*#__PURE__*/_jsx(Button,{variant:\"danger\",onClick:()=>handleKycDecision('rejected'),disabled:kycLoading||(selectedMember===null||selectedMember===void 0?void 0:selectedMember.verify_status)==='rejected',children:kycLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\",\"aria-hidden\":\"true\"}),t('processing')]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaTimes,{className:\"me-1\"}),t('reject')]})}),/*#__PURE__*/_jsx(Button,{variant:\"success\",onClick:()=>handleKycDecision('approved'),disabled:kycLoading||(selectedMember===null||selectedMember===void 0?void 0:selectedMember.verify_status)==='approved',children:kycLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\",\"aria-hidden\":\"true\"}),t('processing')]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaCheck,{className:\"me-1\"}),t('approve')]})})]})]})]});};export default Members;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "<PERSON><PERSON>", "Form", "InputGroup", "Dropdown", "Modal", "<PERSON><PERSON>", "FaSearch", "FaPlus", "FaEye", "FaUserCheck", "FaExchangeAlt", "FaCheck", "FaTimes", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Members", "_selectedMember$users", "_selectedMember$users2", "t", "members", "setMembers", "loading", "setLoading", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "startDate", "setStartDate", "endDate", "setEndDate", "filteredMembers", "setFilteredMembers", "showKycModal", "setShowKycModal", "selected<PERSON><PERSON>ber", "setSelectedMember", "kycLoading", "setKycLoading", "kycError", "setKycError", "kycSuccess", "setKycSuccess", "fetchMembers", "supabase", "data", "user", "auth", "getUser", "customers", "error", "profileError", "from", "select", "eq", "id", "order", "ascending", "console", "userIds", "map", "c", "user_id", "filter", "Boolean", "userInfoList", "userError", "usersMap", "Map", "u", "enrichedMembers", "users", "get", "filtered", "member", "_member$users", "_member$users$email", "_member$real_name", "email", "toLowerCase", "includes", "real_name", "verify_status", "_member$users2", "Date", "created_at", "_member$users3", "getStatusBadge", "status", "bg", "children", "handleSearch", "log", "handleAddMember", "alert", "handleKycReview", "handleKycDecision", "decision", "Error", "update", "length", "existingRecord", "selectError", "prevMembers", "prev", "setTimeout", "message", "closeKycModal", "handleChangeAgent", "memberId", "handleViewDetails", "className", "Body", "md", "variant", "onClick", "Group", "Label", "Control", "type", "placeholder", "value", "onChange", "e", "target", "Select", "striped", "bordered", "hover", "responsive", "colSpan", "_member$users4", "_member$users5", "id_number", "id_img_front", "src", "alt", "style", "width", "height", "objectFit", "borderRadius", "cursor", "window", "open", "id_img_back", "toLocaleString", "size", "title", "show", "onHide", "Header", "closeButton", "Title", "dangerouslySetInnerHTML", "__html", "maxHeight", "border", "Footer", "disabled", "role"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/Members.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge, Button, Form, InputGroup, Dropdown, Modal, Alert } from 'react-bootstrap';\nimport { FaSearch, FaPlus, FaEye, FaUserCheck, FaExchangeAlt, FaCheck, FaTimes } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Members = () => {\n    const { t } = useTranslation();\n    const [members, setMembers] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [statusFilter, setStatusFilter] = useState('');\n    const [startDate, setStartDate] = useState('');\n    const [endDate, setEndDate] = useState('');\n    const [filteredMembers, setFilteredMembers] = useState([]);\n    const [showKycModal, setShowKycModal] = useState(false);\n    const [selectedMember, setSelectedMember] = useState(null);\n    const [kycLoading, setKycLoading] = useState(false);\n    const [kycError, setKycError] = useState('');\n    const [kycSuccess, setKycSuccess] = useState('');\n\n    useEffect(() => {\n            const fetchMembers = async () => {\n                const supabase = getSupabase();\n                if (!supabase) return;\n    \n                setLoading(true);\n                const { data: { user } } = await supabase.auth.getUser();\n    \n                if (!user) {\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 1: 查询 customer_profiles\n                const { data: customers, error: profileError } = await supabase\n                    .from('customer_profiles')\n                    .select('user_id, real_name, id_number, id_img_front, id_img_back, verify_status')\n                    .eq('agent_id', user.id)\n                    .order('created_at', { ascending: false });\n    \n                if (profileError || !customers) {\n                    console.error('Error fetching customer_profiles:', profileError);\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 2: 查询 users 表\n                const userIds = customers.map(c => c.user_id).filter(Boolean);\n    \n                const { data: userInfoList, error: userError } = await supabase\n                    .from('users')\n                    .select('id, email, created_at')\n\n                if (userError) {\n                    console.error('Error fetching users:', userError);\n                }\n    \n                // Step 3: 合并结果\n                const usersMap = new Map((userInfoList || []).map(u => [u.id, u]));\n    \n                const enrichedMembers = customers.map(c => ({\n                    ...c,\n                    users: usersMap.get(c.user_id) || {}\n                }));\n    \n                setMembers(enrichedMembers);\n                setLoading(false);\n            };\n\n        fetchMembers();\n    }, []);\n\n    // Filter members based on search criteria\n    useEffect(() => {\n        let filtered = members;\n\n        // Search by username (email)\n        if (searchTerm) {\n            filtered = filtered.filter(member =>\n                member.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                member.real_name?.toLowerCase().includes(searchTerm.toLowerCase())\n            );\n        }\n\n        // Filter by status\n        if (statusFilter) {\n            filtered = filtered.filter(member => member.verify_status === statusFilter);\n        }\n\n        // Filter by date range\n        if (startDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) >= new Date(startDate)\n            );\n        }\n        if (endDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) <= new Date(endDate)\n            );\n        }\n\n        setFilteredMembers(filtered);\n    }, [members, searchTerm, statusFilter, startDate, endDate]);\n\n    const getStatusBadge = (status) => {\n        switch (status) {\n            case 'approved':\n                return <Badge bg=\"success\">{t('approved')}</Badge>;\n            case 'pending':\n                return <Badge bg=\"warning\">{t('pending_review')}</Badge>;\n            case 'rejected':\n                return <Badge bg=\"danger\">{t('rejected')}</Badge>;\n            case 'under_review':\n                return <Badge bg=\"info\">{t('under_review')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{status || t('not_submitted')}</Badge>;\n        }\n    };\n\n    const handleSearch = () => {\n        // Search is handled by useEffect, this function can be used for additional logic if needed\n        console.log('Search triggered');\n    };\n\n    const handleAddMember = () => {\n        // TODO: Implement add member functionality\n        alert(t('add_member_coming_soon'));\n    };\n\n    const handleKycReview = (member) => {\n        console.log('Selected member for KYC review:', member);\n        setSelectedMember(member);\n        setShowKycModal(true);\n        setKycError('');\n        setKycSuccess('');\n    };\n\n    const handleKycDecision = async (decision) => {\n        if (!selectedMember) return;\n\n        setKycLoading(true);\n        setKycError('');\n        setKycSuccess('');\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error('Database connection failed');\n            }\n\n            console.log('Updating KYC status for user:', selectedMember.user_id, 'to:', decision);\n\n            // Try to update using user_id first\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .update({ verify_status: decision })\n                .eq('user_id', selectedMember.user_id)\n                .select();\n\n            if (error) {\n                console.error('Database error:', error);\n                throw error;\n            }\n\n            console.log('Update result:', data);\n\n            // Check if any rows were updated\n            if (!data || data.length === 0) {\n                // If no rows updated, it might be because the record doesn't exist or user_id is different\n                console.log('No rows updated, checking record existence...');\n                \n                // Try to find the record\n                const { data: existingRecord, error: selectError } = await supabase\n                    .from('customer_profiles')\n                    .select('*')\n                    .eq('user_id', selectedMember.user_id);\n\n                if (selectError) {\n                    console.error('Error checking existing record:', selectError);\n                    throw selectError;\n                }\n\n                console.log('Existing record:', existingRecord);\n                \n                if (!existingRecord || existingRecord.length === 0) {\n                    throw new Error('Customer profile not found');\n                }\n            }\n\n            // Update local state\n            setMembers(prevMembers => \n                prevMembers.map(member => \n                    member.user_id === selectedMember.user_id \n                        ? { ...member, verify_status: decision }\n                        : member\n                )\n            );\n\n            // Also update the selected member\n            setSelectedMember(prev => ({ ...prev, verify_status: decision }));\n\n            setKycSuccess(decision === 'approved' ? t('kyc_approved_success') : t('kyc_rejected_success'));\n            \n            // Close modal after 1.5 seconds\n            setTimeout(() => {\n                setShowKycModal(false);\n                setSelectedMember(null);\n            }, 1500);\n\n        } catch (error) {\n            console.error('Error updating KYC status:', error);\n            setKycError(error.message || t('kyc_update_error'));\n        } finally {\n            setKycLoading(false);\n        }\n    };\n\n    const closeKycModal = () => {\n        setShowKycModal(false);\n        setSelectedMember(null);\n        setKycError('');\n        setKycSuccess('');\n    };\n\n    const handleChangeAgent = (memberId) => {\n        // TODO: Implement change agent functionality\n        alert(t('change_agent_coming_soon'));\n    };\n\n    const handleViewDetails = (memberId) => {\n        // TODO: Implement view details functionality\n        alert(t('view_details_coming_soon'));\n    };\n\n    if (loading) {\n        return <div>{t('loading_members')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('member_list')}</h2>\n            \n            {/* Top Operation Bar */}\n            <Row className=\"mb-4\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={2}>\n                                    <Button \n                                        variant=\"primary\" \n                                        onClick={handleAddMember}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaPlus className=\"me-1\" />\n                                        {t('add_member')}\n                                    </Button>\n                                </Col>\n                                <Col md={3}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_username')}</Form.Label>\n                                        <InputGroup>\n                                            <Form.Control\n                                                type=\"text\"\n                                                placeholder={t('please_enter_username')}\n                                                value={searchTerm}\n                                                onChange={(e) => setSearchTerm(e.target.value)}\n                                            />\n                                        </InputGroup>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('status_filter')}</Form.Label>\n                                        <Form.Select\n                                            value={statusFilter}\n                                            onChange={(e) => setStatusFilter(e.target.value)}\n                                        >\n                                            <option value=\"\">{t('please_select_status')}</option>\n                                            <option value=\"pending\">{t('pending_review')}</option>\n                                            <option value=\"approved\">{t('approved')}</option>\n                                            <option value=\"rejected\">{t('rejected')}</option>\n                                            <option value=\"under_review\">{t('under_review')}</option>\n                                        </Form.Select>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('start_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={startDate}\n                                            onChange={(e) => setStartDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('end_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={endDate}\n                                            onChange={(e) => setEndDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={1}>\n                                    <Button \n                                        variant=\"outline-primary\" \n                                        onClick={handleSearch}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaSearch />\n                                    </Button>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* Members Table */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('username')}</th>\n                                        <th>{t('real_name')}</th>\n                                        <th>{t('id_number')}</th>\n                                        <th>{t('id_front_image')}</th>\n                                        <th>{t('id_back_image')}</th>\n                                        <th>{t('status')}</th>\n                                        <th>{t('registration_time')}</th>\n                                        <th>{t('actions')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {filteredMembers.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"9\" className=\"text-center\">{t('no_members_found')}</td>\n                                        </tr>\n                                    ) : (\n                                        filteredMembers.map(member => (\n                                            <tr key={member.user_id}>\n                                                <td>{member.users?.email || '-'}</td>\n                                                <td>{member.real_name || '-'}</td>\n                                                <td>{member.id_number || '-'}</td>\n                                                <td>\n                                                    {member.id_img_front ? (\n                                                        <img \n                                                            src={member.id_img_front} \n                                                            alt=\"ID Front\" \n                                                            style={{ \n                                                                width: '60px', \n                                                                height: '40px', \n                                                                objectFit: 'cover', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer'\n                                                            }}\n                                                            onClick={() => window.open(member.id_img_front, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <span className=\"text-muted\">-</span>\n                                                    )}\n                                                </td>\n                                                <td>\n                                                    {member.id_img_back ? (\n                                                        <img \n                                                            src={member.id_img_back} \n                                                            alt=\"ID Back\" \n                                                            style={{ \n                                                                width: '60px', \n                                                                height: '40px', \n                                                                objectFit: 'cover', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer'\n                                                            }}\n                                                            onClick={() => window.open(member.id_img_back, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <span className=\"text-muted\">-</span>\n                                                    )}\n                                                </td>\n                                                <td>{getStatusBadge(member.verify_status)}</td>\n                                                <td>{member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'}</td>\n                                                <td>\n                                                    <div className=\"d-flex gap-1\">\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-primary\"\n                                                            onClick={() => handleKycReview(member)}\n                                                            title={t('kyc_review')}\n                                                        >\n                                                            <FaUserCheck />\n                                                        </Button>\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-warning\"\n                                                            onClick={() => handleChangeAgent(member.user_id)}\n                                                            title={t('change_agent')}\n                                                        >\n                                                            <FaExchangeAlt />\n                                                        </Button>\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-info\"\n                                                            onClick={() => handleViewDetails(member.user_id)}\n                                                            title={t('view_details')}\n                                                        >\n                                                            <FaEye />\n                                                        </Button>\n                                                    </div>\n                                                </td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* KYC Review Modal */}\n            <Modal show={showKycModal} onHide={closeKycModal} size=\"lg\">\n                <Modal.Header closeButton className=\"custom-modal-header\">\n                    <Modal.Title>{t('kyc_review')}</Modal.Title>\n                </Modal.Header>\n                <style dangerouslySetInnerHTML={{\n                    __html: `\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    `\n                }} />\n                <Modal.Body>\n                    {selectedMember && (\n                        <>\n                            {kycError && (\n                                <Alert variant=\"danger\" className=\"mb-3\">\n                                    {kycError}\n                                </Alert>\n                            )}\n                            {kycSuccess && (\n                                <Alert variant=\"success\" className=\"mb-3\">\n                                    {kycSuccess}\n                                </Alert>\n                            )}\n                            \n                            <Row>\n                                <Col md={6}>\n                                    <Card className=\"mb-3\">\n                                        <Card.Header>\n                                            <strong>{t('customer_info')}</strong>\n                                        </Card.Header>\n                                        <Card.Body>\n                                            <p><strong>{t('username')}:</strong> {selectedMember.users?.email || '-'}</p>\n                                            <p><strong>{t('real_name')}:</strong> {selectedMember.real_name || '-'}</p>\n                                            <p><strong>{t('id_number')}:</strong> {selectedMember.id_number || '-'}</p>\n                                            <p><strong>{t('current_status')}:</strong> {getStatusBadge(selectedMember.verify_status)}</p>\n                                            <p><strong>{t('registration_time')}:</strong> {selectedMember.users?.created_at ? new Date(selectedMember.users.created_at).toLocaleString() : '-'}</p>\n                                        </Card.Body>\n                                    </Card>\n                                </Col>\n                                <Col md={6}>\n                                    <Card className=\"mb-3\">\n                                        <Card.Header>\n                                            <strong>{t('id_documents')}</strong>\n                                        </Card.Header>\n                                        <Card.Body>\n                                            <div className=\"mb-3\">\n                                                <strong>{t('id_front_image')}:</strong>\n                                                <div className=\"mt-2\">\n                                                    {selectedMember.id_img_front ? (\n                                                        <img \n                                                            src={selectedMember.id_img_front} \n                                                            alt=\"ID Front\" \n                                                            style={{ \n                                                                width: '100%', \n                                                                maxHeight: '150px', \n                                                                objectFit: 'contain', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer',\n                                                                border: '1px solid #dee2e6'\n                                                            }}\n                                                            onClick={() => window.open(selectedMember.id_img_front, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <div className=\"text-muted text-center py-3\" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>\n                                                            {t('no_image_uploaded')}\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                            <div className=\"mb-3\">\n                                                <strong>{t('id_back_image')}:</strong>\n                                                <div className=\"mt-2\">\n                                                    {selectedMember.id_img_back ? (\n                                                        <img \n                                                            src={selectedMember.id_img_back} \n                                                            alt=\"ID Back\" \n                                                            style={{ \n                                                                width: '100%', \n                                                                maxHeight: '150px', \n                                                                objectFit: 'contain', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer',\n                                                                border: '1px solid #dee2e6'\n                                                            }}\n                                                            onClick={() => window.open(selectedMember.id_img_back, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <div className=\"text-muted text-center py-3\" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>\n                                                            {t('no_image_uploaded')}\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                        </Card.Body>\n                                    </Card>\n                                </Col>\n                            </Row>\n                        </>\n                    )}\n                </Modal.Body>\n                <Modal.Footer>\n                    <Button variant=\"secondary\" onClick={closeKycModal} disabled={kycLoading}>\n                        {t('cancel')}\n                    </Button>\n                    <Button \n                        variant=\"danger\" \n                        onClick={() => handleKycDecision('rejected')}\n                        disabled={kycLoading || selectedMember?.verify_status === 'rejected'}\n                    >\n                        {kycLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaTimes className=\"me-1\" />\n                                {t('reject')}\n                            </>\n                        )}\n                    </Button>\n                    <Button \n                        variant=\"success\" \n                        onClick={() => handleKycDecision('approved')}\n                        disabled={kycLoading || selectedMember?.verify_status === 'approved'}\n                    >\n                        {kycLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaCheck className=\"me-1\" />\n                                {t('approve')}\n                            </>\n                        )}\n                    </Button>\n                </Modal.Footer>\n            </Modal>\n        </Container>\n    );\n};\n\nexport default Members;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,MAAM,CAAEC,IAAI,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CAC3H,OAASC,QAAQ,CAAEC,MAAM,CAAEC,KAAK,CAAEC,WAAW,CAAEC,aAAa,CAAEC,OAAO,CAAEC,OAAO,KAAQ,gBAAgB,CACtG,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/C,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAClB,KAAM,CAAEC,CAAE,CAAC,CAAGV,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACW,OAAO,CAAEC,UAAU,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACmC,OAAO,CAAEC,UAAU,CAAC,CAAGpC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqC,UAAU,CAAEC,aAAa,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACuC,YAAY,CAAEC,eAAe,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACyC,SAAS,CAAEC,YAAY,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC2C,OAAO,CAAEC,UAAU,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC6C,eAAe,CAAEC,kBAAkB,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC+C,YAAY,CAAEC,eAAe,CAAC,CAAGhD,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACiD,cAAc,CAAEC,iBAAiB,CAAC,CAAGlD,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACmD,UAAU,CAAEC,aAAa,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACqD,QAAQ,CAAEC,WAAW,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACuD,UAAU,CAAEC,aAAa,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CAEhDC,SAAS,CAAC,IAAM,CACR,KAAM,CAAAwD,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACqC,QAAQ,CAAE,OAEftB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEuB,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPxB,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEuB,IAAI,CAAEI,SAAS,CAAEC,KAAK,CAAEC,YAAa,CAAC,CAAG,KAAM,CAAAP,QAAQ,CAC1DQ,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,yEAAyE,CAAC,CACjFC,EAAE,CAAC,UAAU,CAAER,IAAI,CAACS,EAAE,CAAC,CACvBC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIN,YAAY,EAAI,CAACF,SAAS,CAAE,CAC5BS,OAAO,CAACR,KAAK,CAAC,mCAAmC,CAAEC,YAAY,CAAC,CAChE7B,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAAqC,OAAO,CAAGV,SAAS,CAACW,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAE7D,KAAM,CAAEnB,IAAI,CAAEoB,YAAY,CAAEf,KAAK,CAAEgB,SAAU,CAAC,CAAG,KAAM,CAAAtB,QAAQ,CAC1DQ,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,uBAAuB,CAAC,CAEpC,GAAIa,SAAS,CAAE,CACXR,OAAO,CAACR,KAAK,CAAC,uBAAuB,CAAEgB,SAAS,CAAC,CACrD,CAEA;AACA,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,GAAG,CAAC,CAACH,YAAY,EAAI,EAAE,EAAEL,GAAG,CAACS,CAAC,EAAI,CAACA,CAAC,CAACd,EAAE,CAAEc,CAAC,CAAC,CAAC,CAAC,CAElE,KAAM,CAAAC,eAAe,CAAGrB,SAAS,CAACW,GAAG,CAACC,CAAC,GAAK,CACxC,GAAGA,CAAC,CACJU,KAAK,CAAEJ,QAAQ,CAACK,GAAG,CAACX,CAAC,CAACC,OAAO,CAAC,EAAI,CAAC,CACvC,CAAC,CAAC,CAAC,CAEH1C,UAAU,CAACkD,eAAe,CAAC,CAC3BhD,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAELqB,YAAY,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN;AACAxD,SAAS,CAAC,IAAM,CACZ,GAAI,CAAAsF,QAAQ,CAAGtD,OAAO,CAEtB;AACA,GAAII,UAAU,CAAE,CACZkD,QAAQ,CAAGA,QAAQ,CAACV,MAAM,CAACW,MAAM,OAAAC,aAAA,CAAAC,mBAAA,CAAAC,iBAAA,OAC7B,EAAAF,aAAA,CAAAD,MAAM,CAACH,KAAK,UAAAI,aAAA,kBAAAC,mBAAA,CAAZD,aAAA,CAAcG,KAAK,UAAAF,mBAAA,iBAAnBA,mBAAA,CAAqBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzD,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC,KAAAF,iBAAA,CACrEH,MAAM,CAACO,SAAS,UAAAJ,iBAAA,iBAAhBA,iBAAA,CAAkBE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzD,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC,GACtE,CAAC,CACL,CAEA;AACA,GAAItD,YAAY,CAAE,CACdgD,QAAQ,CAAGA,QAAQ,CAACV,MAAM,CAACW,MAAM,EAAIA,MAAM,CAACQ,aAAa,GAAKzD,YAAY,CAAC,CAC/E,CAEA;AACA,GAAIE,SAAS,CAAE,CACX8C,QAAQ,CAAGA,QAAQ,CAACV,MAAM,CAACW,MAAM,OAAAS,cAAA,OAC7B,IAAI,CAAAC,IAAI,EAAAD,cAAA,CAACT,MAAM,CAACH,KAAK,UAAAY,cAAA,iBAAZA,cAAA,CAAcE,UAAU,CAAC,EAAI,GAAI,CAAAD,IAAI,CAACzD,SAAS,CAAC,EAC7D,CAAC,CACL,CACA,GAAIE,OAAO,CAAE,CACT4C,QAAQ,CAAGA,QAAQ,CAACV,MAAM,CAACW,MAAM,OAAAY,cAAA,OAC7B,IAAI,CAAAF,IAAI,EAAAE,cAAA,CAACZ,MAAM,CAACH,KAAK,UAAAe,cAAA,iBAAZA,cAAA,CAAcD,UAAU,CAAC,EAAI,GAAI,CAAAD,IAAI,CAACvD,OAAO,CAAC,EAC3D,CAAC,CACL,CAEAG,kBAAkB,CAACyC,QAAQ,CAAC,CAChC,CAAC,CAAE,CAACtD,OAAO,CAAEI,UAAU,CAAEE,YAAY,CAAEE,SAAS,CAAEE,OAAO,CAAC,CAAC,CAE3D,KAAM,CAAA0D,cAAc,CAAIC,MAAM,EAAK,CAC/B,OAAQA,MAAM,EACV,IAAK,UAAU,CACX,mBAAO9E,IAAA,CAACjB,KAAK,EAACgG,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAExE,CAAC,CAAC,UAAU,CAAC,CAAQ,CAAC,CACtD,IAAK,SAAS,CACV,mBAAOR,IAAA,CAACjB,KAAK,EAACgG,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAExE,CAAC,CAAC,gBAAgB,CAAC,CAAQ,CAAC,CAC5D,IAAK,UAAU,CACX,mBAAOR,IAAA,CAACjB,KAAK,EAACgG,EAAE,CAAC,QAAQ,CAAAC,QAAA,CAAExE,CAAC,CAAC,UAAU,CAAC,CAAQ,CAAC,CACrD,IAAK,cAAc,CACf,mBAAOR,IAAA,CAACjB,KAAK,EAACgG,EAAE,CAAC,MAAM,CAAAC,QAAA,CAAExE,CAAC,CAAC,cAAc,CAAC,CAAQ,CAAC,CACvD,QACI,mBAAOR,IAAA,CAACjB,KAAK,EAACgG,EAAE,CAAC,WAAW,CAAAC,QAAA,CAAEF,MAAM,EAAItE,CAAC,CAAC,eAAe,CAAC,CAAQ,CAAC,CAC3E,CACJ,CAAC,CAED,KAAM,CAAAyE,YAAY,CAAGA,CAAA,GAAM,CACvB;AACAjC,OAAO,CAACkC,GAAG,CAAC,kBAAkB,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC1B;AACAC,KAAK,CAAC5E,CAAC,CAAC,wBAAwB,CAAC,CAAC,CACtC,CAAC,CAED,KAAM,CAAA6E,eAAe,CAAIrB,MAAM,EAAK,CAChChB,OAAO,CAACkC,GAAG,CAAC,iCAAiC,CAAElB,MAAM,CAAC,CACtDtC,iBAAiB,CAACsC,MAAM,CAAC,CACzBxC,eAAe,CAAC,IAAI,CAAC,CACrBM,WAAW,CAAC,EAAE,CAAC,CACfE,aAAa,CAAC,EAAE,CAAC,CACrB,CAAC,CAED,KAAM,CAAAsD,iBAAiB,CAAG,KAAO,CAAAC,QAAQ,EAAK,CAC1C,GAAI,CAAC9D,cAAc,CAAE,OAErBG,aAAa,CAAC,IAAI,CAAC,CACnBE,WAAW,CAAC,EAAE,CAAC,CACfE,aAAa,CAAC,EAAE,CAAC,CAEjB,GAAI,CACA,KAAM,CAAAE,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACqC,QAAQ,CAAE,CACX,KAAM,IAAI,CAAAsD,KAAK,CAAC,4BAA4B,CAAC,CACjD,CAEAxC,OAAO,CAACkC,GAAG,CAAC,+BAA+B,CAAEzD,cAAc,CAAC2B,OAAO,CAAE,KAAK,CAAEmC,QAAQ,CAAC,CAErF;AACA,KAAM,CAAEpD,IAAI,CAAEK,KAAM,CAAC,CAAG,KAAM,CAAAN,QAAQ,CACjCQ,IAAI,CAAC,mBAAmB,CAAC,CACzB+C,MAAM,CAAC,CAAEjB,aAAa,CAAEe,QAAS,CAAC,CAAC,CACnC3C,EAAE,CAAC,SAAS,CAAEnB,cAAc,CAAC2B,OAAO,CAAC,CACrCT,MAAM,CAAC,CAAC,CAEb,GAAIH,KAAK,CAAE,CACPQ,OAAO,CAACR,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,KAAM,CAAAA,KAAK,CACf,CAEAQ,OAAO,CAACkC,GAAG,CAAC,gBAAgB,CAAE/C,IAAI,CAAC,CAEnC;AACA,GAAI,CAACA,IAAI,EAAIA,IAAI,CAACuD,MAAM,GAAK,CAAC,CAAE,CAC5B;AACA1C,OAAO,CAACkC,GAAG,CAAC,+CAA+C,CAAC,CAE5D;AACA,KAAM,CAAE/C,IAAI,CAAEwD,cAAc,CAAEnD,KAAK,CAAEoD,WAAY,CAAC,CAAG,KAAM,CAAA1D,QAAQ,CAC9DQ,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,CAAEnB,cAAc,CAAC2B,OAAO,CAAC,CAE1C,GAAIwC,WAAW,CAAE,CACb5C,OAAO,CAACR,KAAK,CAAC,iCAAiC,CAAEoD,WAAW,CAAC,CAC7D,KAAM,CAAAA,WAAW,CACrB,CAEA5C,OAAO,CAACkC,GAAG,CAAC,kBAAkB,CAAES,cAAc,CAAC,CAE/C,GAAI,CAACA,cAAc,EAAIA,cAAc,CAACD,MAAM,GAAK,CAAC,CAAE,CAChD,KAAM,IAAI,CAAAF,KAAK,CAAC,4BAA4B,CAAC,CACjD,CACJ,CAEA;AACA9E,UAAU,CAACmF,WAAW,EAClBA,WAAW,CAAC3C,GAAG,CAACc,MAAM,EAClBA,MAAM,CAACZ,OAAO,GAAK3B,cAAc,CAAC2B,OAAO,CACnC,CAAE,GAAGY,MAAM,CAAEQ,aAAa,CAAEe,QAAS,CAAC,CACtCvB,MACV,CACJ,CAAC,CAED;AACAtC,iBAAiB,CAACoE,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEtB,aAAa,CAAEe,QAAS,CAAC,CAAC,CAAC,CAEjEvD,aAAa,CAACuD,QAAQ,GAAK,UAAU,CAAG/E,CAAC,CAAC,sBAAsB,CAAC,CAAGA,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAE9F;AACAuF,UAAU,CAAC,IAAM,CACbvE,eAAe,CAAC,KAAK,CAAC,CACtBE,iBAAiB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAAE,IAAI,CAAC,CAEZ,CAAE,MAAOc,KAAK,CAAE,CACZQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDV,WAAW,CAACU,KAAK,CAACwD,OAAO,EAAIxF,CAAC,CAAC,kBAAkB,CAAC,CAAC,CACvD,CAAC,OAAS,CACNoB,aAAa,CAAC,KAAK,CAAC,CACxB,CACJ,CAAC,CAED,KAAM,CAAAqE,aAAa,CAAGA,CAAA,GAAM,CACxBzE,eAAe,CAAC,KAAK,CAAC,CACtBE,iBAAiB,CAAC,IAAI,CAAC,CACvBI,WAAW,CAAC,EAAE,CAAC,CACfE,aAAa,CAAC,EAAE,CAAC,CACrB,CAAC,CAED,KAAM,CAAAkE,iBAAiB,CAAIC,QAAQ,EAAK,CACpC;AACAf,KAAK,CAAC5E,CAAC,CAAC,0BAA0B,CAAC,CAAC,CACxC,CAAC,CAED,KAAM,CAAA4F,iBAAiB,CAAID,QAAQ,EAAK,CACpC;AACAf,KAAK,CAAC5E,CAAC,CAAC,0BAA0B,CAAC,CAAC,CACxC,CAAC,CAED,GAAIG,OAAO,CAAE,CACT,mBAAOX,IAAA,QAAAgF,QAAA,CAAMxE,CAAC,CAAC,iBAAiB,CAAC,CAAM,CAAC,CAC5C,CAEA,mBACIN,KAAA,CAACxB,SAAS,EAAAsG,QAAA,eACNhF,IAAA,OAAIqG,SAAS,CAAC,MAAM,CAAArB,QAAA,CAAExE,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,cAG5CR,IAAA,CAACrB,GAAG,EAAC0H,SAAS,CAAC,MAAM,CAAArB,QAAA,cACjBhF,IAAA,CAACpB,GAAG,EAAAoG,QAAA,cACAhF,IAAA,CAACnB,IAAI,EAAAmG,QAAA,cACDhF,IAAA,CAACnB,IAAI,CAACyH,IAAI,EAAAtB,QAAA,cACN9E,KAAA,CAACvB,GAAG,EAAC0H,SAAS,CAAC,iBAAiB,CAAArB,QAAA,eAC5BhF,IAAA,CAACpB,GAAG,EAAC2H,EAAE,CAAE,CAAE,CAAAvB,QAAA,cACP9E,KAAA,CAAClB,MAAM,EACHwH,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEtB,eAAgB,CACzBkB,SAAS,CAAC,MAAM,CAAArB,QAAA,eAEhBhF,IAAA,CAACT,MAAM,EAAC8G,SAAS,CAAC,MAAM,CAAE,CAAC,CAC1B7F,CAAC,CAAC,YAAY,CAAC,EACZ,CAAC,CACR,CAAC,cACNR,IAAA,CAACpB,GAAG,EAAC2H,EAAE,CAAE,CAAE,CAAAvB,QAAA,cACP9E,KAAA,CAACjB,IAAI,CAACyH,KAAK,EAAA1B,QAAA,eACPhF,IAAA,CAACf,IAAI,CAAC0H,KAAK,EAAA3B,QAAA,CAAExE,CAAC,CAAC,iBAAiB,CAAC,CAAa,CAAC,cAC/CR,IAAA,CAACd,UAAU,EAAA8F,QAAA,cACPhF,IAAA,CAACf,IAAI,CAAC2H,OAAO,EACTC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAEtG,CAAC,CAAC,uBAAuB,CAAE,CACxCuG,KAAK,CAAElG,UAAW,CAClBmG,QAAQ,CAAGC,CAAC,EAAKnG,aAAa,CAACmG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClD,CAAC,CACM,CAAC,EACL,CAAC,CACZ,CAAC,cACN/G,IAAA,CAACpB,GAAG,EAAC2H,EAAE,CAAE,CAAE,CAAAvB,QAAA,cACP9E,KAAA,CAACjB,IAAI,CAACyH,KAAK,EAAA1B,QAAA,eACPhF,IAAA,CAACf,IAAI,CAAC0H,KAAK,EAAA3B,QAAA,CAAExE,CAAC,CAAC,eAAe,CAAC,CAAa,CAAC,cAC7CN,KAAA,CAACjB,IAAI,CAACkI,MAAM,EACRJ,KAAK,CAAEhG,YAAa,CACpBiG,QAAQ,CAAGC,CAAC,EAAKjG,eAAe,CAACiG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAA/B,QAAA,eAEjDhF,IAAA,WAAQ+G,KAAK,CAAC,EAAE,CAAA/B,QAAA,CAAExE,CAAC,CAAC,sBAAsB,CAAC,CAAS,CAAC,cACrDR,IAAA,WAAQ+G,KAAK,CAAC,SAAS,CAAA/B,QAAA,CAAExE,CAAC,CAAC,gBAAgB,CAAC,CAAS,CAAC,cACtDR,IAAA,WAAQ+G,KAAK,CAAC,UAAU,CAAA/B,QAAA,CAAExE,CAAC,CAAC,UAAU,CAAC,CAAS,CAAC,cACjDR,IAAA,WAAQ+G,KAAK,CAAC,UAAU,CAAA/B,QAAA,CAAExE,CAAC,CAAC,UAAU,CAAC,CAAS,CAAC,cACjDR,IAAA,WAAQ+G,KAAK,CAAC,cAAc,CAAA/B,QAAA,CAAExE,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,EAChD,CAAC,EACN,CAAC,CACZ,CAAC,cACNR,IAAA,CAACpB,GAAG,EAAC2H,EAAE,CAAE,CAAE,CAAAvB,QAAA,cACP9E,KAAA,CAACjB,IAAI,CAACyH,KAAK,EAAA1B,QAAA,eACPhF,IAAA,CAACf,IAAI,CAAC0H,KAAK,EAAA3B,QAAA,CAAExE,CAAC,CAAC,YAAY,CAAC,CAAa,CAAC,cAC1CR,IAAA,CAACf,IAAI,CAAC2H,OAAO,EACTC,IAAI,CAAC,MAAM,CACXE,KAAK,CAAE9F,SAAU,CACjB+F,QAAQ,CAAGC,CAAC,EAAK/F,YAAY,CAAC+F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjD,CAAC,EACM,CAAC,CACZ,CAAC,cACN/G,IAAA,CAACpB,GAAG,EAAC2H,EAAE,CAAE,CAAE,CAAAvB,QAAA,cACP9E,KAAA,CAACjB,IAAI,CAACyH,KAAK,EAAA1B,QAAA,eACPhF,IAAA,CAACf,IAAI,CAAC0H,KAAK,EAAA3B,QAAA,CAAExE,CAAC,CAAC,UAAU,CAAC,CAAa,CAAC,cACxCR,IAAA,CAACf,IAAI,CAAC2H,OAAO,EACTC,IAAI,CAAC,MAAM,CACXE,KAAK,CAAE5F,OAAQ,CACf6F,QAAQ,CAAGC,CAAC,EAAK7F,UAAU,CAAC6F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/C,CAAC,EACM,CAAC,CACZ,CAAC,cACN/G,IAAA,CAACpB,GAAG,EAAC2H,EAAE,CAAE,CAAE,CAAAvB,QAAA,cACPhF,IAAA,CAAChB,MAAM,EACHwH,OAAO,CAAC,iBAAiB,CACzBC,OAAO,CAAExB,YAAa,CACtBoB,SAAS,CAAC,MAAM,CAAArB,QAAA,cAEhBhF,IAAA,CAACV,QAAQ,GAAE,CAAC,CACR,CAAC,CACR,CAAC,EACL,CAAC,CACC,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAGNU,IAAA,CAACrB,GAAG,EAAAqG,QAAA,cACAhF,IAAA,CAACpB,GAAG,EAAAoG,QAAA,cACAhF,IAAA,CAACnB,IAAI,EAAAmG,QAAA,cACDhF,IAAA,CAACnB,IAAI,CAACyH,IAAI,EAAAtB,QAAA,cACN9E,KAAA,CAACpB,KAAK,EAACsI,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAvC,QAAA,eACpChF,IAAA,UAAAgF,QAAA,cACI9E,KAAA,OAAA8E,QAAA,eACIhF,IAAA,OAAAgF,QAAA,CAAKxE,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBR,IAAA,OAAAgF,QAAA,CAAKxE,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBR,IAAA,OAAAgF,QAAA,CAAKxE,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBR,IAAA,OAAAgF,QAAA,CAAKxE,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC9BR,IAAA,OAAAgF,QAAA,CAAKxE,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BR,IAAA,OAAAgF,QAAA,CAAKxE,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBR,IAAA,OAAAgF,QAAA,CAAKxE,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCR,IAAA,OAAAgF,QAAA,CAAKxE,CAAC,CAAC,SAAS,CAAC,CAAK,CAAC,EACvB,CAAC,CACF,CAAC,cACRR,IAAA,UAAAgF,QAAA,CACK3D,eAAe,CAACqE,MAAM,GAAK,CAAC,cACzB1F,IAAA,OAAAgF,QAAA,cACIhF,IAAA,OAAIwH,OAAO,CAAC,GAAG,CAACnB,SAAS,CAAC,aAAa,CAAArB,QAAA,CAAExE,CAAC,CAAC,kBAAkB,CAAC,CAAK,CAAC,CACpE,CAAC,CAELa,eAAe,CAAC6B,GAAG,CAACc,MAAM,OAAAyD,cAAA,CAAAC,cAAA,oBACtBxH,KAAA,OAAA8E,QAAA,eACIhF,IAAA,OAAAgF,QAAA,CAAK,EAAAyC,cAAA,CAAAzD,MAAM,CAACH,KAAK,UAAA4D,cAAA,iBAAZA,cAAA,CAAcrD,KAAK,GAAI,GAAG,CAAK,CAAC,cACrCpE,IAAA,OAAAgF,QAAA,CAAKhB,MAAM,CAACO,SAAS,EAAI,GAAG,CAAK,CAAC,cAClCvE,IAAA,OAAAgF,QAAA,CAAKhB,MAAM,CAAC2D,SAAS,EAAI,GAAG,CAAK,CAAC,cAClC3H,IAAA,OAAAgF,QAAA,CACKhB,MAAM,CAAC4D,YAAY,cAChB5H,IAAA,QACI6H,GAAG,CAAE7D,MAAM,CAAC4D,YAAa,CACzBE,GAAG,CAAC,UAAU,CACdC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,OAAO,CAClBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SACZ,CAAE,CACF3B,OAAO,CAAEA,CAAA,GAAM4B,MAAM,CAACC,IAAI,CAACtE,MAAM,CAAC4D,YAAY,CAAE,QAAQ,CAAE,CAC7D,CAAC,cAEF5H,IAAA,SAAMqG,SAAS,CAAC,YAAY,CAAArB,QAAA,CAAC,GAAC,CAAM,CACvC,CACD,CAAC,cACLhF,IAAA,OAAAgF,QAAA,CACKhB,MAAM,CAACuE,WAAW,cACfvI,IAAA,QACI6H,GAAG,CAAE7D,MAAM,CAACuE,WAAY,CACxBT,GAAG,CAAC,SAAS,CACbC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,OAAO,CAClBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SACZ,CAAE,CACF3B,OAAO,CAAEA,CAAA,GAAM4B,MAAM,CAACC,IAAI,CAACtE,MAAM,CAACuE,WAAW,CAAE,QAAQ,CAAE,CAC5D,CAAC,cAEFvI,IAAA,SAAMqG,SAAS,CAAC,YAAY,CAAArB,QAAA,CAAC,GAAC,CAAM,CACvC,CACD,CAAC,cACLhF,IAAA,OAAAgF,QAAA,CAAKH,cAAc,CAACb,MAAM,CAACQ,aAAa,CAAC,CAAK,CAAC,cAC/CxE,IAAA,OAAAgF,QAAA,CAAK,CAAA0C,cAAA,CAAA1D,MAAM,CAACH,KAAK,UAAA6D,cAAA,WAAZA,cAAA,CAAc/C,UAAU,CAAG,GAAI,CAAAD,IAAI,CAACV,MAAM,CAACH,KAAK,CAACc,UAAU,CAAC,CAAC6D,cAAc,CAAC,CAAC,CAAG,GAAG,CAAK,CAAC,cAC9FxI,IAAA,OAAAgF,QAAA,cACI9E,KAAA,QAAKmG,SAAS,CAAC,cAAc,CAAArB,QAAA,eACzBhF,IAAA,CAAChB,MAAM,EACHyJ,IAAI,CAAC,IAAI,CACTjC,OAAO,CAAC,iBAAiB,CACzBC,OAAO,CAAEA,CAAA,GAAMpB,eAAe,CAACrB,MAAM,CAAE,CACvC0E,KAAK,CAAElI,CAAC,CAAC,YAAY,CAAE,CAAAwE,QAAA,cAEvBhF,IAAA,CAACP,WAAW,GAAE,CAAC,CACX,CAAC,cACTO,IAAA,CAAChB,MAAM,EACHyJ,IAAI,CAAC,IAAI,CACTjC,OAAO,CAAC,iBAAiB,CACzBC,OAAO,CAAEA,CAAA,GAAMP,iBAAiB,CAAClC,MAAM,CAACZ,OAAO,CAAE,CACjDsF,KAAK,CAAElI,CAAC,CAAC,cAAc,CAAE,CAAAwE,QAAA,cAEzBhF,IAAA,CAACN,aAAa,GAAE,CAAC,CACb,CAAC,cACTM,IAAA,CAAChB,MAAM,EACHyJ,IAAI,CAAC,IAAI,CACTjC,OAAO,CAAC,cAAc,CACtBC,OAAO,CAAEA,CAAA,GAAML,iBAAiB,CAACpC,MAAM,CAACZ,OAAO,CAAE,CACjDsF,KAAK,CAAElI,CAAC,CAAC,cAAc,CAAE,CAAAwE,QAAA,cAEzBhF,IAAA,CAACR,KAAK,GAAE,CAAC,CACL,CAAC,EACR,CAAC,CACN,CAAC,GArEAwE,MAAM,CAACZ,OAsEZ,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAGNlD,KAAA,CAACd,KAAK,EAACuJ,IAAI,CAAEpH,YAAa,CAACqH,MAAM,CAAE3C,aAAc,CAACwC,IAAI,CAAC,IAAI,CAAAzD,QAAA,eACvDhF,IAAA,CAACZ,KAAK,CAACyJ,MAAM,EAACC,WAAW,MAACzC,SAAS,CAAC,qBAAqB,CAAArB,QAAA,cACrDhF,IAAA,CAACZ,KAAK,CAAC2J,KAAK,EAAA/D,QAAA,CAAExE,CAAC,CAAC,YAAY,CAAC,CAAc,CAAC,CAClC,CAAC,cACfR,IAAA,UAAOgJ,uBAAuB,CAAE,CAC5BC,MAAM,CAAE;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBACgB,CAAE,CAAE,CAAC,cACLjJ,IAAA,CAACZ,KAAK,CAACkH,IAAI,EAAAtB,QAAA,CACNvD,cAAc,eACXvB,KAAA,CAAAE,SAAA,EAAA4E,QAAA,EACKnD,QAAQ,eACL7B,IAAA,CAACX,KAAK,EAACmH,OAAO,CAAC,QAAQ,CAACH,SAAS,CAAC,MAAM,CAAArB,QAAA,CACnCnD,QAAQ,CACN,CACV,CACAE,UAAU,eACP/B,IAAA,CAACX,KAAK,EAACmH,OAAO,CAAC,SAAS,CAACH,SAAS,CAAC,MAAM,CAAArB,QAAA,CACpCjD,UAAU,CACR,CACV,cAED7B,KAAA,CAACvB,GAAG,EAAAqG,QAAA,eACAhF,IAAA,CAACpB,GAAG,EAAC2H,EAAE,CAAE,CAAE,CAAAvB,QAAA,cACP9E,KAAA,CAACrB,IAAI,EAACwH,SAAS,CAAC,MAAM,CAAArB,QAAA,eAClBhF,IAAA,CAACnB,IAAI,CAACgK,MAAM,EAAA7D,QAAA,cACRhF,IAAA,WAAAgF,QAAA,CAASxE,CAAC,CAAC,eAAe,CAAC,CAAS,CAAC,CAC5B,CAAC,cACdN,KAAA,CAACrB,IAAI,CAACyH,IAAI,EAAAtB,QAAA,eACN9E,KAAA,MAAA8E,QAAA,eAAG9E,KAAA,WAAA8E,QAAA,EAASxE,CAAC,CAAC,UAAU,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC,EAAAF,qBAAA,CAAAmB,cAAc,CAACoC,KAAK,UAAAvD,qBAAA,iBAApBA,qBAAA,CAAsB8D,KAAK,GAAI,GAAG,EAAI,CAAC,cAC7ElE,KAAA,MAAA8E,QAAA,eAAG9E,KAAA,WAAA8E,QAAA,EAASxE,CAAC,CAAC,WAAW,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACiB,cAAc,CAAC8C,SAAS,EAAI,GAAG,EAAI,CAAC,cAC3ErE,KAAA,MAAA8E,QAAA,eAAG9E,KAAA,WAAA8E,QAAA,EAASxE,CAAC,CAAC,WAAW,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACiB,cAAc,CAACkG,SAAS,EAAI,GAAG,EAAI,CAAC,cAC3EzH,KAAA,MAAA8E,QAAA,eAAG9E,KAAA,WAAA8E,QAAA,EAASxE,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACqE,cAAc,CAACpD,cAAc,CAAC+C,aAAa,CAAC,EAAI,CAAC,cAC7FtE,KAAA,MAAA8E,QAAA,eAAG9E,KAAA,WAAA8E,QAAA,EAASxE,CAAC,CAAC,mBAAmB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC,CAAAD,sBAAA,CAAAkB,cAAc,CAACoC,KAAK,UAAAtD,sBAAA,WAApBA,sBAAA,CAAsBoE,UAAU,CAAG,GAAI,CAAAD,IAAI,CAACjD,cAAc,CAACoC,KAAK,CAACc,UAAU,CAAC,CAAC6D,cAAc,CAAC,CAAC,CAAG,GAAG,EAAI,CAAC,EAChJ,CAAC,EACV,CAAC,CACN,CAAC,cACNxI,IAAA,CAACpB,GAAG,EAAC2H,EAAE,CAAE,CAAE,CAAAvB,QAAA,cACP9E,KAAA,CAACrB,IAAI,EAACwH,SAAS,CAAC,MAAM,CAAArB,QAAA,eAClBhF,IAAA,CAACnB,IAAI,CAACgK,MAAM,EAAA7D,QAAA,cACRhF,IAAA,WAAAgF,QAAA,CAASxE,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAC3B,CAAC,cACdN,KAAA,CAACrB,IAAI,CAACyH,IAAI,EAAAtB,QAAA,eACN9E,KAAA,QAAKmG,SAAS,CAAC,MAAM,CAAArB,QAAA,eACjB9E,KAAA,WAAA8E,QAAA,EAASxE,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAC,EAAQ,CAAC,cACvCR,IAAA,QAAKqG,SAAS,CAAC,MAAM,CAAArB,QAAA,CAChBvD,cAAc,CAACmG,YAAY,cACxB5H,IAAA,QACI6H,GAAG,CAAEpG,cAAc,CAACmG,YAAa,CACjCE,GAAG,CAAC,UAAU,CACdC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbkB,SAAS,CAAE,OAAO,CAClBhB,SAAS,CAAE,SAAS,CACpBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBe,MAAM,CAAE,mBACZ,CAAE,CACF1C,OAAO,CAAEA,CAAA,GAAM4B,MAAM,CAACC,IAAI,CAAC7G,cAAc,CAACmG,YAAY,CAAE,QAAQ,CAAE,CACrE,CAAC,cAEF5H,IAAA,QAAKqG,SAAS,CAAC,6BAA6B,CAAC0B,KAAK,CAAE,CAAEoB,MAAM,CAAE,oBAAoB,CAAEhB,YAAY,CAAE,KAAM,CAAE,CAAAnD,QAAA,CACrGxE,CAAC,CAAC,mBAAmB,CAAC,CACtB,CACR,CACA,CAAC,EACL,CAAC,cACNN,KAAA,QAAKmG,SAAS,CAAC,MAAM,CAAArB,QAAA,eACjB9E,KAAA,WAAA8E,QAAA,EAASxE,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,cACtCR,IAAA,QAAKqG,SAAS,CAAC,MAAM,CAAArB,QAAA,CAChBvD,cAAc,CAAC8G,WAAW,cACvBvI,IAAA,QACI6H,GAAG,CAAEpG,cAAc,CAAC8G,WAAY,CAChCT,GAAG,CAAC,SAAS,CACbC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbkB,SAAS,CAAE,OAAO,CAClBhB,SAAS,CAAE,SAAS,CACpBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBe,MAAM,CAAE,mBACZ,CAAE,CACF1C,OAAO,CAAEA,CAAA,GAAM4B,MAAM,CAACC,IAAI,CAAC7G,cAAc,CAAC8G,WAAW,CAAE,QAAQ,CAAE,CACpE,CAAC,cAEFvI,IAAA,QAAKqG,SAAS,CAAC,6BAA6B,CAAC0B,KAAK,CAAE,CAAEoB,MAAM,CAAE,oBAAoB,CAAEhB,YAAY,CAAE,KAAM,CAAE,CAAAnD,QAAA,CACrGxE,CAAC,CAAC,mBAAmB,CAAC,CACtB,CACR,CACA,CAAC,EACL,CAAC,EACC,CAAC,EACV,CAAC,CACN,CAAC,EACL,CAAC,EACR,CACL,CACO,CAAC,cACbN,KAAA,CAACd,KAAK,CAACgK,MAAM,EAAApE,QAAA,eACThF,IAAA,CAAChB,MAAM,EAACwH,OAAO,CAAC,WAAW,CAACC,OAAO,CAAER,aAAc,CAACoD,QAAQ,CAAE1H,UAAW,CAAAqD,QAAA,CACpExE,CAAC,CAAC,QAAQ,CAAC,CACR,CAAC,cACTR,IAAA,CAAChB,MAAM,EACHwH,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAEA,CAAA,GAAMnB,iBAAiB,CAAC,UAAU,CAAE,CAC7C+D,QAAQ,CAAE1H,UAAU,EAAI,CAAAF,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE+C,aAAa,IAAK,UAAW,CAAAQ,QAAA,CAEpErD,UAAU,cACPzB,KAAA,CAAAE,SAAA,EAAA4E,QAAA,eACIhF,IAAA,SAAMqG,SAAS,CAAC,uCAAuC,CAACiD,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAO,CAAC,CAC/F9I,CAAC,CAAC,YAAY,CAAC,EAClB,CAAC,cAEHN,KAAA,CAAAE,SAAA,EAAA4E,QAAA,eACIhF,IAAA,CAACJ,OAAO,EAACyG,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B7F,CAAC,CAAC,QAAQ,CAAC,EACd,CACL,CACG,CAAC,cACTR,IAAA,CAAChB,MAAM,EACHwH,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,CAAA,GAAMnB,iBAAiB,CAAC,UAAU,CAAE,CAC7C+D,QAAQ,CAAE1H,UAAU,EAAI,CAAAF,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE+C,aAAa,IAAK,UAAW,CAAAQ,QAAA,CAEpErD,UAAU,cACPzB,KAAA,CAAAE,SAAA,EAAA4E,QAAA,eACIhF,IAAA,SAAMqG,SAAS,CAAC,uCAAuC,CAACiD,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAO,CAAC,CAC/F9I,CAAC,CAAC,YAAY,CAAC,EAClB,CAAC,cAEHN,KAAA,CAAAE,SAAA,EAAA4E,QAAA,eACIhF,IAAA,CAACL,OAAO,EAAC0G,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B7F,CAAC,CAAC,SAAS,CAAC,EACf,CACL,CACG,CAAC,EACC,CAAC,EACZ,CAAC,EACD,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}