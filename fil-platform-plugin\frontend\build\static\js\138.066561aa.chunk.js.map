{"version": 3, "file": "static/js/138.066561aa.chunk.js", "mappings": "oSAMA,MAs7BA,EAt7BgBA,KAAO,IAADC,EAAAC,EAAAC,EAClB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAASC,IAAcC,EAAAA,EAAAA,UAAS,KAChCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,KACtCK,EAAcC,IAAmBN,EAAAA,EAAAA,UAAS,KAC1CO,EAAWC,IAAgBR,EAAAA,EAAAA,UAAS,KACpCS,EAASC,IAAcV,EAAAA,EAAAA,UAAS,KAChCW,EAAiBC,IAAsBZ,EAAAA,EAAAA,UAAS,KAChDa,EAAcC,IAAmBd,EAAAA,EAAAA,WAAS,IAC1Ce,EAAgBC,IAAqBhB,EAAAA,EAAAA,UAAS,OAC9CiB,EAAYC,IAAiBlB,EAAAA,EAAAA,WAAS,IACtCmB,EAAUC,IAAepB,EAAAA,EAAAA,UAAS,KAClCqB,EAAYC,IAAiBtB,EAAAA,EAAAA,UAAS,KAGtCuB,EAAsBC,IAA2BxB,EAAAA,EAAAA,WAAS,IAC1DyB,EAAmBC,IAAwB1B,EAAAA,EAAAA,UAAS,OACpD2B,EAAiBC,IAAsB5B,EAAAA,EAAAA,UAAS,KAChD6B,EAAkBC,IAAuB9B,EAAAA,EAAAA,UAAS,KAClD+B,EAAoBC,IAAyBhC,EAAAA,EAAAA,WAAS,IACtDiC,GAAkBC,KAAuBlC,EAAAA,EAAAA,UAAS,KAClDmC,GAAoBC,KAAyBpC,EAAAA,EAAAA,UAAS,KAGtDqC,GAAoBC,KAAyBtC,EAAAA,EAAAA,WAAS,IACtDuC,GAAgBC,KAAqBxC,EAAAA,EAAAA,UAAS,KAC9CyC,GAAmBC,KAAwB1C,EAAAA,EAAAA,UAAS,KACpD2C,GAAqBC,KAA0B5C,EAAAA,EAAAA,UAAS,KACxD6C,GAAkBC,KAAuB9C,EAAAA,EAAAA,WAAS,IAClD+C,GAAgBC,KAAqBhD,EAAAA,EAAAA,UAAS,KAC9CiD,GAAkBC,KAAuBlD,EAAAA,EAAAA,UAAS,KAEzDmD,EAAAA,EAAAA,WAAU,KACmBC,WACjB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfnD,GAAW,GACX,MAAQqD,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAtD,GAAW,GAKf,MAAQqD,KAAMI,EAAWC,MAAOC,SAAuBR,EAClDS,KAAK,qBACLC,OAAO,2EACPC,GAAG,WAAYR,EAAKS,IACpBC,MAAM,aAAc,CAAEC,WAAW,IAEtC,GAAIN,IAAiBF,EAGjB,OAFAS,QAAQR,MAAM,oCAAqCC,QACnD3D,GAAW,GAKf,MAAMmE,EAAUV,EAAUW,IAAIC,GAAKA,EAAEC,SAASC,OAAOC,UAE7CnB,KAAMoB,EAAcf,MAAOgB,SAAoBvB,EAClDS,KAAK,SACLC,OAAO,yBACPc,GAAG,KAAMR,GAEVO,GACAR,QAAQR,MAAM,wBAAyBgB,GAI3C,MAAME,EAAW,IAAIC,KAAKJ,GAAgB,IAAIL,IAAIU,GAAK,CAACA,EAAEf,GAAIe,KAExDC,EAAkBtB,EAAUW,IAAIC,IAAC,IAChCA,EACHW,MAAOJ,EAASK,IAAIZ,EAAEC,UAAY,CAAC,KAGvCzE,EAAWkF,GACX/E,GAAW,IAGnBkF,IACD,KAGHjC,EAAAA,EAAAA,WAAU,KACN,IAAIkC,EAAWvF,EAGXK,IACAkF,EAAWA,EAASZ,OAAOa,IAAM,IAAAC,EAAAC,EAAAC,EAAA,OACjB,QAAZF,EAAAD,EAAOJ,aAAK,IAAAK,GAAO,QAAPC,EAAZD,EAAcG,aAAK,IAAAF,OAAP,EAAZA,EAAqBG,cAAcC,SAASzF,EAAWwF,kBACvC,QADqDF,EACrEH,EAAOO,iBAAS,IAAAJ,OAAA,EAAhBA,EAAkBE,cAAcC,SAASzF,EAAWwF,mBAKxDtF,IACAgF,EAAWA,EAASZ,OAAOa,GAAUA,EAAOQ,gBAAkBzF,IAI9DE,IACA8E,EAAWA,EAASZ,OAAOa,IAAM,IAAAS,EAAA,OAC7B,IAAIC,KAAiB,QAAbD,EAACT,EAAOJ,aAAK,IAAAa,OAAA,EAAZA,EAAcE,aAAe,IAAID,KAAKzF,MAGnDE,IACA4E,EAAWA,EAASZ,OAAOa,IAAM,IAAAY,EAAA,OAC7B,IAAIF,KAAiB,QAAbE,EAACZ,EAAOJ,aAAK,IAAAgB,OAAA,EAAZA,EAAcD,aAAe,IAAID,KAAKvF,MAIvDG,EAAmByE,IACpB,CAACvF,EAASK,EAAYE,EAAcE,EAAWE,IAGlD,MAiCM0F,GAAkBC,IACpB,OAAQA,GACJ,IAAK,WACD,OAAOC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,GAAG,UAASC,SAAE5G,EAAE,cAClC,IAAK,UACD,OAAOyG,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,GAAG,UAASC,SAAE5G,EAAE,oBAClC,IAAK,WACD,OAAOyG,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,GAAG,SAAQC,SAAE5G,EAAE,cACjC,IAAK,eACD,OAAOyG,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,GAAG,OAAMC,SAAE5G,EAAE,kBAC/B,QACI,OAAOyG,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,GAAG,YAAWC,SAAEJ,GAAUxG,EAAE,qBAoGhD6G,GAAsBA,KACxBnE,IAAsB,GACtBE,GAAkB,IAClBE,GAAqB,IACrBE,GAAuB,IACvBI,GAAkB,IAClBE,GAAoB,KAUlBwD,GAAoBtD,UACtB,GAAKrC,EAAL,CAEAG,GAAc,GACdE,EAAY,IACZE,EAAc,IAEd,IACI,MAAM+B,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EACD,MAAM,IAAIsD,MAAM,8BAGpB,MAAM,KAAEpD,EAAI,MAAEK,SAAgBP,EACzBS,KAAK,qBACL8C,OAAO,CAAEd,cAAee,IACxB7C,GAAG,UAAWjD,EAAeyD,SAC7BT,SAEL,GAAIH,EAEA,MADAQ,QAAQR,MAAM,kBAAmBA,GAC3BA,EAIV,IAAKL,GAAwB,IAAhBA,EAAKuD,OAAc,CAG5B,MAAQvD,KAAMwD,EAAgBnD,MAAOoD,SAAsB3D,EACtDS,KAAK,qBACLC,OAAO,KACPC,GAAG,UAAWjD,EAAeyD,SAElC,GAAIwC,EAEA,MADA5C,QAAQR,MAAM,kCAAmCoD,GAC3CA,EAGV,IAAKD,GAA4C,IAA1BA,EAAeD,OAClC,MAAM,IAAIH,MAAM,6BAExB,CAGA5G,EAAWkH,GACPA,EAAY3C,IAAIgB,GACZA,EAAOd,UAAYzD,EAAeyD,QAC5B,IAAKc,EAAQQ,cAAee,GAC5BvB,IAKdtE,EAAkBkG,IAAI,IAAUA,EAAMpB,cAAee,KAErDvF,EAAwC1B,EAAb,aAAbiH,EAA4B,uBAA4B,yBAGtEM,WAAW,KACPrG,GAAgB,GAChBE,EAAkB,OACnB,KAEP,CAAE,MAAO4C,GACLQ,QAAQR,MAAM,6BAA8BA,GAC5CxC,EAAYwC,EAAMwD,SAAWxH,EAAE,oBACnC,CAAC,QACGsB,GAAc,EAClB,CAnE2B,GAsEzBmG,GAAgBA,KAClBvG,GAAgB,GAChBE,EAAkB,MAClBI,EAAY,IACZE,EAAc,KAGZgG,GAAoBlE,UACtB1B,EAAqB4D,GACrBpD,GAAoB,IACpBE,GAAsB,IACtBN,EAAoB,SAlPKsB,WACzB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,GAAKD,EAEL,IAEI,MAAQE,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAC/C,IAAKF,EAAM,OAGX,MAAQD,KAAMgE,EAAM,MAAE3D,SAAgBP,EACjCS,KAAK,kBACLC,OAAO,qNAQPyD,IAAI,UAAWhE,EAAKS,IAEzB,GAAIL,EAEA,YADAQ,QAAQR,MAAM,yBAA0BA,GAI5ChC,EAAmB2F,GAAU,GACjC,CAAE,MAAO3D,GACLQ,QAAQR,MAAM,iCAAkCA,EACpD,GAuNM6D,GAENjG,GAAwB,IAyDtBkG,GAAwBA,KAC1BlG,GAAwB,GACxBE,EAAqB,MACrBI,EAAoB,IACpBI,GAAoB,IACpBE,GAAsB,KAI1B,OAAInC,GACOoG,EAAAA,EAAAA,KAAA,OAAAG,SAAM5G,EAAE,sBAIf+H,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAApB,SAAA,EACNH,EAAAA,EAAAA,KAAA,MAAIwB,UAAU,OAAMrB,SAAE5G,EAAE,kBAGxByG,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAACD,UAAU,OAAMrB,UACjBH,EAAAA,EAAAA,KAAC0B,EAAAA,EAAG,CAAAvB,UACAH,EAAAA,EAAAA,KAAC2B,EAAAA,EAAI,CAAAxB,UACDH,EAAAA,EAAAA,KAAC2B,EAAAA,EAAKC,KAAI,CAAAzB,UACNmB,EAAAA,EAAAA,MAACG,EAAAA,EAAG,CAACD,UAAU,kBAAiBrB,SAAA,EAC5BH,EAAAA,EAAAA,KAAC0B,EAAAA,EAAG,CAACG,GAAI,EAAE1B,UACPmB,EAAAA,EAAAA,MAACQ,EAAAA,EAAM,CACHC,QAAQ,UACRC,QArRZC,KACpBhG,IAAsB,GACtBE,GAAkB,IAClBE,GAAqB,IACrBE,GAAuB,IACvBI,GAAkB,IAClBE,GAAoB,KAgRY2E,UAAU,OAAMrB,SAAA,EAEhBH,EAAAA,EAAAA,KAACkC,EAAAA,IAAM,CAACV,UAAU,SACjBjI,EAAE,oBAGXyG,EAAAA,EAAAA,KAAC0B,EAAAA,EAAG,CAACG,GAAI,EAAE1B,UACPmB,EAAAA,EAAAA,MAACa,EAAAA,EAAKC,MAAK,CAAAjC,SAAA,EACPH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,SAAE5G,EAAE,sBACfyG,EAAAA,EAAAA,KAACsC,EAAAA,EAAU,CAAAnC,UACPH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKI,QAAO,CACTC,KAAK,OACLC,YAAalJ,EAAE,yBACfmJ,MAAO5I,EACP6I,SAAWC,GAAM7I,EAAc6I,EAAEC,OAAOH,iBAKxD1C,EAAAA,EAAAA,KAAC0B,EAAAA,EAAG,CAACG,GAAI,EAAE1B,UACPmB,EAAAA,EAAAA,MAACa,EAAAA,EAAKC,MAAK,CAAAjC,SAAA,EACPH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,SAAE5G,EAAE,oBACf+H,EAAAA,EAAAA,MAACa,EAAAA,EAAKW,OAAM,CACRJ,MAAO1I,EACP2I,SAAWC,GAAM3I,EAAgB2I,EAAEC,OAAOH,OAAOvC,SAAA,EAEjDH,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,GAAEvC,SAAE5G,EAAE,2BACpByG,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,UAASvC,SAAE5G,EAAE,qBAC3ByG,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,WAAUvC,SAAE5G,EAAE,eAC5ByG,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,WAAUvC,SAAE5G,EAAE,eAC5ByG,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,eAAcvC,SAAE5G,EAAE,2BAI5CyG,EAAAA,EAAAA,KAAC0B,EAAAA,EAAG,CAACG,GAAI,EAAE1B,UACPmB,EAAAA,EAAAA,MAACa,EAAAA,EAAKC,MAAK,CAAAjC,SAAA,EACPH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,SAAE5G,EAAE,iBACfyG,EAAAA,EAAAA,KAACmC,EAAAA,EAAKI,QAAO,CACTC,KAAK,OACLE,MAAOxI,EACPyI,SAAWC,GAAMzI,EAAayI,EAAEC,OAAOH,eAInD1C,EAAAA,EAAAA,KAAC0B,EAAAA,EAAG,CAACG,GAAI,EAAE1B,UACPmB,EAAAA,EAAAA,MAACa,EAAAA,EAAKC,MAAK,CAAAjC,SAAA,EACPH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,SAAE5G,EAAE,eACfyG,EAAAA,EAAAA,KAACmC,EAAAA,EAAKI,QAAO,CACTC,KAAK,OACLE,MAAOtI,EACPuI,SAAWC,GAAMvI,EAAWuI,EAAEC,OAAOH,eAIjD1C,EAAAA,EAAAA,KAAC0B,EAAAA,EAAG,CAACG,GAAI,EAAE1B,UACPH,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CACHC,QAAQ,kBACRC,QApVfe,KAEjBhF,QAAQiF,IAAI,qBAmVoBxB,UAAU,OAAMrB,UAEhBH,EAAAA,EAAAA,KAACiD,EAAAA,IAAQ,oBAUrCjD,EAAAA,EAAAA,KAACyB,EAAAA,EAAG,CAAAtB,UACAH,EAAAA,EAAAA,KAAC0B,EAAAA,EAAG,CAAAvB,UACAH,EAAAA,EAAAA,KAAC2B,EAAAA,EAAI,CAAAxB,UACDH,EAAAA,EAAAA,KAAC2B,EAAAA,EAAKC,KAAI,CAAAzB,UACNmB,EAAAA,EAAAA,MAAC4B,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAAnD,SAAA,EACpCH,EAAAA,EAAAA,KAAA,SAAAG,UACImB,EAAAA,EAAAA,MAAA,MAAAnB,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,SAAK5G,EAAE,eACPyG,EAAAA,EAAAA,KAAA,MAAAG,SAAK5G,EAAE,gBACPyG,EAAAA,EAAAA,KAAA,MAAAG,SAAK5G,EAAE,gBACPyG,EAAAA,EAAAA,KAAA,MAAAG,SAAK5G,EAAE,qBACPyG,EAAAA,EAAAA,KAAA,MAAAG,SAAK5G,EAAE,oBACPyG,EAAAA,EAAAA,KAAA,MAAAG,SAAK5G,EAAE,aACPyG,EAAAA,EAAAA,KAAA,MAAAG,SAAK5G,EAAE,wBACPyG,EAAAA,EAAAA,KAAA,MAAAG,SAAK5G,EAAE,mBAGfyG,EAAAA,EAAAA,KAAA,SAAAG,SACgC,IAA3B7F,EAAgBmG,QACbT,EAAAA,EAAAA,KAAA,MAAAG,UACIH,EAAAA,EAAAA,KAAA,MAAIuD,QAAQ,IAAI/B,UAAU,cAAarB,SAAE5G,EAAE,wBAG/Ce,EAAgB2D,IAAIgB,IAAM,IAAAuE,EAAAC,EAAA,OACtBnC,EAAAA,EAAAA,MAAA,MAAAnB,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAiB,QAAZqD,EAAAvE,EAAOJ,aAAK,IAAA2E,OAAA,EAAZA,EAAcnE,QAAS,OAC5BW,EAAAA,EAAAA,KAAA,MAAAG,SAAKlB,EAAOO,WAAa,OACzBQ,EAAAA,EAAAA,KAAA,MAAAG,SAAKlB,EAAOyE,WAAa,OACzB1D,EAAAA,EAAAA,KAAA,MAAAG,SACKlB,EAAO0E,cACJ3D,EAAAA,EAAAA,KAAA,OACI4D,IAAK3E,EAAO0E,aACZE,IAAI,WACJC,MAAO,CACHC,MAAO,OACPC,OAAQ,OACRC,UAAW,QACXC,aAAc,MACdC,OAAQ,WAEZnC,QAASA,IAAMoC,OAAOC,KAAKpF,EAAO0E,aAAc,aAGpD3D,EAAAA,EAAAA,KAAA,QAAMwB,UAAU,aAAYrB,SAAC,SAGrCH,EAAAA,EAAAA,KAAA,MAAAG,SACKlB,EAAOqF,aACJtE,EAAAA,EAAAA,KAAA,OACI4D,IAAK3E,EAAOqF,YACZT,IAAI,UACJC,MAAO,CACHC,MAAO,OACPC,OAAQ,OACRC,UAAW,QACXC,aAAc,MACdC,OAAQ,WAEZnC,QAASA,IAAMoC,OAAOC,KAAKpF,EAAOqF,YAAa,aAGnDtE,EAAAA,EAAAA,KAAA,QAAMwB,UAAU,aAAYrB,SAAC,SAGrCH,EAAAA,EAAAA,KAAA,MAAAG,SAAKL,GAAeb,EAAOQ,kBAC3BO,EAAAA,EAAAA,KAAA,MAAAG,SAAiB,QAAZsD,EAAAxE,EAAOJ,aAAK,IAAA4E,GAAZA,EAAc7D,WAAa,IAAID,KAAKV,EAAOJ,MAAMe,YAAY2E,iBAAmB,OACrFvE,EAAAA,EAAAA,KAAA,MAAAG,UACImB,EAAAA,EAAAA,MAAA,OAAKE,UAAU,iCAAgCrB,SAAA,EAC3CH,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CACH0C,KAAK,KACLzC,QAAQ,kBACRC,QAASA,IA/TxC/C,KACrBtE,EAAkBsE,GAClBxE,GAAgB,GAChBM,EAAY,IACZE,EAAc,KA2TqDwJ,CAAgBxF,GAC/ByF,MAAOnL,EAAE,cAAc4G,UAEvBH,EAAAA,EAAAA,KAAC2E,EAAAA,IAAW,OAEhB3E,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CACH0C,KAAK,KACLzC,QAAQ,kBACRC,QAASA,IAAMf,GAAkBhC,GACjCyF,MAAOnL,EAAE,gBAAgB4G,UAEzBH,EAAAA,EAAAA,KAAC4E,EAAAA,IAAa,aA1DrB3F,EAAOd,yBAyEhDmD,EAAAA,EAAAA,MAACuD,EAAAA,EAAK,CAACC,KAAMtK,EAAcuK,OAAQ/D,GAAewD,KAAK,KAAIrE,SAAA,EACvDH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMG,OAAM,CAACC,aAAW,EAACzD,UAAU,sBAAqBrB,UACrDH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMK,MAAK,CAAA/E,SAAE5G,EAAE,mBAEpByG,EAAAA,EAAAA,KAAA,SAAOmF,wBAAyB,CAC5BC,OAAQ,wjBAaZpF,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMjD,KAAI,CAAAzB,SACNzF,IACG4G,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAAlF,SAAA,CACKrF,IACGkF,EAAAA,EAAAA,KAACsF,EAAAA,EAAK,CAACvD,QAAQ,SAASP,UAAU,OAAMrB,SACnCrF,IAGRE,IACGgF,EAAAA,EAAAA,KAACsF,EAAAA,EAAK,CAACvD,QAAQ,UAAUP,UAAU,OAAMrB,SACpCnF,KAITsG,EAAAA,EAAAA,MAACG,EAAAA,EAAG,CAAAtB,SAAA,EACAH,EAAAA,EAAAA,KAAC0B,EAAAA,EAAG,CAACG,GAAI,EAAE1B,UACPmB,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACH,UAAU,OAAMrB,SAAA,EAClBH,EAAAA,EAAAA,KAAC2B,EAAAA,EAAKqD,OAAM,CAAA7E,UACRH,EAAAA,EAAAA,KAAA,UAAAG,SAAS5G,EAAE,sBAEf+H,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,KAAI,CAAAzB,SAAA,EACNmB,EAAAA,EAAAA,MAAA,KAAAnB,SAAA,EAAGmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAS5G,EAAE,YAAY,OAAU,KAAsB,QAApBH,EAAAsB,EAAemE,aAAK,IAAAzF,OAAA,EAApBA,EAAsBiG,QAAS,QACrEiC,EAAAA,EAAAA,MAAA,KAAAnB,SAAA,EAAGmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAS5G,EAAE,aAAa,OAAU,IAAEmB,EAAe8E,WAAa,QACnE8B,EAAAA,EAAAA,MAAA,KAAAnB,SAAA,EAAGmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAS5G,EAAE,aAAa,OAAU,IAAEmB,EAAegJ,WAAa,QACnEpC,EAAAA,EAAAA,MAAA,KAAAnB,SAAA,EAAGmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAS5G,EAAE,kBAAkB,OAAU,IAAEuG,GAAepF,EAAe+E,mBAC1E6B,EAAAA,EAAAA,MAAA,KAAAnB,SAAA,EAAGmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAS5G,EAAE,qBAAqB,OAAU,IAAsB,QAApBF,EAAAqB,EAAemE,aAAK,IAAAxF,GAApBA,EAAsBuG,WAAa,IAAID,KAAKjF,EAAemE,MAAMe,YAAY2E,iBAAmB,gBAI3JvE,EAAAA,EAAAA,KAAC0B,EAAAA,EAAG,CAACG,GAAI,EAAE1B,UACPmB,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACH,UAAU,OAAMrB,SAAA,EAClBH,EAAAA,EAAAA,KAAC2B,EAAAA,EAAKqD,OAAM,CAAA7E,UACRH,EAAAA,EAAAA,KAAA,UAAAG,SAAS5G,EAAE,qBAEf+H,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,KAAI,CAAAzB,SAAA,EACNmB,EAAAA,EAAAA,MAAA,OAAKE,UAAU,OAAMrB,SAAA,EACjBmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAS5G,EAAE,kBAAkB,QAC7ByG,EAAAA,EAAAA,KAAA,OAAKwB,UAAU,OAAMrB,SAChBzF,EAAeiJ,cACZ3D,EAAAA,EAAAA,KAAA,OACI4D,IAAKlJ,EAAeiJ,aACpBE,IAAI,WACJC,MAAO,CACHC,MAAO,OACPwB,UAAW,QACXtB,UAAW,UACXC,aAAc,MACdC,OAAQ,UACRqB,OAAQ,qBAEZxD,QAASA,IAAMoC,OAAOC,KAAK3J,EAAeiJ,aAAc,aAG5D3D,EAAAA,EAAAA,KAAA,OAAKwB,UAAU,8BAA8BsC,MAAO,CAAE0B,OAAQ,qBAAsBtB,aAAc,OAAQ/D,SACrG5G,EAAE,6BAKnB+H,EAAAA,EAAAA,MAAA,OAAKE,UAAU,OAAMrB,SAAA,EACjBmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAS5G,EAAE,iBAAiB,QAC5ByG,EAAAA,EAAAA,KAAA,OAAKwB,UAAU,OAAMrB,SAChBzF,EAAe4J,aACZtE,EAAAA,EAAAA,KAAA,OACI4D,IAAKlJ,EAAe4J,YACpBT,IAAI,UACJC,MAAO,CACHC,MAAO,OACPwB,UAAW,QACXtB,UAAW,UACXC,aAAc,MACdC,OAAQ,UACRqB,OAAQ,qBAEZxD,QAASA,IAAMoC,OAAOC,KAAK3J,EAAe4J,YAAa,aAG3DtE,EAAAA,EAAAA,KAAA,OAAKwB,UAAU,8BAA8BsC,MAAO,CAAE0B,OAAQ,qBAAsBtB,aAAc,OAAQ/D,SACrG5G,EAAE,6CAY/C+H,EAAAA,EAAAA,MAACuD,EAAAA,EAAMY,OAAM,CAAAtF,SAAA,EACTH,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAAShB,GAAe0E,SAAU9K,EAAWuF,SACpE5G,EAAE,aAEPyG,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CACHC,QAAQ,SACRC,QAASA,IAAM3B,GAAkB,YACjCqF,SAAU9K,GAAgD,cAApB,OAAdF,QAAc,IAAdA,OAAc,EAAdA,EAAgB+E,eAA6BU,SAEpEvF,GACG0G,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAA,QAAMwB,UAAU,wCAAwCmE,KAAK,SAAS,cAAY,SACjFpM,EAAE,kBAGP+H,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAC4F,EAAAA,IAAO,CAACpE,UAAU,SAClBjI,EAAE,gBAIfyG,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CACHC,QAAQ,UACRC,QAASA,IAAM3B,GAAkB,YACjCqF,SAAU9K,GAAgD,cAApB,OAAdF,QAAc,IAAdA,OAAc,EAAdA,EAAgB+E,eAA6BU,SAEpEvF,GACG0G,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAA,QAAMwB,UAAU,wCAAwCmE,KAAK,SAAS,cAAY,SACjFpM,EAAE,kBAGP+H,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAC6F,EAAAA,IAAO,CAACrE,UAAU,SAClBjI,EAAE,uBAQvB+H,EAAAA,EAAAA,MAACuD,EAAAA,EAAK,CAACC,KAAM5J,EAAsB6J,OAAQ1D,GAAuBmD,KAAK,KAAIrE,SAAA,EACvEH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMG,OAAM,CAACC,aAAW,EAAA9E,UACrBH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMK,MAAK,CAAA/E,SAAE5G,EAAE,qBAEpByG,EAAAA,EAAAA,KAAA,SAAOmF,wBAAyB,CAC5BC,OAAQ,wjBAaZ9D,EAAAA,EAAAA,MAACuD,EAAAA,EAAMjD,KAAI,CAAAzB,SAAA,CACNvE,KACGoE,EAAAA,EAAAA,KAACsF,EAAAA,EAAK,CAACvD,QAAQ,SAASP,UAAU,OAAMrB,SACnCvE,KAGRE,KACGkE,EAAAA,EAAAA,KAACsF,EAAAA,EAAK,CAACvD,QAAQ,UAAUP,UAAU,OAAMrB,SACpCrE,KAIRV,IACG4E,EAAAA,EAAAA,KAAA,OAAKwB,UAAU,OAAMrB,UACjBmB,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAAAxB,SAAA,EACDH,EAAAA,EAAAA,KAAC2B,EAAAA,EAAKqD,OAAM,CAAA7E,UACRH,EAAAA,EAAAA,KAAA,UAAAG,SAAS5G,EAAE,sBAEf+H,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,KAAI,CAAAzB,SAAA,EACNmB,EAAAA,EAAAA,MAAA,KAAAnB,SAAA,EAAGmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAS5G,EAAE,YAAY,OAAU,KAAyB,QAAvBD,EAAA8B,EAAkByD,aAAK,IAAAvF,OAAA,EAAvBA,EAAyB+F,QAAS,QACxEiC,EAAAA,EAAAA,MAAA,KAAAnB,SAAA,EAAGmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAS5G,EAAE,aAAa,OAAU,IAAE6B,EAAkBoE,WAAa,QACtE8B,EAAAA,EAAAA,MAAA,KAAAnB,SAAA,EAAGmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAS5G,EAAE,kBAAkB,OAAU,IAAEuG,GAAe1E,EAAkBqE,2BAM7F6B,EAAAA,EAAAA,MAACa,EAAAA,EAAKC,MAAK,CAACZ,UAAU,OAAMrB,SAAA,EACxBH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,UAACH,EAAAA,EAAAA,KAAA,UAAAG,SAAS5G,EAAE,yBACvB+H,EAAAA,EAAAA,MAACa,EAAAA,EAAKW,OAAM,CACRJ,MAAOlH,EACPmH,SAAWC,GAAMnH,EAAoBmH,EAAEC,OAAOH,OAC9CgD,SAAUhK,EAAmByE,SAAA,EAE7BH,EAAAA,EAAAA,KAAA,UAAQ0C,MAAM,GAAEvC,SAAE5G,EAAE,yBACnB+B,EAAgB2C,IAAI6H,IAAK,IAAAC,EAAA,OACtBzE,EAAAA,EAAAA,MAAA,UAA4BoB,MAAOoD,EAAM3H,QAAQgC,SAAA,CAC5C2F,EAAME,aAAyB,QAAfD,EAAID,EAAMjH,aAAK,IAAAkH,OAAA,EAAXA,EAAa1G,QAASyG,EAAM3H,QAChD2H,EAAMG,gBAAkB,KAAKH,EAAMG,qBAF3BH,EAAM3H,cAMC,IAA3B7C,EAAgBmF,SACbT,EAAAA,EAAAA,KAACmC,EAAAA,EAAK+D,KAAI,CAAC1E,UAAU,aAAYrB,SAC5B5G,EAAE,gCAKnB+H,EAAAA,EAAAA,MAACuD,EAAAA,EAAMY,OAAM,CAAAtF,SAAA,EACTH,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAASX,GAAuBqE,SAAUhK,EAAmByE,SACpF5G,EAAE,aAEPyG,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CACHC,QAAQ,UACRC,QAvdajF,UAC7B,GAAK3B,GAAsBI,EAA3B,CAKAG,GAAsB,GACtBE,GAAoB,IACpBE,GAAsB,IAEtB,IACI,MAAMiB,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EACD,MAAM,IAAIsD,MAAM,8BAIpB,MAAM,KAAEpD,EAAI,MAAEK,SAAgBP,EACzBS,KAAK,qBACL8C,OAAO,CAAE4F,SAAU3K,IACnBmC,GAAG,UAAWvC,EAAkB+C,SAChCT,SAEL,GAAIH,EAEA,MADAQ,QAAQR,MAAM,kBAAmBA,GAC3BA,EAGV,IAAKL,GAAwB,IAAhBA,EAAKuD,OACd,MAAM,IAAIH,MAAM,qCAIpB5G,EAAWkH,GACPA,EAAYxC,OAAOa,GAAUA,EAAOd,UAAY/C,EAAkB+C,UAGtEpC,GAAsBxC,EAAE,+BAGxBuH,WAAW,KACP3F,GAAwB,GACxBE,EAAqB,MACrBI,EAAoB,KACrB,KAEP,CAAE,MAAO8B,GACLQ,QAAQR,MAAM,wBAAyBA,GACvC1B,GAAoB0B,EAAMwD,SAAWxH,EAAE,sBAC3C,CAAC,QACGoC,GAAsB,EAC1B,CA/CA,MAFIE,GAAoBtC,EAAE,yBAsdVmM,SAAUhK,IAAuBF,GAA+C,IAA3BF,EAAgBmF,OAAaN,SAEjFzE,GACG4F,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAA,QAAMwB,UAAU,wCAAwCmE,KAAK,SAAS,cAAY,SACjFpM,EAAE,kBAGP+H,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAC4E,EAAAA,IAAa,CAACpD,UAAU,SACxBjI,EAAE,8BAQvB+H,EAAAA,EAAAA,MAACuD,EAAAA,EAAK,CAACC,KAAM9I,GAAoB+I,OAAQ3E,GAAqBoE,KAAK,KAAIrE,SAAA,EACnEH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMG,OAAM,CAACC,aAAW,EAAA9E,UACrBH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAMK,MAAK,CAAA/E,SAAE5G,EAAE,mBAEpB+H,EAAAA,EAAAA,MAACuD,EAAAA,EAAMjD,KAAI,CAAAzB,SAAA,CACNzD,KACGsD,EAAAA,EAAAA,KAACsF,EAAAA,EAAK,CAACvD,QAAQ,SAASP,UAAU,OAAMrB,SACnCzD,KAGRE,KACGoD,EAAAA,EAAAA,KAACsF,EAAAA,EAAK,CAACvD,QAAQ,UAAUP,UAAU,OAAMrB,SACpCvD,MAIT0E,EAAAA,EAAAA,MAACa,EAAAA,EAAI,CAAAhC,SAAA,EACDmB,EAAAA,EAAAA,MAACa,EAAAA,EAAKC,MAAK,CAACZ,UAAU,OAAMrB,SAAA,EACxBH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,UAACH,EAAAA,EAAAA,KAAA,UAAAG,SAAS5G,EAAE,sBACvByG,EAAAA,EAAAA,KAACmC,EAAAA,EAAKI,QAAO,CACTC,KAAK,QACLE,MAAOxG,GACPyG,SAAWC,GAAMzG,GAAkByG,EAAEC,OAAOH,OAC5CD,YAAalJ,EAAE,uBACfmM,SAAUlJ,GACV4J,UAAQ,KAEZpG,EAAAA,EAAAA,KAACmC,EAAAA,EAAK+D,KAAI,CAAC1E,UAAU,aAAYrB,SAC5B5G,EAAE,2BAIX+H,EAAAA,EAAAA,MAACa,EAAAA,EAAKC,MAAK,CAACZ,UAAU,OAAMrB,SAAA,EACxBH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,UAACH,EAAAA,EAAAA,KAAA,UAAAG,SAAS5G,EAAE,iBACvByG,EAAAA,EAAAA,KAACmC,EAAAA,EAAKI,QAAO,CACTC,KAAK,WACLE,MAAOtG,GACPuG,SAAWC,GAAMvG,GAAqBuG,EAAEC,OAAOH,OAC/CD,YAAalJ,EAAE,kBACfmM,SAAUlJ,GACV6J,UAAW,EACXD,UAAQ,KAEZpG,EAAAA,EAAAA,KAACmC,EAAAA,EAAK+D,KAAI,CAAC1E,UAAU,aAAYrB,SAC5B5G,EAAE,8BAIX+H,EAAAA,EAAAA,MAACa,EAAAA,EAAKC,MAAK,CAACZ,UAAU,OAAMrB,SAAA,EACxBH,EAAAA,EAAAA,KAACmC,EAAAA,EAAKE,MAAK,CAAAlC,UAACH,EAAAA,EAAAA,KAAA,UAAAG,SAAS5G,EAAE,oBACvByG,EAAAA,EAAAA,KAACmC,EAAAA,EAAKI,QAAO,CACTC,KAAK,OACLE,MAAOpG,GACPqG,SAAWC,GAAMrG,GAAuBqG,EAAEC,OAAOH,OACjDD,YAAalJ,EAAE,qBACfmM,SAAUlJ,GACV4J,UAAQ,KAEZpG,EAAAA,EAAAA,KAACmC,EAAAA,EAAK+D,KAAI,CAAC1E,UAAU,aAAYrB,SAC5B5G,EAAE,gCAKnB+H,EAAAA,EAAAA,MAACuD,EAAAA,EAAMY,OAAM,CAAAtF,SAAA,EACTH,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAAS5B,GAAqBsF,SAAUlJ,GAAiB2D,SAChF5G,EAAE,aAEPyG,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CACHC,QAAQ,UACRC,QA5uBWjF,UAC3B,IAAKb,KAAmBE,KAAsBE,GAE1C,YADAK,GAAkBpD,EAAE,wBAMxB,GADmB,6BACH+M,KAAKpK,IAMrB,GAAIE,GAAkBqE,OAAS,EAC3B9D,GAAkBpD,EAAE,4BADxB,CAKAkD,IAAoB,GACpBE,GAAkB,IAClBE,GAAoB,IAEpB,IACI,MAAMG,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EACD,MAAM,IAAIsD,MAAM,8BAIpB,MAAQpD,MAAQC,KAAMoJ,UAAwBvJ,EAASI,KAAKC,UAC5D,IAAKkJ,EACD,MAAM,IAAIjG,MAAM,2BAKpB,MAAMkG,QAA2BC,MAAM,GAAGrC,OAAOsC,OAAOC,sBAAuB,CAC3EC,OAAQ,OACRC,QAAS,CACL,eAAgB,mBAChB,aAAczC,OAAOsC,OAAOI,OAEhCC,KAAMC,KAAKC,UAAU,CACjB5H,MAAOnD,GACPgL,SAAU9K,GACV+K,YAAa7K,GACb6J,SAAUI,EAAY3I,OAI9B,IAAK4I,EAAmBY,GAAI,CACxB,MAAMC,QAAkBb,EAAmBc,OAC3C,MAAM,IAAIhH,MAAM+G,EAAUtG,SAAW,0BACzC,CAEA,MAAMwG,QAAef,EAAmBc,OAExC,IAAKC,EAAOC,QACR,MAAM,IAAIlH,MAAMiH,EAAOxG,SAAW,2BAGtClE,GAAoBtD,EAAE,gCAGtBuH,WAAW,KACP7E,IAAsB,GACtBE,GAAkB,IAClBE,GAAqB,IACrBE,GAAuB,IAEvB6H,OAAOqD,SAASC,UACjB,IAEP,CAAE,MAAOnK,GACLQ,QAAQR,MAAM,yBAA0BA,GACxCZ,GAAkBY,EAAMwD,SAAWxH,EAAE,yBACzC,CAAC,QACGkD,IAAoB,EACxB,CA9DA,MARIE,GAAkBpD,EAAE,0BAouBRmM,SAAUlJ,KAAqBN,KAAmBE,KAAsBE,GAAoB6D,SAE3F3D,IACG8E,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAAA,QAAMwB,UAAU,wCAAwCmE,KAAK,SAAS,cAAY,SACjFpM,EAAE,gBAGP+H,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAAlF,SAAA,EACIH,EAAAA,EAAAA,KAACkC,EAAAA,IAAM,CAACV,UAAU,SACjBjI,EAAE,+B", "sources": ["pages/agent/Members.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge, Button, Form, InputGroup, Dropdown, Modal, Alert } from 'react-bootstrap';\nimport { FaSearch, FaPlus, FaUserCheck, FaExchangeAlt, FaCheck, FaTimes } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Members = () => {\n    const { t } = useTranslation();\n    const [members, setMembers] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [statusFilter, setStatusFilter] = useState('');\n    const [startDate, setStartDate] = useState('');\n    const [endDate, setEndDate] = useState('');\n    const [filteredMembers, setFilteredMembers] = useState([]);\n    const [showKycModal, setShowKycModal] = useState(false);\n    const [selectedMember, setSelectedMember] = useState(null);\n    const [kycLoading, setKycLoading] = useState(false);\n    const [kycError, setKycError] = useState('');\n    const [kycSuccess, setKycSuccess] = useState('');\n\n    // Change Agent Modal states\n    const [showChangeAgentModal, setShowChangeAgentModal] = useState(false);\n    const [changeAgentMember, setChangeAgentMember] = useState(null);\n    const [availableAgents, setAvailableAgents] = useState([]);\n    const [selectedNewAgent, setSelectedNewAgent] = useState('');\n    const [changeAgentLoading, setChangeAgentLoading] = useState(false);\n    const [changeAgentError, setChangeAgentError] = useState('');\n    const [changeAgentSuccess, setChangeAgentSuccess] = useState('');\n\n    // Add Member Modal states\n    const [showAddMemberModal, setShowAddMemberModal] = useState(false);\n    const [newMemberEmail, setNewMemberEmail] = useState('');\n    const [newMemberPassword, setNewMemberPassword] = useState('');\n    const [newMemberInviteCode, setNewMemberInviteCode] = useState('');\n    const [addMemberLoading, setAddMemberLoading] = useState(false);\n    const [addMemberError, setAddMemberError] = useState('');\n    const [addMemberSuccess, setAddMemberSuccess] = useState('');\n\n    useEffect(() => {\n            const fetchMembers = async () => {\n                const supabase = getSupabase();\n                if (!supabase) return;\n    \n                setLoading(true);\n                const { data: { user } } = await supabase.auth.getUser();\n    \n                if (!user) {\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 1: 查询 customer_profiles\n                const { data: customers, error: profileError } = await supabase\n                    .from('customer_profiles')\n                    .select('user_id, real_name, id_number, id_img_front, id_img_back, verify_status')\n                    .eq('agent_id', user.id)\n                    .order('created_at', { ascending: false });\n    \n                if (profileError || !customers) {\n                    console.error('Error fetching customer_profiles:', profileError);\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 2: 查询 users 表\n                const userIds = customers.map(c => c.user_id).filter(Boolean);\n    \n                const { data: userInfoList, error: userError } = await supabase\n                    .from('users')\n                    .select('id, email, created_at')\n                    .in('id', userIds);\n\n                if (userError) {\n                    console.error('Error fetching users:', userError);\n                }\n    \n                // Step 3: 合并结果\n                const usersMap = new Map((userInfoList || []).map(u => [u.id, u]));\n    \n                const enrichedMembers = customers.map(c => ({\n                    ...c,\n                    users: usersMap.get(c.user_id) || {}\n                }));\n    \n                setMembers(enrichedMembers);\n                setLoading(false);\n            };\n\n        fetchMembers();\n    }, []);\n\n    // Filter members based on search criteria\n    useEffect(() => {\n        let filtered = members;\n\n        // Search by username (email)\n        if (searchTerm) {\n            filtered = filtered.filter(member =>\n                member.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                member.real_name?.toLowerCase().includes(searchTerm.toLowerCase())\n            );\n        }\n\n        // Filter by status\n        if (statusFilter) {\n            filtered = filtered.filter(member => member.verify_status === statusFilter);\n        }\n\n        // Filter by date range\n        if (startDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) >= new Date(startDate)\n            );\n        }\n        if (endDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) <= new Date(endDate)\n            );\n        }\n\n        setFilteredMembers(filtered);\n    }, [members, searchTerm, statusFilter, startDate, endDate]);\n\n    // Fetch available agents for change agent modal\n    const fetchAvailableAgents = async () => {\n        const supabase = getSupabase();\n        if (!supabase) return;\n\n        try {\n            // Get current user (should be an agent)\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) return;\n\n            // Query agent_profiles and join with users to get email\n            const { data: agents, error } = await supabase\n                .from('agent_profiles')\n                .select(`\n                    user_id,\n                    brand_name,\n                    commission_pct,\n                    users:user_id (\n                        email\n                    )\n                `)\n                .neq('user_id', user.id); // Exclude current agent\n\n            if (error) {\n                console.error('Error fetching agents:', error);\n                return;\n            }\n\n            setAvailableAgents(agents || []);\n        } catch (error) {\n            console.error('Error in fetchAvailableAgents:', error);\n        }\n    };\n\n    const getStatusBadge = (status) => {\n        switch (status) {\n            case 'approved':\n                return <Badge bg=\"success\">{t('approved')}</Badge>;\n            case 'pending':\n                return <Badge bg=\"warning\">{t('pending_review')}</Badge>;\n            case 'rejected':\n                return <Badge bg=\"danger\">{t('rejected')}</Badge>;\n            case 'under_review':\n                return <Badge bg=\"info\">{t('under_review')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{status || t('not_submitted')}</Badge>;\n        }\n    };\n\n    const handleSearch = () => {\n        // Search is handled by useEffect, this function can be used for additional logic if needed\n        console.log('Search triggered');\n    };\n\n    const handleAddMember = () => {\n        setShowAddMemberModal(true);\n        setNewMemberEmail('');\n        setNewMemberPassword('');\n        setNewMemberInviteCode('');\n        setAddMemberError('');\n        setAddMemberSuccess('');\n    };\n\n    const handleConfirmAddMember = async () => {\n        if (!newMemberEmail || !newMemberPassword || !newMemberInviteCode) {\n            setAddMemberError(t('all_fields_required'));\n            return;\n        }\n\n        // Validate email format\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(newMemberEmail)) {\n            setAddMemberError(t('invalid_email_format'));\n            return;\n        }\n\n        // Validate password length\n        if (newMemberPassword.length < 6) {\n            setAddMemberError(t('password_min_length'));\n            return;\n        }\n\n        setAddMemberLoading(true);\n        setAddMemberError('');\n        setAddMemberSuccess('');\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error('Database connection failed');\n            }\n\n            // Get current user (agent)\n            const { data: { user: currentUser } } = await supabase.auth.getUser();\n            if (!currentUser) {\n                throw new Error('Agent not authenticated');\n            }\n\n            // Step 1: Create auth user using admin API\n            // Note: This requires service key, so we need to call a backend endpoint\n            const createUserResponse = await fetch(`${window.wpData.apiUrl}create-member`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'X-WP-Nonce': window.wpData.nonce\n                },\n                body: JSON.stringify({\n                    email: newMemberEmail,\n                    password: newMemberPassword,\n                    invite_code: newMemberInviteCode,\n                    agent_id: currentUser.id\n                })\n            });\n\n            if (!createUserResponse.ok) {\n                const errorData = await createUserResponse.json();\n                throw new Error(errorData.message || 'Failed to create member');\n            }\n\n            const result = await createUserResponse.json();\n\n            if (!result.success) {\n                throw new Error(result.message || 'Failed to create member');\n            }\n\n            setAddMemberSuccess(t('member_created_successfully'));\n\n            // Close modal and refresh after 2 seconds to show success message\n            setTimeout(() => {\n                setShowAddMemberModal(false);\n                setNewMemberEmail('');\n                setNewMemberPassword('');\n                setNewMemberInviteCode('');\n                // Refresh the members list\n                window.location.reload();\n            }, 2000);\n\n        } catch (error) {\n            console.error('Error creating member:', error);\n            setAddMemberError(error.message || t('member_creation_error'));\n        } finally {\n            setAddMemberLoading(false);\n        }\n    };\n\n    const closeAddMemberModal = () => {\n        setShowAddMemberModal(false);\n        setNewMemberEmail('');\n        setNewMemberPassword('');\n        setNewMemberInviteCode('');\n        setAddMemberError('');\n        setAddMemberSuccess('');\n    };\n\n    const handleKycReview = (member) => {\n        setSelectedMember(member);\n        setShowKycModal(true);\n        setKycError('');\n        setKycSuccess('');\n    };\n\n    const handleKycDecision = async (decision) => {\n        if (!selectedMember) return;\n\n        setKycLoading(true);\n        setKycError('');\n        setKycSuccess('');\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error('Database connection failed');\n            }\n\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .update({ verify_status: decision })\n                .eq('user_id', selectedMember.user_id)\n                .select();\n\n            if (error) {\n                console.error('Database error:', error);\n                throw error;\n            }\n\n            // Check if any rows were updated\n            if (!data || data.length === 0) {\n                \n                // Try to find the record\n                const { data: existingRecord, error: selectError } = await supabase\n                    .from('customer_profiles')\n                    .select('*')\n                    .eq('user_id', selectedMember.user_id);\n\n                if (selectError) {\n                    console.error('Error checking existing record:', selectError);\n                    throw selectError;\n                }\n                \n                if (!existingRecord || existingRecord.length === 0) {\n                    throw new Error('Customer profile not found');\n                }\n            }\n\n            // Update local state\n            setMembers(prevMembers => \n                prevMembers.map(member => \n                    member.user_id === selectedMember.user_id \n                        ? { ...member, verify_status: decision }\n                        : member\n                )\n            );\n\n            // Also update the selected member\n            setSelectedMember(prev => ({ ...prev, verify_status: decision }));\n\n            setKycSuccess(decision === 'approved' ? t('kyc_approved_success') : t('kyc_rejected_success'));\n            \n            // Close modal after 1.5 seconds\n            setTimeout(() => {\n                setShowKycModal(false);\n                setSelectedMember(null);\n            }, 1500);\n\n        } catch (error) {\n            console.error('Error updating KYC status:', error);\n            setKycError(error.message || t('kyc_update_error'));\n        } finally {\n            setKycLoading(false);\n        }\n    };\n\n    const closeKycModal = () => {\n        setShowKycModal(false);\n        setSelectedMember(null);\n        setKycError('');\n        setKycSuccess('');\n    };\n\n    const handleChangeAgent = async (member) => {\n        setChangeAgentMember(member);\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n        setSelectedNewAgent('');\n        \n        // Fetch available agents\n        await fetchAvailableAgents();\n        \n        setShowChangeAgentModal(true);\n    };\n\n    const handleConfirmChangeAgent = async () => {\n        if (!changeAgentMember || !selectedNewAgent) {\n            setChangeAgentError(t('please_select_agent'));\n            return;\n        }\n\n        setChangeAgentLoading(true);\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error('Database connection failed');\n            }\n\n            // Update customer's agent_id\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .update({ agent_id: selectedNewAgent })\n                .eq('user_id', changeAgentMember.user_id)\n                .select();\n\n            if (error) {\n                console.error('Database error:', error);\n                throw error;\n            }\n\n            if (!data || data.length === 0) {\n                throw new Error('Failed to update agent assignment');\n            }\n\n            // Remove the member from current list since they're no longer assigned to current agent\n            setMembers(prevMembers => \n                prevMembers.filter(member => member.user_id !== changeAgentMember.user_id)\n            );\n\n            setChangeAgentSuccess(t('agent_changed_successfully'));\n            \n            // Close modal after 1.5 seconds\n            setTimeout(() => {\n                setShowChangeAgentModal(false);\n                setChangeAgentMember(null);\n                setSelectedNewAgent('');\n            }, 1500);\n\n        } catch (error) {\n            console.error('Error changing agent:', error);\n            setChangeAgentError(error.message || t('agent_change_error'));\n        } finally {\n            setChangeAgentLoading(false);\n        }\n    };\n\n    const closeChangeAgentModal = () => {\n        setShowChangeAgentModal(false);\n        setChangeAgentMember(null);\n        setSelectedNewAgent('');\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n    };\n\n\n    if (loading) {\n        return <div>{t('loading_members')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('member_list')}</h2>\n            \n            {/* Top Operation Bar */}\n            <Row className=\"mb-4\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={2}>\n                                    <Button \n                                        variant=\"primary\" \n                                        onClick={handleAddMember}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaPlus className=\"me-1\" />\n                                        {t('add_member')}\n                                    </Button>\n                                </Col>\n                                <Col md={3}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_username')}</Form.Label>\n                                        <InputGroup>\n                                            <Form.Control\n                                                type=\"text\"\n                                                placeholder={t('please_enter_username')}\n                                                value={searchTerm}\n                                                onChange={(e) => setSearchTerm(e.target.value)}\n                                            />\n                                        </InputGroup>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('status_filter')}</Form.Label>\n                                        <Form.Select\n                                            value={statusFilter}\n                                            onChange={(e) => setStatusFilter(e.target.value)}\n                                        >\n                                            <option value=\"\">{t('please_select_status')}</option>\n                                            <option value=\"pending\">{t('pending_review')}</option>\n                                            <option value=\"approved\">{t('approved')}</option>\n                                            <option value=\"rejected\">{t('rejected')}</option>\n                                            <option value=\"under_review\">{t('under_review')}</option>\n                                        </Form.Select>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('start_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={startDate}\n                                            onChange={(e) => setStartDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('end_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={endDate}\n                                            onChange={(e) => setEndDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={1}>\n                                    <Button \n                                        variant=\"outline-primary\" \n                                        onClick={handleSearch}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaSearch />\n                                    </Button>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* Members Table */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('username')}</th>\n                                        <th>{t('real_name')}</th>\n                                        <th>{t('id_number')}</th>\n                                        <th>{t('id_front_image')}</th>\n                                        <th>{t('id_back_image')}</th>\n                                        <th>{t('status')}</th>\n                                        <th>{t('registration_time')}</th>\n                                        <th>{t('actions')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {filteredMembers.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"9\" className=\"text-center\">{t('no_members_found')}</td>\n                                        </tr>\n                                    ) : (\n                                        filteredMembers.map(member => (\n                                            <tr key={member.user_id}>\n                                                <td>{member.users?.email || '-'}</td>\n                                                <td>{member.real_name || '-'}</td>\n                                                <td>{member.id_number || '-'}</td>\n                                                <td>\n                                                    {member.id_img_front ? (\n                                                        <img \n                                                            src={member.id_img_front} \n                                                            alt=\"ID Front\" \n                                                            style={{ \n                                                                width: '60px', \n                                                                height: '40px', \n                                                                objectFit: 'cover', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer'\n                                                            }}\n                                                            onClick={() => window.open(member.id_img_front, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <span className=\"text-muted\">-</span>\n                                                    )}\n                                                </td>\n                                                <td>\n                                                    {member.id_img_back ? (\n                                                        <img \n                                                            src={member.id_img_back} \n                                                            alt=\"ID Back\" \n                                                            style={{ \n                                                                width: '60px', \n                                                                height: '40px', \n                                                                objectFit: 'cover', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer'\n                                                            }}\n                                                            onClick={() => window.open(member.id_img_back, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <span className=\"text-muted\">-</span>\n                                                    )}\n                                                </td>\n                                                <td>{getStatusBadge(member.verify_status)}</td>\n                                                <td>{member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'}</td>\n                                                <td>\n                                                    <div className=\"d-flex justify-content-between\">\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-primary\"\n                                                            onClick={() => handleKycReview(member)}\n                                                            title={t('kyc_review')}\n                                                        >\n                                                            <FaUserCheck />\n                                                        </Button>\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-warning\"\n                                                            onClick={() => handleChangeAgent(member)}\n                                                            title={t('change_agent')}\n                                                        >\n                                                            <FaExchangeAlt />\n                                                        </Button>\n                                                    </div>\n                                                </td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* KYC Review Modal */}\n            <Modal show={showKycModal} onHide={closeKycModal} size=\"lg\">\n                <Modal.Header closeButton className=\"custom-modal-header\">\n                    <Modal.Title>{t('kyc_review')}</Modal.Title>\n                </Modal.Header>\n                <style dangerouslySetInnerHTML={{\n                    __html: `\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    `\n                }} />\n                <Modal.Body>\n                    {selectedMember && (\n                        <>\n                            {kycError && (\n                                <Alert variant=\"danger\" className=\"mb-3\">\n                                    {kycError}\n                                </Alert>\n                            )}\n                            {kycSuccess && (\n                                <Alert variant=\"success\" className=\"mb-3\">\n                                    {kycSuccess}\n                                </Alert>\n                            )}\n                            \n                            <Row>\n                                <Col md={6}>\n                                    <Card className=\"mb-3\">\n                                        <Card.Header>\n                                            <strong>{t('customer_info')}</strong>\n                                        </Card.Header>\n                                        <Card.Body>\n                                            <p><strong>{t('username')}:</strong> {selectedMember.users?.email || '-'}</p>\n                                            <p><strong>{t('real_name')}:</strong> {selectedMember.real_name || '-'}</p>\n                                            <p><strong>{t('id_number')}:</strong> {selectedMember.id_number || '-'}</p>\n                                            <p><strong>{t('current_status')}:</strong> {getStatusBadge(selectedMember.verify_status)}</p>\n                                            <p><strong>{t('registration_time')}:</strong> {selectedMember.users?.created_at ? new Date(selectedMember.users.created_at).toLocaleString() : '-'}</p>\n                                        </Card.Body>\n                                    </Card>\n                                </Col>\n                                <Col md={6}>\n                                    <Card className=\"mb-3\">\n                                        <Card.Header>\n                                            <strong>{t('id_documents')}</strong>\n                                        </Card.Header>\n                                        <Card.Body>\n                                            <div className=\"mb-3\">\n                                                <strong>{t('id_front_image')}:</strong>\n                                                <div className=\"mt-2\">\n                                                    {selectedMember.id_img_front ? (\n                                                        <img \n                                                            src={selectedMember.id_img_front} \n                                                            alt=\"ID Front\" \n                                                            style={{ \n                                                                width: '100%', \n                                                                maxHeight: '150px', \n                                                                objectFit: 'contain', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer',\n                                                                border: '1px solid #dee2e6'\n                                                            }}\n                                                            onClick={() => window.open(selectedMember.id_img_front, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <div className=\"text-muted text-center py-3\" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>\n                                                            {t('no_image_uploaded')}\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                            <div className=\"mb-3\">\n                                                <strong>{t('id_back_image')}:</strong>\n                                                <div className=\"mt-2\">\n                                                    {selectedMember.id_img_back ? (\n                                                        <img \n                                                            src={selectedMember.id_img_back} \n                                                            alt=\"ID Back\" \n                                                            style={{ \n                                                                width: '100%', \n                                                                maxHeight: '150px', \n                                                                objectFit: 'contain', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer',\n                                                                border: '1px solid #dee2e6'\n                                                            }}\n                                                            onClick={() => window.open(selectedMember.id_img_back, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <div className=\"text-muted text-center py-3\" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>\n                                                            {t('no_image_uploaded')}\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                        </Card.Body>\n                                    </Card>\n                                </Col>\n                            </Row>\n                        </>\n                    )}\n                </Modal.Body>\n                <Modal.Footer>\n                    <Button variant=\"secondary\" onClick={closeKycModal} disabled={kycLoading}>\n                        {t('cancel')}\n                    </Button>\n                    <Button \n                        variant=\"danger\" \n                        onClick={() => handleKycDecision('rejected')}\n                        disabled={kycLoading || selectedMember?.verify_status === 'rejected'}\n                    >\n                        {kycLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaTimes className=\"me-1\" />\n                                {t('reject')}\n                            </>\n                        )}\n                    </Button>\n                    <Button \n                        variant=\"success\" \n                        onClick={() => handleKycDecision('approved')}\n                        disabled={kycLoading || selectedMember?.verify_status === 'approved'}\n                    >\n                        {kycLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaCheck className=\"me-1\" />\n                                {t('approve')}\n                            </>\n                        )}\n                    </Button>\n                </Modal.Footer>\n            </Modal>\n\n            {/* Change Agent Modal */}\n            <Modal show={showChangeAgentModal} onHide={closeChangeAgentModal} size=\"md\">\n                <Modal.Header closeButton>\n                    <Modal.Title>{t('change_agent')}</Modal.Title>\n                </Modal.Header>\n                <style dangerouslySetInnerHTML={{\n                    __html: `\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    `\n                }} />\n                <Modal.Body>\n                    {changeAgentError && (\n                        <Alert variant=\"danger\" className=\"mb-3\">\n                            {changeAgentError}\n                        </Alert>\n                    )}\n                    {changeAgentSuccess && (\n                        <Alert variant=\"success\" className=\"mb-3\">\n                            {changeAgentSuccess}\n                        </Alert>\n                    )}\n                    \n                    {changeAgentMember && (\n                        <div className=\"mb-4\">\n                            <Card>\n                                <Card.Header>\n                                    <strong>{t('customer_info')}</strong>\n                                </Card.Header>\n                                <Card.Body>\n                                    <p><strong>{t('username')}:</strong> {changeAgentMember.users?.email || '-'}</p>\n                                    <p><strong>{t('real_name')}:</strong> {changeAgentMember.real_name || '-'}</p>\n                                    <p><strong>{t('current_status')}:</strong> {getStatusBadge(changeAgentMember.verify_status)}</p>\n                                </Card.Body>\n                            </Card>\n                        </div>\n                    )}\n\n                    <Form.Group className=\"mb-3\">\n                        <Form.Label><strong>{t('select_new_agent')}</strong></Form.Label>\n                        <Form.Select\n                            value={selectedNewAgent}\n                            onChange={(e) => setSelectedNewAgent(e.target.value)}\n                            disabled={changeAgentLoading}\n                        >\n                            <option value=\"\">{t('please_select_agent')}</option>\n                            {availableAgents.map(agent => (\n                                <option key={agent.user_id} value={agent.user_id}>\n                                    {agent.brand_name || agent.users?.email || agent.user_id} \n                                    {agent.commission_pct && ` (${agent.commission_pct}%)`}\n                                </option>\n                            ))}\n                        </Form.Select>\n                        {availableAgents.length === 0 && (\n                            <Form.Text className=\"text-muted\">\n                                {t('no_available_agents')}\n                            </Form.Text>\n                        )}\n                    </Form.Group>\n                </Modal.Body>\n                <Modal.Footer>\n                    <Button variant=\"secondary\" onClick={closeChangeAgentModal} disabled={changeAgentLoading}>\n                        {t('cancel')}\n                    </Button>\n                    <Button \n                        variant=\"primary\" \n                        onClick={handleConfirmChangeAgent}\n                        disabled={changeAgentLoading || !selectedNewAgent || availableAgents.length === 0}\n                    >\n                        {changeAgentLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaExchangeAlt className=\"me-1\" />\n                                {t('confirm_change')}\n                            </>\n                        )}\n                    </Button>\n                </Modal.Footer>\n            </Modal>\n\n            {/* Add Member Modal */}\n            <Modal show={showAddMemberModal} onHide={closeAddMemberModal} size=\"md\">\n                <Modal.Header closeButton>\n                    <Modal.Title>{t('add_member')}</Modal.Title>\n                </Modal.Header>\n                <Modal.Body>\n                    {addMemberError && (\n                        <Alert variant=\"danger\" className=\"mb-3\">\n                            {addMemberError}\n                        </Alert>\n                    )}\n                    {addMemberSuccess && (\n                        <Alert variant=\"success\" className=\"mb-3\">\n                            {addMemberSuccess}\n                        </Alert>\n                    )}\n\n                    <Form>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label><strong>{t('email_address')}</strong></Form.Label>\n                            <Form.Control\n                                type=\"email\"\n                                value={newMemberEmail}\n                                onChange={(e) => setNewMemberEmail(e.target.value)}\n                                placeholder={t('enter_email_address')}\n                                disabled={addMemberLoading}\n                                required\n                            />\n                            <Form.Text className=\"text-muted\">\n                                {t('member_email_help')}\n                            </Form.Text>\n                        </Form.Group>\n\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label><strong>{t('password')}</strong></Form.Label>\n                            <Form.Control\n                                type=\"password\"\n                                value={newMemberPassword}\n                                onChange={(e) => setNewMemberPassword(e.target.value)}\n                                placeholder={t('enter_password')}\n                                disabled={addMemberLoading}\n                                minLength={6}\n                                required\n                            />\n                            <Form.Text className=\"text-muted\">\n                                {t('password_min_6_chars')}\n                            </Form.Text>\n                        </Form.Group>\n\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label><strong>{t('invite_code')}</strong></Form.Label>\n                            <Form.Control\n                                type=\"text\"\n                                value={newMemberInviteCode}\n                                onChange={(e) => setNewMemberInviteCode(e.target.value)}\n                                placeholder={t('enter_invite_code')}\n                                disabled={addMemberLoading}\n                                required\n                            />\n                            <Form.Text className=\"text-muted\">\n                                {t('invite_code_help')}\n                            </Form.Text>\n                        </Form.Group>\n                    </Form>\n                </Modal.Body>\n                <Modal.Footer>\n                    <Button variant=\"secondary\" onClick={closeAddMemberModal} disabled={addMemberLoading}>\n                        {t('cancel')}\n                    </Button>\n                    <Button\n                        variant=\"primary\"\n                        onClick={handleConfirmAddMember}\n                        disabled={addMemberLoading || !newMemberEmail || !newMemberPassword || !newMemberInviteCode}\n                    >\n                        {addMemberLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('creating')}\n                            </>\n                        ) : (\n                            <>\n                                <FaPlus className=\"me-1\" />\n                                {t('create_member')}\n                            </>\n                        )}\n                    </Button>\n                </Modal.Footer>\n            </Modal>\n        </Container>\n    );\n};\n\nexport default Members;"], "names": ["Members", "_selectedMember$users", "_selectedMember$users2", "_changeAgentMember$us", "t", "useTranslation", "members", "setMembers", "useState", "loading", "setLoading", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "startDate", "setStartDate", "endDate", "setEndDate", "filteredMembers", "setFilteredMembers", "showKycModal", "setShowKycModal", "selected<PERSON><PERSON>ber", "setSelectedMember", "kycLoading", "setKycLoading", "kycError", "setKycError", "kycSuccess", "setKycSuccess", "showChangeAgentModal", "setShowChangeAgentModal", "changeAgentMember", "setChangeAgentMember", "availableAgents", "setAvailableAgents", "selectedNewAgent", "setSelectedNewAgent", "changeAgentLoading", "setChangeAgentLoading", "changeAgentError", "setChangeAgentError", "changeAgentSuccess", "setChangeAgentSuccess", "showAddMemberModal", "setShowAddMemberModal", "newMemberEmail", "setNewMemberEmail", "newMemberPassword", "setNewMemberPassword", "newMemberInviteCode", "setNewMemberInviteCode", "addMemberLoading", "setAddMemberLoading", "addMemberError", "setAddMemberError", "addMemberSuccess", "setAddMemberSuccess", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "customers", "error", "profileError", "from", "select", "eq", "id", "order", "ascending", "console", "userIds", "map", "c", "user_id", "filter", "Boolean", "userInfoList", "userError", "in", "usersMap", "Map", "u", "enrichedMembers", "users", "get", "fetchMembers", "filtered", "member", "_member$users", "_member$users$email", "_member$real_name", "email", "toLowerCase", "includes", "real_name", "verify_status", "_member$users2", "Date", "created_at", "_member$users3", "getStatusBadge", "status", "_jsx", "Badge", "bg", "children", "closeAddMemberModal", "handleKycDecision", "Error", "update", "decision", "length", "existingRecord", "selectError", "prevMembers", "prev", "setTimeout", "message", "closeKycModal", "handleChangeAgent", "agents", "neq", "fetchAvailableAgents", "closeChangeAgentModal", "_jsxs", "Container", "className", "Row", "Col", "Card", "Body", "md", "<PERSON><PERSON>", "variant", "onClick", "handleAddMember", "FaPlus", "Form", "Group", "Label", "InputGroup", "Control", "type", "placeholder", "value", "onChange", "e", "target", "Select", "handleSearch", "log", "FaSearch", "Table", "striped", "bordered", "hover", "responsive", "colSpan", "_member$users4", "_member$users5", "id_number", "id_img_front", "src", "alt", "style", "width", "height", "objectFit", "borderRadius", "cursor", "window", "open", "id_img_back", "toLocaleString", "size", "handleKycReview", "title", "FaUserCheck", "FaExchangeAlt", "Modal", "show", "onHide", "Header", "closeButton", "Title", "dangerouslySetInnerHTML", "__html", "_Fragment", "<PERSON><PERSON>", "maxHeight", "border", "Footer", "disabled", "role", "FaTimes", "FaCheck", "agent", "_agent$users", "brand_name", "commission_pct", "Text", "agent_id", "required", "<PERSON><PERSON><PERSON><PERSON>", "test", "currentUser", "createUserResponse", "fetch", "wpData", "apiUrl", "method", "headers", "nonce", "body", "JSON", "stringify", "password", "invite_code", "ok", "errorData", "json", "result", "success", "location", "reload"], "sourceRoot": ""}