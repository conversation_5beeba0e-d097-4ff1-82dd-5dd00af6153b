-- =========================================================
--  SUPABASE RLS POLICIES  (Maker ▸ Agent ▸ Customer)
--  ✅ 20+ tables, fully fixed INSERT / UPDATE separation
--  ✅ Ready to run in Supabase Studio
-- =========================================================

/*────────────────────────────────────────────────────────────
  0. Helper function : current_user_role()
────────────────────────────────────────────────────────────*/
CREATE OR REPLACE FUNCTION current_user_role()
RETURNS TEXT
LANGUAGE sql STABLE PARALLEL SAFE AS $$
  SELECT role FROM users WHERE id = auth.uid();
$$;

-- START: Base users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
CREATE POLICY users_select_own ON users
  FOR SELECT
  USING (auth.role() = 'authenticated');
CREATE POLICY users_insert_self_admin ON users FOR INSERT WITH CHECK ( id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY users_update_self_admin ON users FOR UPDATE USING ( id = auth.uid() OR current_user_role() = 'admin' ) WITH CHECK ( id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY users_delete_admin ON users FOR DELETE USING ( current_user_role() = 'admin' );

-- MAKER_PROFILES
ALTER TABLE maker_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY maker_sel_owner_admin ON maker_profiles FOR SELECT USING ( user_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY maker_ins_owner_admin ON maker_profiles FOR INSERT WITH CHECK ( user_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY maker_upd_owner_admin ON maker_profiles FOR UPDATE USING ( user_id = auth.uid() OR current_user_role() = 'admin' ) WITH CHECK ( user_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY maker_del_admin ON maker_profiles FOR DELETE USING ( current_user_role() = 'admin' );

-- AGENT_PROFILES
ALTER TABLE agent_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY agent_sel_owner_maker_admin ON agent_profiles FOR SELECT USING ( user_id = auth.uid() OR maker_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY agent_ins_owner_admin ON agent_profiles FOR INSERT WITH CHECK ( user_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY agent_upd_owner_admin ON agent_profiles FOR UPDATE USING ( user_id = auth.uid() OR current_user_role() = 'admin' ) WITH CHECK ( user_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY agent_del_admin ON agent_profiles FOR DELETE USING ( current_user_role() = 'admin' );

-- CUSTOMER_PROFILES
ALTER TABLE customer_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY cust_sel_self_agent_admin ON customer_profiles FOR SELECT USING ( user_id = auth.uid() OR agent_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY cust_ins_self_agent_admin ON customer_profiles FOR INSERT WITH CHECK ( user_id = auth.uid() OR agent_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY cust_upd_self_admin ON customer_profiles FOR UPDATE USING ( (user_id = auth.uid()) OR 
  (agent_id = auth.uid()) OR 
  (current_user_role() = 'admin'::text) ) WITH CHECK ( (user_id = auth.uid()) OR 
  (agent_id = auth.uid()) OR 
  (current_user_role() = 'admin'::text) );
CREATE POLICY cust_del_admin ON customer_profiles FOR DELETE USING ( current_user_role() = 'admin' );

-- FACILITIES
ALTER TABLE facilities ENABLE ROW LEVEL SECURITY;
CREATE POLICY fac_read_all ON facilities FOR SELECT USING ( TRUE );
CREATE POLICY fac_ins_admin ON facilities FOR INSERT WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY fac_upd_admin ON facilities FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY fac_del_admin ON facilities FOR DELETE USING ( current_user_role() = 'admin' );

-- MINERS
ALTER TABLE miners ENABLE ROW LEVEL SECURITY;
CREATE POLICY miners_read_all ON miners FOR SELECT USING ( TRUE );
CREATE POLICY miners_ins_maker_admin ON miners FOR INSERT WITH CHECK ( current_user_role() IN ('maker','admin') );
CREATE POLICY miners_upd_maker_admin ON miners FOR UPDATE USING ( current_user_role() IN ('maker','admin') ) WITH CHECK ( current_user_role() IN ('maker','admin') );
CREATE POLICY miners_del_admin ON miners FOR DELETE USING ( current_user_role() = 'admin' );

-- NETWORK_STATS
ALTER TABLE network_stats ENABLE ROW LEVEL SECURITY;
CREATE POLICY net_read_all ON network_stats FOR SELECT USING ( TRUE );
CREATE POLICY net_ins_admin ON network_stats FOR INSERT WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY net_upd_admin ON network_stats FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY net_del_admin ON network_stats FOR DELETE USING ( current_user_role() = 'admin' );

-- CURRENCIES
ALTER TABLE currencies ENABLE ROW LEVEL SECURITY;
CREATE POLICY cur_read_all ON currencies FOR SELECT USING ( TRUE );
CREATE POLICY cur_ins_admin ON currencies FOR INSERT WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY cur_upd_admin ON currencies FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY cur_del_admin ON currencies FOR DELETE USING ( current_user_role() = 'admin' );

-- WITHDRAWALS
ALTER TABLE withdrawals ENABLE ROW LEVEL SECURITY;
CREATE POLICY wd_read_owner_admin ON withdrawals FOR SELECT USING ( user_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY wd_ins_owner ON withdrawals FOR INSERT WITH CHECK ( user_id = auth.uid() );
CREATE POLICY wd_upd_admin ON withdrawals FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY wd_del_admin ON withdrawals FOR DELETE USING ( current_user_role() = 'admin' );

-- NOTIFICATIONS
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
CREATE POLICY notif_read_owner ON notifications FOR SELECT USING ( recipient_id = auth.uid() );
CREATE POLICY notif_upd_owner ON notifications FOR UPDATE USING ( recipient_id = auth.uid() ) WITH CHECK ( recipient_id = auth.uid() );
CREATE POLICY notif_ins_self_or_admin ON notifications FOR INSERT WITH CHECK ( recipient_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY notif_del_admin ON notifications FOR DELETE USING ( current_user_role() = 'admin' );

-- AUDIT_LOGS
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
CREATE POLICY audit_read_admin ON audit_logs FOR SELECT USING ( current_user_role() = 'admin' );
CREATE POLICY audit_ins_admin ON audit_logs FOR INSERT WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY audit_upd_admin ON audit_logs FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY audit_del_admin ON audit_logs FOR DELETE USING ( current_user_role() = 'admin' );

-- MANUAL_JOURNALS
ALTER TABLE manual_journals ENABLE ROW LEVEL SECURITY;
CREATE POLICY mj_read_owner_admin ON manual_journals FOR SELECT USING ( maker_id = auth.uid() OR customer_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY mj_ins_maker_admin ON manual_journals FOR INSERT WITH CHECK ( maker_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY mj_upd_admin ON manual_journals FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY mj_del_admin ON manual_journals FOR DELETE USING ( current_user_role() = 'admin' );

-- TRANSACTIONS
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
CREATE POLICY tx_read_party_admin ON transactions FOR SELECT USING ( sender_user_id = auth.uid() OR receiver_user_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY tx_ins_admin ON transactions FOR INSERT WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY tx_upd_admin ON transactions FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY tx_del_admin ON transactions FOR DELETE USING ( current_user_role() = 'admin' );

-- PRODUCTS
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
CREATE POLICY prod_read_all ON products FOR SELECT USING ( TRUE );
CREATE POLICY prod_ins_maker_admin ON products FOR INSERT WITH CHECK ( maker_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY prod_upd_maker_admin ON products FOR UPDATE USING ( maker_id = auth.uid() OR current_user_role() = 'admin' ) WITH CHECK ( maker_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY prod_del_admin ON products FOR DELETE USING ( current_user_role() = 'admin' );

-- ORDERS
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
CREATE POLICY order_read_owner_admin ON orders FOR SELECT USING ( customer_id = auth.uid() OR agent_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY order_ins_owner_admin ON orders FOR INSERT WITH CHECK ( customer_id = auth.uid() OR agent_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY order_upd_owner_admin ON orders FOR UPDATE USING ( customer_id = auth.uid() OR agent_id = auth.uid() OR current_user_role() = 'admin' ) WITH CHECK ( customer_id = auth.uid() OR agent_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY order_del_admin ON orders FOR DELETE USING ( current_user_role() = 'admin' );

-- DISTRIBUTION_BATCHES
ALTER TABLE distribution_batches ENABLE ROW LEVEL SECURITY;
CREATE POLICY batch_read_maker_agent_admin ON distribution_batches FOR SELECT USING ( maker_id = auth.uid() OR agent_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY batch_ins_maker_admin ON distribution_batches FOR INSERT WITH CHECK ( maker_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY batch_upd_admin ON distribution_batches FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY batch_del_admin ON distribution_batches FOR DELETE USING ( current_user_role() = 'admin' );

-- ORDER_DISTRIBUTIONS
ALTER TABLE order_distributions ENABLE ROW LEVEL SECURITY;
CREATE POLICY dist_read_owner_admin ON order_distributions FOR SELECT USING ( customer_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY dist_ins_admin ON order_distributions FOR INSERT WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY dist_upd_admin ON order_distributions FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY dist_del_admin ON order_distributions FOR DELETE USING ( current_user_role() = 'admin' );

-- USER_ASSETS
ALTER TABLE user_assets ENABLE ROW LEVEL SECURITY;
CREATE POLICY assets_read_owner_admin ON user_assets FOR SELECT USING ( user_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY assets_upd_owner_admin ON user_assets FOR UPDATE USING ( user_id = auth.uid() OR current_user_role() = 'admin' ) WITH CHECK ( user_id = auth.uid() OR current_user_role() = 'admin' );

-- EXPANSION_REQUESTS
ALTER TABLE capacity_requests ENABLE ROW LEVEL SECURITY;
CREATE POLICY exp_read_owner_admin ON capacity_requests FOR SELECT USING ( maker_id = auth.uid() OR current_user_role() = 'admin' );
CREATE POLICY exp_ins_maker ON capacity_requests FOR INSERT WITH CHECK ( maker_id = auth.uid() );
CREATE POLICY exp_upd_admin ON capacity_requests FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY exp_del_admin ON capacity_requests FOR DELETE USING ( current_user_role() = 'admin' );

-- MINER_DAILY_SNAPSHOTS
ALTER TABLE miner_daily_snapshots ENABLE ROW LEVEL SECURITY;
CREATE POLICY snapshot_read_all_admin ON miner_daily_snapshots FOR SELECT USING ( TRUE );
CREATE POLICY snapshot_ins_admin ON miner_daily_snapshots FOR INSERT WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY snapshot_upd_admin ON miner_daily_snapshots FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY snapshot_del_admin ON miner_daily_snapshots FOR DELETE USING ( current_user_role() = 'admin' );

-- MINER_DAILY_EARNINGS
ALTER TABLE miner_daily_earnings ENABLE ROW LEVEL SECURITY;
CREATE POLICY earning_read_all_admin ON miner_daily_earnings FOR SELECT USING ( TRUE );
CREATE POLICY earning_ins_admin ON miner_daily_earnings FOR INSERT WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY earning_upd_admin ON miner_daily_earnings FOR UPDATE USING ( current_user_role() = 'admin' ) WITH CHECK ( current_user_role() = 'admin' );
CREATE POLICY earning_del_admin ON miner_daily_earnings FOR DELETE USING ( current_user_role() = 'admin' );

