{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge,Button,Form,InputGroup,Dropdown,Modal,Alert}from'react-bootstrap';import{FaSearch,FaPlus,FaUserCheck,FaExchangeAlt,FaCheck,FaTimes}from'react-icons/fa';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Members=()=>{var _selectedMember$users,_selectedMember$users2,_changeAgentMember$us;const{t}=useTranslation();const[members,setMembers]=useState([]);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[statusFilter,setStatusFilter]=useState('');const[startDate,setStartDate]=useState('');const[endDate,setEndDate]=useState('');const[filteredMembers,setFilteredMembers]=useState([]);const[showKycModal,setShowKycModal]=useState(false);const[selectedMember,setSelectedMember]=useState(null);const[kycLoading,setKycLoading]=useState(false);const[kycError,setKycError]=useState('');const[kycSuccess,setKycSuccess]=useState('');// Change Agent Modal states\nconst[showChangeAgentModal,setShowChangeAgentModal]=useState(false);const[changeAgentMember,setChangeAgentMember]=useState(null);const[availableAgents,setAvailableAgents]=useState([]);const[selectedNewAgent,setSelectedNewAgent]=useState('');const[changeAgentLoading,setChangeAgentLoading]=useState(false);const[changeAgentError,setChangeAgentError]=useState('');const[changeAgentSuccess,setChangeAgentSuccess]=useState('');useEffect(()=>{const fetchMembers=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;}// Step 1: 查询 customer_profiles\nconst{data:customers,error:profileError}=await supabase.from('customer_profiles').select('user_id, real_name, id_number, id_img_front, id_img_back, verify_status').eq('agent_id',user.id).order('created_at',{ascending:false});if(profileError||!customers){console.error('Error fetching customer_profiles:',profileError);setLoading(false);return;}// Step 2: 查询 users 表\nconst userIds=customers.map(c=>c.user_id).filter(Boolean);const{data:userInfoList,error:userError}=await supabase.from('users').select('id, email, created_at');if(userError){console.error('Error fetching users:',userError);}// Step 3: 合并结果\nconst usersMap=new Map((userInfoList||[]).map(u=>[u.id,u]));const enrichedMembers=customers.map(c=>({...c,users:usersMap.get(c.user_id)||{}}));setMembers(enrichedMembers);setLoading(false);};fetchMembers();},[]);// Filter members based on search criteria\nuseEffect(()=>{let filtered=members;// Search by username (email)\nif(searchTerm){filtered=filtered.filter(member=>{var _member$users,_member$users$email,_member$real_name;return((_member$users=member.users)===null||_member$users===void 0?void 0:(_member$users$email=_member$users.email)===null||_member$users$email===void 0?void 0:_member$users$email.toLowerCase().includes(searchTerm.toLowerCase()))||((_member$real_name=member.real_name)===null||_member$real_name===void 0?void 0:_member$real_name.toLowerCase().includes(searchTerm.toLowerCase()));});}// Filter by status\nif(statusFilter){filtered=filtered.filter(member=>member.verify_status===statusFilter);}// Filter by date range\nif(startDate){filtered=filtered.filter(member=>{var _member$users2;return new Date((_member$users2=member.users)===null||_member$users2===void 0?void 0:_member$users2.created_at)>=new Date(startDate);});}if(endDate){filtered=filtered.filter(member=>{var _member$users3;return new Date((_member$users3=member.users)===null||_member$users3===void 0?void 0:_member$users3.created_at)<=new Date(endDate);});}setFilteredMembers(filtered);},[members,searchTerm,statusFilter,startDate,endDate]);// Fetch available agents for change agent modal\nconst fetchAvailableAgents=async()=>{const supabase=getSupabase();if(!supabase)return;try{// Get current user (should be an agent)\nconst{data:{user}}=await supabase.auth.getUser();if(!user)return;// Query agent_profiles and join with users to get email\nconst{data:agents,error}=await supabase.from('agent_profiles').select(`\n                    user_id,\n                    brand_name,\n                    commission_pct,\n                    users:user_id (\n                        email\n                    )\n                `).neq('user_id',user.id);// Exclude current agent\nif(error){console.error('Error fetching agents:',error);return;}setAvailableAgents(agents||[]);}catch(error){console.error('Error in fetchAvailableAgents:',error);}};const getStatusBadge=status=>{switch(status){case'approved':return/*#__PURE__*/_jsx(Badge,{bg:\"success\",children:t('approved')});case'pending':return/*#__PURE__*/_jsx(Badge,{bg:\"warning\",children:t('pending_review')});case'rejected':return/*#__PURE__*/_jsx(Badge,{bg:\"danger\",children:t('rejected')});case'under_review':return/*#__PURE__*/_jsx(Badge,{bg:\"info\",children:t('under_review')});default:return/*#__PURE__*/_jsx(Badge,{bg:\"secondary\",children:status||t('not_submitted')});}};const handleSearch=()=>{// Search is handled by useEffect, this function can be used for additional logic if needed\nconsole.log('Search triggered');};const handleAddMember=()=>{// TODO: Implement add member functionality\nalert(t('add_member_coming_soon'));};const handleKycReview=member=>{setSelectedMember(member);setShowKycModal(true);setKycError('');setKycSuccess('');};const handleKycDecision=async decision=>{if(!selectedMember)return;setKycLoading(true);setKycError('');setKycSuccess('');try{const supabase=getSupabase();if(!supabase){throw new Error('Database connection failed');}const{data,error}=await supabase.from('customer_profiles').update({verify_status:decision}).eq('user_id',selectedMember.user_id).select();if(error){console.error('Database error:',error);throw error;}// Check if any rows were updated\nif(!data||data.length===0){// Try to find the record\nconst{data:existingRecord,error:selectError}=await supabase.from('customer_profiles').select('*').eq('user_id',selectedMember.user_id);if(selectError){console.error('Error checking existing record:',selectError);throw selectError;}if(!existingRecord||existingRecord.length===0){throw new Error('Customer profile not found');}}// Update local state\nsetMembers(prevMembers=>prevMembers.map(member=>member.user_id===selectedMember.user_id?{...member,verify_status:decision}:member));// Also update the selected member\nsetSelectedMember(prev=>({...prev,verify_status:decision}));setKycSuccess(decision==='approved'?t('kyc_approved_success'):t('kyc_rejected_success'));// Close modal after 1.5 seconds\nsetTimeout(()=>{setShowKycModal(false);setSelectedMember(null);},1500);}catch(error){console.error('Error updating KYC status:',error);setKycError(error.message||t('kyc_update_error'));}finally{setKycLoading(false);}};const closeKycModal=()=>{setShowKycModal(false);setSelectedMember(null);setKycError('');setKycSuccess('');};const handleChangeAgent=async member=>{setChangeAgentMember(member);setChangeAgentError('');setChangeAgentSuccess('');setSelectedNewAgent('');// Fetch available agents\nawait fetchAvailableAgents();setShowChangeAgentModal(true);};const handleConfirmChangeAgent=async()=>{if(!changeAgentMember||!selectedNewAgent){setChangeAgentError(t('please_select_agent'));return;}setChangeAgentLoading(true);setChangeAgentError('');setChangeAgentSuccess('');try{const supabase=getSupabase();if(!supabase){throw new Error('Database connection failed');}// Update customer's agent_id\nconst{data,error}=await supabase.from('customer_profiles').update({agent_id:selectedNewAgent}).eq('user_id',changeAgentMember.user_id).select();if(error){console.error('Database error:',error);throw error;}if(!data||data.length===0){throw new Error('Failed to update agent assignment');}// Remove the member from current list since they're no longer assigned to current agent\nsetMembers(prevMembers=>prevMembers.filter(member=>member.user_id!==changeAgentMember.user_id));setChangeAgentSuccess(t('agent_changed_successfully'));// Close modal after 1.5 seconds\nsetTimeout(()=>{setShowChangeAgentModal(false);setChangeAgentMember(null);setSelectedNewAgent('');},1500);}catch(error){console.error('Error changing agent:',error);setChangeAgentError(error.message||t('agent_change_error'));}finally{setChangeAgentLoading(false);}};const closeChangeAgentModal=()=>{setShowChangeAgentModal(false);setChangeAgentMember(null);setSelectedNewAgent('');setChangeAgentError('');setChangeAgentSuccess('');};if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_members')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('member_list')}),/*#__PURE__*/_jsx(Row,{className:\"mb-4\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{className:\"align-items-end\",children:[/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Button,{variant:\"primary\",onClick:handleAddMember,className:\"mb-2\",children:[/*#__PURE__*/_jsx(FaPlus,{className:\"me-1\"}),t('add_member')]})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('search_username')}),/*#__PURE__*/_jsx(InputGroup,{children:/*#__PURE__*/_jsx(Form.Control,{type:\"text\",placeholder:t('please_enter_username'),value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})})]})}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('status_filter')}),/*#__PURE__*/_jsxs(Form.Select,{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:t('please_select_status')}),/*#__PURE__*/_jsx(\"option\",{value:\"pending\",children:t('pending_review')}),/*#__PURE__*/_jsx(\"option\",{value:\"approved\",children:t('approved')}),/*#__PURE__*/_jsx(\"option\",{value:\"rejected\",children:t('rejected')}),/*#__PURE__*/_jsx(\"option\",{value:\"under_review\",children:t('under_review')})]})]})}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('start_date')}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:startDate,onChange:e=>setStartDate(e.target.value)})]})}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('end_date')}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:endDate,onChange:e=>setEndDate(e.target.value)})]})}),/*#__PURE__*/_jsx(Col,{md:1,children:/*#__PURE__*/_jsx(Button,{variant:\"outline-primary\",onClick:handleSearch,className:\"mb-2\",children:/*#__PURE__*/_jsx(FaSearch,{})})})]})})})})}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('username')}),/*#__PURE__*/_jsx(\"th\",{children:t('real_name')}),/*#__PURE__*/_jsx(\"th\",{children:t('id_number')}),/*#__PURE__*/_jsx(\"th\",{children:t('id_front_image')}),/*#__PURE__*/_jsx(\"th\",{children:t('id_back_image')}),/*#__PURE__*/_jsx(\"th\",{children:t('status')}),/*#__PURE__*/_jsx(\"th\",{children:t('registration_time')}),/*#__PURE__*/_jsx(\"th\",{children:t('actions')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:filteredMembers.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"9\",className:\"text-center\",children:t('no_members_found')})}):filteredMembers.map(member=>{var _member$users4,_member$users5;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:((_member$users4=member.users)===null||_member$users4===void 0?void 0:_member$users4.email)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:member.real_name||'-'}),/*#__PURE__*/_jsx(\"td\",{children:member.id_number||'-'}),/*#__PURE__*/_jsx(\"td\",{children:member.id_img_front?/*#__PURE__*/_jsx(\"img\",{src:member.id_img_front,alt:\"ID Front\",style:{width:'60px',height:'40px',objectFit:'cover',borderRadius:'4px',cursor:'pointer'},onClick:()=>window.open(member.id_img_front,'_blank')}):/*#__PURE__*/_jsx(\"span\",{className:\"text-muted\",children:\"-\"})}),/*#__PURE__*/_jsx(\"td\",{children:member.id_img_back?/*#__PURE__*/_jsx(\"img\",{src:member.id_img_back,alt:\"ID Back\",style:{width:'60px',height:'40px',objectFit:'cover',borderRadius:'4px',cursor:'pointer'},onClick:()=>window.open(member.id_img_back,'_blank')}):/*#__PURE__*/_jsx(\"span\",{className:\"text-muted\",children:\"-\"})}),/*#__PURE__*/_jsx(\"td\",{children:getStatusBadge(member.verify_status)}),/*#__PURE__*/_jsx(\"td\",{children:(_member$users5=member.users)!==null&&_member$users5!==void 0&&_member$users5.created_at?new Date(member.users.created_at).toLocaleString():'-'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-1\",children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-primary\",onClick:()=>handleKycReview(member),title:t('kyc_review'),children:/*#__PURE__*/_jsx(FaUserCheck,{})}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-warning\",onClick:()=>handleChangeAgent(member),title:t('change_agent'),children:/*#__PURE__*/_jsx(FaExchangeAlt,{})})]})})]},member.user_id);})})]})})})})}),/*#__PURE__*/_jsxs(Modal,{show:showKycModal,onHide:closeKycModal,size:\"lg\",children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,className:\"custom-modal-header\",children:/*#__PURE__*/_jsx(Modal.Title,{children:t('kyc_review')})}),/*#__PURE__*/_jsx(\"style\",{dangerouslySetInnerHTML:{__html:`\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    `}}),/*#__PURE__*/_jsx(Modal.Body,{children:selectedMember&&/*#__PURE__*/_jsxs(_Fragment,{children:[kycError&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"mb-3\",children:kycError}),kycSuccess&&/*#__PURE__*/_jsx(Alert,{variant:\"success\",className:\"mb-3\",children:kycSuccess}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('customer_info')})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('username'),\":\"]}),\" \",((_selectedMember$users=selectedMember.users)===null||_selectedMember$users===void 0?void 0:_selectedMember$users.email)||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('real_name'),\":\"]}),\" \",selectedMember.real_name||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('id_number'),\":\"]}),\" \",selectedMember.id_number||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('current_status'),\":\"]}),\" \",getStatusBadge(selectedMember.verify_status)]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('registration_time'),\":\"]}),\" \",(_selectedMember$users2=selectedMember.users)!==null&&_selectedMember$users2!==void 0&&_selectedMember$users2.created_at?new Date(selectedMember.users.created_at).toLocaleString():'-']})]})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('id_documents')})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('id_front_image'),\":\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:selectedMember.id_img_front?/*#__PURE__*/_jsx(\"img\",{src:selectedMember.id_img_front,alt:\"ID Front\",style:{width:'100%',maxHeight:'150px',objectFit:'contain',borderRadius:'4px',cursor:'pointer',border:'1px solid #dee2e6'},onClick:()=>window.open(selectedMember.id_img_front,'_blank')}):/*#__PURE__*/_jsx(\"div\",{className:\"text-muted text-center py-3\",style:{border:'1px dashed #dee2e6',borderRadius:'4px'},children:t('no_image_uploaded')})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('id_back_image'),\":\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:selectedMember.id_img_back?/*#__PURE__*/_jsx(\"img\",{src:selectedMember.id_img_back,alt:\"ID Back\",style:{width:'100%',maxHeight:'150px',objectFit:'contain',borderRadius:'4px',cursor:'pointer',border:'1px solid #dee2e6'},onClick:()=>window.open(selectedMember.id_img_back,'_blank')}):/*#__PURE__*/_jsx(\"div\",{className:\"text-muted text-center py-3\",style:{border:'1px dashed #dee2e6',borderRadius:'4px'},children:t('no_image_uploaded')})})]})]})]})})]})]})}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:closeKycModal,disabled:kycLoading,children:t('cancel')}),/*#__PURE__*/_jsx(Button,{variant:\"danger\",onClick:()=>handleKycDecision('rejected'),disabled:kycLoading||(selectedMember===null||selectedMember===void 0?void 0:selectedMember.verify_status)==='rejected',children:kycLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\",\"aria-hidden\":\"true\"}),t('processing')]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaTimes,{className:\"me-1\"}),t('reject')]})}),/*#__PURE__*/_jsx(Button,{variant:\"success\",onClick:()=>handleKycDecision('approved'),disabled:kycLoading||(selectedMember===null||selectedMember===void 0?void 0:selectedMember.verify_status)==='approved',children:kycLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\",\"aria-hidden\":\"true\"}),t('processing')]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaCheck,{className:\"me-1\"}),t('approve')]})})]})]}),/*#__PURE__*/_jsxs(Modal,{show:showChangeAgentModal,onHide:closeChangeAgentModal,size:\"md\",children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsx(Modal.Title,{children:t('change_agent')})}),/*#__PURE__*/_jsxs(Modal.Body,{children:[changeAgentError&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"mb-3\",children:changeAgentError}),changeAgentSuccess&&/*#__PURE__*/_jsx(Alert,{variant:\"success\",className:\"mb-3\",children:changeAgentSuccess}),changeAgentMember&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('customer_info')})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('username'),\":\"]}),\" \",((_changeAgentMember$us=changeAgentMember.users)===null||_changeAgentMember$us===void 0?void 0:_changeAgentMember$us.email)||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('real_name'),\":\"]}),\" \",changeAgentMember.real_name||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('current_status'),\":\"]}),\" \",getStatusBadge(changeAgentMember.verify_status)]})]})]})}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('select_new_agent')})}),/*#__PURE__*/_jsxs(Form.Select,{value:selectedNewAgent,onChange:e=>setSelectedNewAgent(e.target.value),disabled:changeAgentLoading,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:t('please_select_agent')}),availableAgents.map(agent=>{var _agent$users;return/*#__PURE__*/_jsxs(\"option\",{value:agent.user_id,children:[agent.brand_name||((_agent$users=agent.users)===null||_agent$users===void 0?void 0:_agent$users.email)||agent.user_id,agent.commission_pct&&` (${agent.commission_pct}%)`]},agent.user_id);})]}),availableAgents.length===0&&/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:t('no_available_agents')})]})]}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:closeChangeAgentModal,disabled:changeAgentLoading,children:t('cancel')}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:handleConfirmChangeAgent,disabled:changeAgentLoading||!selectedNewAgent||availableAgents.length===0,children:changeAgentLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\",\"aria-hidden\":\"true\"}),t('processing')]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaExchangeAlt,{className:\"me-1\"}),t('confirm_change')]})})]})]})]});};export default Members;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "<PERSON><PERSON>", "Form", "InputGroup", "Dropdown", "Modal", "<PERSON><PERSON>", "FaSearch", "FaPlus", "FaUserCheck", "FaExchangeAlt", "FaCheck", "FaTimes", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Members", "_selectedMember$users", "_selectedMember$users2", "_changeAgentMember$us", "t", "members", "setMembers", "loading", "setLoading", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "startDate", "setStartDate", "endDate", "setEndDate", "filteredMembers", "setFilteredMembers", "showKycModal", "setShowKycModal", "selected<PERSON><PERSON>ber", "setSelectedMember", "kycLoading", "setKycLoading", "kycError", "setKycError", "kycSuccess", "setKycSuccess", "showChangeAgentModal", "setShowChangeAgentModal", "changeAgentMember", "setChangeAgentMember", "availableAgents", "setAvailableAgents", "selectedNewAgent", "setSelectedNewAgent", "changeAgentLoading", "setChangeAgentLoading", "changeAgentError", "setChangeAgentError", "changeAgentSuccess", "setChangeAgentSuccess", "fetchMembers", "supabase", "data", "user", "auth", "getUser", "customers", "error", "profileError", "from", "select", "eq", "id", "order", "ascending", "console", "userIds", "map", "c", "user_id", "filter", "Boolean", "userInfoList", "userError", "usersMap", "Map", "u", "enrichedMembers", "users", "get", "filtered", "member", "_member$users", "_member$users$email", "_member$real_name", "email", "toLowerCase", "includes", "real_name", "verify_status", "_member$users2", "Date", "created_at", "_member$users3", "fetchAvailableAgents", "agents", "neq", "getStatusBadge", "status", "bg", "children", "handleSearch", "log", "handleAddMember", "alert", "handleKycReview", "handleKycDecision", "decision", "Error", "update", "length", "existingRecord", "selectError", "prevMembers", "prev", "setTimeout", "message", "closeKycModal", "handleChangeAgent", "handleConfirmChangeAgent", "agent_id", "closeChangeAgentModal", "className", "Body", "md", "variant", "onClick", "Group", "Label", "Control", "type", "placeholder", "value", "onChange", "e", "target", "Select", "striped", "bordered", "hover", "responsive", "colSpan", "_member$users4", "_member$users5", "id_number", "id_img_front", "src", "alt", "style", "width", "height", "objectFit", "borderRadius", "cursor", "window", "open", "id_img_back", "toLocaleString", "size", "title", "show", "onHide", "Header", "closeButton", "Title", "dangerouslySetInnerHTML", "__html", "maxHeight", "border", "Footer", "disabled", "role", "agent", "_agent$users", "brand_name", "commission_pct", "Text"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/Members.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge, Button, Form, InputGroup, Dropdown, Modal, Alert } from 'react-bootstrap';\nimport { FaSearch, FaPlus, FaUserCheck, FaExchangeAlt, FaCheck, FaTimes } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Members = () => {\n    const { t } = useTranslation();\n    const [members, setMembers] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [statusFilter, setStatusFilter] = useState('');\n    const [startDate, setStartDate] = useState('');\n    const [endDate, setEndDate] = useState('');\n    const [filteredMembers, setFilteredMembers] = useState([]);\n    const [showKycModal, setShowKycModal] = useState(false);\n    const [selectedMember, setSelectedMember] = useState(null);\n    const [kycLoading, setKycLoading] = useState(false);\n    const [kycError, setKycError] = useState('');\n    const [kycSuccess, setKycSuccess] = useState('');\n\n    // Change Agent Modal states\n    const [showChangeAgentModal, setShowChangeAgentModal] = useState(false);\n    const [changeAgentMember, setChangeAgentMember] = useState(null);\n    const [availableAgents, setAvailableAgents] = useState([]);\n    const [selectedNewAgent, setSelectedNewAgent] = useState('');\n    const [changeAgentLoading, setChangeAgentLoading] = useState(false);\n    const [changeAgentError, setChangeAgentError] = useState('');\n    const [changeAgentSuccess, setChangeAgentSuccess] = useState('');\n\n    useEffect(() => {\n            const fetchMembers = async () => {\n                const supabase = getSupabase();\n                if (!supabase) return;\n    \n                setLoading(true);\n                const { data: { user } } = await supabase.auth.getUser();\n    \n                if (!user) {\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 1: 查询 customer_profiles\n                const { data: customers, error: profileError } = await supabase\n                    .from('customer_profiles')\n                    .select('user_id, real_name, id_number, id_img_front, id_img_back, verify_status')\n                    .eq('agent_id', user.id)\n                    .order('created_at', { ascending: false });\n    \n                if (profileError || !customers) {\n                    console.error('Error fetching customer_profiles:', profileError);\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 2: 查询 users 表\n                const userIds = customers.map(c => c.user_id).filter(Boolean);\n    \n                const { data: userInfoList, error: userError } = await supabase\n                    .from('users')\n                    .select('id, email, created_at')\n\n                if (userError) {\n                    console.error('Error fetching users:', userError);\n                }\n    \n                // Step 3: 合并结果\n                const usersMap = new Map((userInfoList || []).map(u => [u.id, u]));\n    \n                const enrichedMembers = customers.map(c => ({\n                    ...c,\n                    users: usersMap.get(c.user_id) || {}\n                }));\n    \n                setMembers(enrichedMembers);\n                setLoading(false);\n            };\n\n        fetchMembers();\n    }, []);\n\n    // Filter members based on search criteria\n    useEffect(() => {\n        let filtered = members;\n\n        // Search by username (email)\n        if (searchTerm) {\n            filtered = filtered.filter(member =>\n                member.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                member.real_name?.toLowerCase().includes(searchTerm.toLowerCase())\n            );\n        }\n\n        // Filter by status\n        if (statusFilter) {\n            filtered = filtered.filter(member => member.verify_status === statusFilter);\n        }\n\n        // Filter by date range\n        if (startDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) >= new Date(startDate)\n            );\n        }\n        if (endDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) <= new Date(endDate)\n            );\n        }\n\n        setFilteredMembers(filtered);\n    }, [members, searchTerm, statusFilter, startDate, endDate]);\n\n    // Fetch available agents for change agent modal\n    const fetchAvailableAgents = async () => {\n        const supabase = getSupabase();\n        if (!supabase) return;\n\n        try {\n            // Get current user (should be an agent)\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) return;\n\n            // Query agent_profiles and join with users to get email\n            const { data: agents, error } = await supabase\n                .from('agent_profiles')\n                .select(`\n                    user_id,\n                    brand_name,\n                    commission_pct,\n                    users:user_id (\n                        email\n                    )\n                `)\n                .neq('user_id', user.id); // Exclude current agent\n\n            if (error) {\n                console.error('Error fetching agents:', error);\n                return;\n            }\n\n            setAvailableAgents(agents || []);\n        } catch (error) {\n            console.error('Error in fetchAvailableAgents:', error);\n        }\n    };\n\n    const getStatusBadge = (status) => {\n        switch (status) {\n            case 'approved':\n                return <Badge bg=\"success\">{t('approved')}</Badge>;\n            case 'pending':\n                return <Badge bg=\"warning\">{t('pending_review')}</Badge>;\n            case 'rejected':\n                return <Badge bg=\"danger\">{t('rejected')}</Badge>;\n            case 'under_review':\n                return <Badge bg=\"info\">{t('under_review')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{status || t('not_submitted')}</Badge>;\n        }\n    };\n\n    const handleSearch = () => {\n        // Search is handled by useEffect, this function can be used for additional logic if needed\n        console.log('Search triggered');\n    };\n\n    const handleAddMember = () => {\n        // TODO: Implement add member functionality\n        alert(t('add_member_coming_soon'));\n    };\n\n    const handleKycReview = (member) => {\n        setSelectedMember(member);\n        setShowKycModal(true);\n        setKycError('');\n        setKycSuccess('');\n    };\n\n    const handleKycDecision = async (decision) => {\n        if (!selectedMember) return;\n\n        setKycLoading(true);\n        setKycError('');\n        setKycSuccess('');\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error('Database connection failed');\n            }\n\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .update({ verify_status: decision })\n                .eq('user_id', selectedMember.user_id)\n                .select();\n\n            if (error) {\n                console.error('Database error:', error);\n                throw error;\n            }\n\n            // Check if any rows were updated\n            if (!data || data.length === 0) {\n                \n                // Try to find the record\n                const { data: existingRecord, error: selectError } = await supabase\n                    .from('customer_profiles')\n                    .select('*')\n                    .eq('user_id', selectedMember.user_id);\n\n                if (selectError) {\n                    console.error('Error checking existing record:', selectError);\n                    throw selectError;\n                }\n                \n                if (!existingRecord || existingRecord.length === 0) {\n                    throw new Error('Customer profile not found');\n                }\n            }\n\n            // Update local state\n            setMembers(prevMembers => \n                prevMembers.map(member => \n                    member.user_id === selectedMember.user_id \n                        ? { ...member, verify_status: decision }\n                        : member\n                )\n            );\n\n            // Also update the selected member\n            setSelectedMember(prev => ({ ...prev, verify_status: decision }));\n\n            setKycSuccess(decision === 'approved' ? t('kyc_approved_success') : t('kyc_rejected_success'));\n            \n            // Close modal after 1.5 seconds\n            setTimeout(() => {\n                setShowKycModal(false);\n                setSelectedMember(null);\n            }, 1500);\n\n        } catch (error) {\n            console.error('Error updating KYC status:', error);\n            setKycError(error.message || t('kyc_update_error'));\n        } finally {\n            setKycLoading(false);\n        }\n    };\n\n    const closeKycModal = () => {\n        setShowKycModal(false);\n        setSelectedMember(null);\n        setKycError('');\n        setKycSuccess('');\n    };\n\n    const handleChangeAgent = async (member) => {\n        setChangeAgentMember(member);\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n        setSelectedNewAgent('');\n        \n        // Fetch available agents\n        await fetchAvailableAgents();\n        \n        setShowChangeAgentModal(true);\n    };\n\n    const handleConfirmChangeAgent = async () => {\n        if (!changeAgentMember || !selectedNewAgent) {\n            setChangeAgentError(t('please_select_agent'));\n            return;\n        }\n\n        setChangeAgentLoading(true);\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error('Database connection failed');\n            }\n\n            // Update customer's agent_id\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .update({ agent_id: selectedNewAgent })\n                .eq('user_id', changeAgentMember.user_id)\n                .select();\n\n            if (error) {\n                console.error('Database error:', error);\n                throw error;\n            }\n\n            if (!data || data.length === 0) {\n                throw new Error('Failed to update agent assignment');\n            }\n\n            // Remove the member from current list since they're no longer assigned to current agent\n            setMembers(prevMembers => \n                prevMembers.filter(member => member.user_id !== changeAgentMember.user_id)\n            );\n\n            setChangeAgentSuccess(t('agent_changed_successfully'));\n            \n            // Close modal after 1.5 seconds\n            setTimeout(() => {\n                setShowChangeAgentModal(false);\n                setChangeAgentMember(null);\n                setSelectedNewAgent('');\n            }, 1500);\n\n        } catch (error) {\n            console.error('Error changing agent:', error);\n            setChangeAgentError(error.message || t('agent_change_error'));\n        } finally {\n            setChangeAgentLoading(false);\n        }\n    };\n\n    const closeChangeAgentModal = () => {\n        setShowChangeAgentModal(false);\n        setChangeAgentMember(null);\n        setSelectedNewAgent('');\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n    };\n\n\n    if (loading) {\n        return <div>{t('loading_members')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('member_list')}</h2>\n            \n            {/* Top Operation Bar */}\n            <Row className=\"mb-4\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={2}>\n                                    <Button \n                                        variant=\"primary\" \n                                        onClick={handleAddMember}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaPlus className=\"me-1\" />\n                                        {t('add_member')}\n                                    </Button>\n                                </Col>\n                                <Col md={3}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_username')}</Form.Label>\n                                        <InputGroup>\n                                            <Form.Control\n                                                type=\"text\"\n                                                placeholder={t('please_enter_username')}\n                                                value={searchTerm}\n                                                onChange={(e) => setSearchTerm(e.target.value)}\n                                            />\n                                        </InputGroup>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('status_filter')}</Form.Label>\n                                        <Form.Select\n                                            value={statusFilter}\n                                            onChange={(e) => setStatusFilter(e.target.value)}\n                                        >\n                                            <option value=\"\">{t('please_select_status')}</option>\n                                            <option value=\"pending\">{t('pending_review')}</option>\n                                            <option value=\"approved\">{t('approved')}</option>\n                                            <option value=\"rejected\">{t('rejected')}</option>\n                                            <option value=\"under_review\">{t('under_review')}</option>\n                                        </Form.Select>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('start_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={startDate}\n                                            onChange={(e) => setStartDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('end_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={endDate}\n                                            onChange={(e) => setEndDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={1}>\n                                    <Button \n                                        variant=\"outline-primary\" \n                                        onClick={handleSearch}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaSearch />\n                                    </Button>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* Members Table */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('username')}</th>\n                                        <th>{t('real_name')}</th>\n                                        <th>{t('id_number')}</th>\n                                        <th>{t('id_front_image')}</th>\n                                        <th>{t('id_back_image')}</th>\n                                        <th>{t('status')}</th>\n                                        <th>{t('registration_time')}</th>\n                                        <th>{t('actions')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {filteredMembers.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"9\" className=\"text-center\">{t('no_members_found')}</td>\n                                        </tr>\n                                    ) : (\n                                        filteredMembers.map(member => (\n                                            <tr key={member.user_id}>\n                                                <td>{member.users?.email || '-'}</td>\n                                                <td>{member.real_name || '-'}</td>\n                                                <td>{member.id_number || '-'}</td>\n                                                <td>\n                                                    {member.id_img_front ? (\n                                                        <img \n                                                            src={member.id_img_front} \n                                                            alt=\"ID Front\" \n                                                            style={{ \n                                                                width: '60px', \n                                                                height: '40px', \n                                                                objectFit: 'cover', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer'\n                                                            }}\n                                                            onClick={() => window.open(member.id_img_front, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <span className=\"text-muted\">-</span>\n                                                    )}\n                                                </td>\n                                                <td>\n                                                    {member.id_img_back ? (\n                                                        <img \n                                                            src={member.id_img_back} \n                                                            alt=\"ID Back\" \n                                                            style={{ \n                                                                width: '60px', \n                                                                height: '40px', \n                                                                objectFit: 'cover', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer'\n                                                            }}\n                                                            onClick={() => window.open(member.id_img_back, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <span className=\"text-muted\">-</span>\n                                                    )}\n                                                </td>\n                                                <td>{getStatusBadge(member.verify_status)}</td>\n                                                <td>{member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'}</td>\n                                                <td>\n                                                    <div className=\"d-flex gap-1\">\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-primary\"\n                                                            onClick={() => handleKycReview(member)}\n                                                            title={t('kyc_review')}\n                                                        >\n                                                            <FaUserCheck />\n                                                        </Button>\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-warning\"\n                                                            onClick={() => handleChangeAgent(member)}\n                                                            title={t('change_agent')}\n                                                        >\n                                                            <FaExchangeAlt />\n                                                        </Button>\n                                                    </div>\n                                                </td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* KYC Review Modal */}\n            <Modal show={showKycModal} onHide={closeKycModal} size=\"lg\">\n                <Modal.Header closeButton className=\"custom-modal-header\">\n                    <Modal.Title>{t('kyc_review')}</Modal.Title>\n                </Modal.Header>\n                <style dangerouslySetInnerHTML={{\n                    __html: `\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    `\n                }} />\n                <Modal.Body>\n                    {selectedMember && (\n                        <>\n                            {kycError && (\n                                <Alert variant=\"danger\" className=\"mb-3\">\n                                    {kycError}\n                                </Alert>\n                            )}\n                            {kycSuccess && (\n                                <Alert variant=\"success\" className=\"mb-3\">\n                                    {kycSuccess}\n                                </Alert>\n                            )}\n                            \n                            <Row>\n                                <Col md={6}>\n                                    <Card className=\"mb-3\">\n                                        <Card.Header>\n                                            <strong>{t('customer_info')}</strong>\n                                        </Card.Header>\n                                        <Card.Body>\n                                            <p><strong>{t('username')}:</strong> {selectedMember.users?.email || '-'}</p>\n                                            <p><strong>{t('real_name')}:</strong> {selectedMember.real_name || '-'}</p>\n                                            <p><strong>{t('id_number')}:</strong> {selectedMember.id_number || '-'}</p>\n                                            <p><strong>{t('current_status')}:</strong> {getStatusBadge(selectedMember.verify_status)}</p>\n                                            <p><strong>{t('registration_time')}:</strong> {selectedMember.users?.created_at ? new Date(selectedMember.users.created_at).toLocaleString() : '-'}</p>\n                                        </Card.Body>\n                                    </Card>\n                                </Col>\n                                <Col md={6}>\n                                    <Card className=\"mb-3\">\n                                        <Card.Header>\n                                            <strong>{t('id_documents')}</strong>\n                                        </Card.Header>\n                                        <Card.Body>\n                                            <div className=\"mb-3\">\n                                                <strong>{t('id_front_image')}:</strong>\n                                                <div className=\"mt-2\">\n                                                    {selectedMember.id_img_front ? (\n                                                        <img \n                                                            src={selectedMember.id_img_front} \n                                                            alt=\"ID Front\" \n                                                            style={{ \n                                                                width: '100%', \n                                                                maxHeight: '150px', \n                                                                objectFit: 'contain', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer',\n                                                                border: '1px solid #dee2e6'\n                                                            }}\n                                                            onClick={() => window.open(selectedMember.id_img_front, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <div className=\"text-muted text-center py-3\" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>\n                                                            {t('no_image_uploaded')}\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                            <div className=\"mb-3\">\n                                                <strong>{t('id_back_image')}:</strong>\n                                                <div className=\"mt-2\">\n                                                    {selectedMember.id_img_back ? (\n                                                        <img \n                                                            src={selectedMember.id_img_back} \n                                                            alt=\"ID Back\" \n                                                            style={{ \n                                                                width: '100%', \n                                                                maxHeight: '150px', \n                                                                objectFit: 'contain', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer',\n                                                                border: '1px solid #dee2e6'\n                                                            }}\n                                                            onClick={() => window.open(selectedMember.id_img_back, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <div className=\"text-muted text-center py-3\" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>\n                                                            {t('no_image_uploaded')}\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                        </Card.Body>\n                                    </Card>\n                                </Col>\n                            </Row>\n                        </>\n                    )}\n                </Modal.Body>\n                <Modal.Footer>\n                    <Button variant=\"secondary\" onClick={closeKycModal} disabled={kycLoading}>\n                        {t('cancel')}\n                    </Button>\n                    <Button \n                        variant=\"danger\" \n                        onClick={() => handleKycDecision('rejected')}\n                        disabled={kycLoading || selectedMember?.verify_status === 'rejected'}\n                    >\n                        {kycLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaTimes className=\"me-1\" />\n                                {t('reject')}\n                            </>\n                        )}\n                    </Button>\n                    <Button \n                        variant=\"success\" \n                        onClick={() => handleKycDecision('approved')}\n                        disabled={kycLoading || selectedMember?.verify_status === 'approved'}\n                    >\n                        {kycLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaCheck className=\"me-1\" />\n                                {t('approve')}\n                            </>\n                        )}\n                    </Button>\n                </Modal.Footer>\n            </Modal>\n\n            {/* Change Agent Modal */}\n            <Modal show={showChangeAgentModal} onHide={closeChangeAgentModal} size=\"md\">\n                <Modal.Header closeButton>\n                    <Modal.Title>{t('change_agent')}</Modal.Title>\n                </Modal.Header>\n                <Modal.Body>\n                    {changeAgentError && (\n                        <Alert variant=\"danger\" className=\"mb-3\">\n                            {changeAgentError}\n                        </Alert>\n                    )}\n                    {changeAgentSuccess && (\n                        <Alert variant=\"success\" className=\"mb-3\">\n                            {changeAgentSuccess}\n                        </Alert>\n                    )}\n                    \n                    {changeAgentMember && (\n                        <div className=\"mb-4\">\n                            <Card>\n                                <Card.Header>\n                                    <strong>{t('customer_info')}</strong>\n                                </Card.Header>\n                                <Card.Body>\n                                    <p><strong>{t('username')}:</strong> {changeAgentMember.users?.email || '-'}</p>\n                                    <p><strong>{t('real_name')}:</strong> {changeAgentMember.real_name || '-'}</p>\n                                    <p><strong>{t('current_status')}:</strong> {getStatusBadge(changeAgentMember.verify_status)}</p>\n                                </Card.Body>\n                            </Card>\n                        </div>\n                    )}\n\n                    <Form.Group className=\"mb-3\">\n                        <Form.Label><strong>{t('select_new_agent')}</strong></Form.Label>\n                        <Form.Select\n                            value={selectedNewAgent}\n                            onChange={(e) => setSelectedNewAgent(e.target.value)}\n                            disabled={changeAgentLoading}\n                        >\n                            <option value=\"\">{t('please_select_agent')}</option>\n                            {availableAgents.map(agent => (\n                                <option key={agent.user_id} value={agent.user_id}>\n                                    {agent.brand_name || agent.users?.email || agent.user_id} \n                                    {agent.commission_pct && ` (${agent.commission_pct}%)`}\n                                </option>\n                            ))}\n                        </Form.Select>\n                        {availableAgents.length === 0 && (\n                            <Form.Text className=\"text-muted\">\n                                {t('no_available_agents')}\n                            </Form.Text>\n                        )}\n                    </Form.Group>\n                </Modal.Body>\n                <Modal.Footer>\n                    <Button variant=\"secondary\" onClick={closeChangeAgentModal} disabled={changeAgentLoading}>\n                        {t('cancel')}\n                    </Button>\n                    <Button \n                        variant=\"primary\" \n                        onClick={handleConfirmChangeAgent}\n                        disabled={changeAgentLoading || !selectedNewAgent || availableAgents.length === 0}\n                    >\n                        {changeAgentLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaExchangeAlt className=\"me-1\" />\n                                {t('confirm_change')}\n                            </>\n                        )}\n                    </Button>\n                </Modal.Footer>\n            </Modal>\n        </Container>\n    );\n};\n\nexport default Members;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,MAAM,CAAEC,IAAI,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CAC3H,OAASC,QAAQ,CAAEC,MAAM,CAAEC,WAAW,CAAEC,aAAa,CAAEC,OAAO,CAAEC,OAAO,KAAQ,gBAAgB,CAC/F,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/C,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAClB,KAAM,CAAEC,CAAE,CAAC,CAAGX,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACY,OAAO,CAAEC,UAAU,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACmC,OAAO,CAAEC,UAAU,CAAC,CAAGpC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqC,UAAU,CAAEC,aAAa,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACuC,YAAY,CAAEC,eAAe,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACyC,SAAS,CAAEC,YAAY,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC2C,OAAO,CAAEC,UAAU,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC6C,eAAe,CAAEC,kBAAkB,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC+C,YAAY,CAAEC,eAAe,CAAC,CAAGhD,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACiD,cAAc,CAAEC,iBAAiB,CAAC,CAAGlD,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACmD,UAAU,CAAEC,aAAa,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACqD,QAAQ,CAAEC,WAAW,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACuD,UAAU,CAAEC,aAAa,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CAEhD;AACA,KAAM,CAACyD,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG1D,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAAC2D,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5D,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAAC6D,eAAe,CAAEC,kBAAkB,CAAC,CAAG9D,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC+D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACiE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlE,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACmE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpE,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACqE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CAEhEC,SAAS,CAAC,IAAM,CACR,KAAM,CAAAsE,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,QAAQ,CAAGpD,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACoD,QAAQ,CAAE,OAEfpC,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEqC,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPtC,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEqC,IAAI,CAAEI,SAAS,CAAEC,KAAK,CAAEC,YAAa,CAAC,CAAG,KAAM,CAAAP,QAAQ,CAC1DQ,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,yEAAyE,CAAC,CACjFC,EAAE,CAAC,UAAU,CAAER,IAAI,CAACS,EAAE,CAAC,CACvBC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIN,YAAY,EAAI,CAACF,SAAS,CAAE,CAC5BS,OAAO,CAACR,KAAK,CAAC,mCAAmC,CAAEC,YAAY,CAAC,CAChE3C,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAAmD,OAAO,CAAGV,SAAS,CAACW,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAE7D,KAAM,CAAEnB,IAAI,CAAEoB,YAAY,CAAEf,KAAK,CAAEgB,SAAU,CAAC,CAAG,KAAM,CAAAtB,QAAQ,CAC1DQ,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,uBAAuB,CAAC,CAEpC,GAAIa,SAAS,CAAE,CACXR,OAAO,CAACR,KAAK,CAAC,uBAAuB,CAAEgB,SAAS,CAAC,CACrD,CAEA;AACA,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,GAAG,CAAC,CAACH,YAAY,EAAI,EAAE,EAAEL,GAAG,CAACS,CAAC,EAAI,CAACA,CAAC,CAACd,EAAE,CAAEc,CAAC,CAAC,CAAC,CAAC,CAElE,KAAM,CAAAC,eAAe,CAAGrB,SAAS,CAACW,GAAG,CAACC,CAAC,GAAK,CACxC,GAAGA,CAAC,CACJU,KAAK,CAAEJ,QAAQ,CAACK,GAAG,CAACX,CAAC,CAACC,OAAO,CAAC,EAAI,CAAC,CACvC,CAAC,CAAC,CAAC,CAEHxD,UAAU,CAACgE,eAAe,CAAC,CAC3B9D,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAELmC,YAAY,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN;AACAtE,SAAS,CAAC,IAAM,CACZ,GAAI,CAAAoG,QAAQ,CAAGpE,OAAO,CAEtB;AACA,GAAII,UAAU,CAAE,CACZgE,QAAQ,CAAGA,QAAQ,CAACV,MAAM,CAACW,MAAM,OAAAC,aAAA,CAAAC,mBAAA,CAAAC,iBAAA,OAC7B,EAAAF,aAAA,CAAAD,MAAM,CAACH,KAAK,UAAAI,aAAA,kBAAAC,mBAAA,CAAZD,aAAA,CAAcG,KAAK,UAAAF,mBAAA,iBAAnBA,mBAAA,CAAqBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvE,UAAU,CAACsE,WAAW,CAAC,CAAC,CAAC,KAAAF,iBAAA,CACrEH,MAAM,CAACO,SAAS,UAAAJ,iBAAA,iBAAhBA,iBAAA,CAAkBE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvE,UAAU,CAACsE,WAAW,CAAC,CAAC,CAAC,GACtE,CAAC,CACL,CAEA;AACA,GAAIpE,YAAY,CAAE,CACd8D,QAAQ,CAAGA,QAAQ,CAACV,MAAM,CAACW,MAAM,EAAIA,MAAM,CAACQ,aAAa,GAAKvE,YAAY,CAAC,CAC/E,CAEA;AACA,GAAIE,SAAS,CAAE,CACX4D,QAAQ,CAAGA,QAAQ,CAACV,MAAM,CAACW,MAAM,OAAAS,cAAA,OAC7B,IAAI,CAAAC,IAAI,EAAAD,cAAA,CAACT,MAAM,CAACH,KAAK,UAAAY,cAAA,iBAAZA,cAAA,CAAcE,UAAU,CAAC,EAAI,GAAI,CAAAD,IAAI,CAACvE,SAAS,CAAC,EAC7D,CAAC,CACL,CACA,GAAIE,OAAO,CAAE,CACT0D,QAAQ,CAAGA,QAAQ,CAACV,MAAM,CAACW,MAAM,OAAAY,cAAA,OAC7B,IAAI,CAAAF,IAAI,EAAAE,cAAA,CAACZ,MAAM,CAACH,KAAK,UAAAe,cAAA,iBAAZA,cAAA,CAAcD,UAAU,CAAC,EAAI,GAAI,CAAAD,IAAI,CAACrE,OAAO,CAAC,EAC3D,CAAC,CACL,CAEAG,kBAAkB,CAACuD,QAAQ,CAAC,CAChC,CAAC,CAAE,CAACpE,OAAO,CAAEI,UAAU,CAAEE,YAAY,CAAEE,SAAS,CAAEE,OAAO,CAAC,CAAC,CAE3D;AACA,KAAM,CAAAwE,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACrC,KAAM,CAAA3C,QAAQ,CAAGpD,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACoD,QAAQ,CAAE,OAEf,GAAI,CACA;AACA,KAAM,CAAEC,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CACxD,GAAI,CAACF,IAAI,CAAE,OAEX;AACA,KAAM,CAAED,IAAI,CAAE2C,MAAM,CAAEtC,KAAM,CAAC,CAAG,KAAM,CAAAN,QAAQ,CACzCQ,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDoC,GAAG,CAAC,SAAS,CAAE3C,IAAI,CAACS,EAAE,CAAC,CAAE;AAE9B,GAAIL,KAAK,CAAE,CACPQ,OAAO,CAACR,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,OACJ,CAEAhB,kBAAkB,CAACsD,MAAM,EAAI,EAAE,CAAC,CACpC,CAAE,MAAOtC,KAAK,CAAE,CACZQ,OAAO,CAACR,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CAC1D,CACJ,CAAC,CAED,KAAM,CAAAwC,cAAc,CAAIC,MAAM,EAAK,CAC/B,OAAQA,MAAM,EACV,IAAK,UAAU,CACX,mBAAOhG,IAAA,CAAChB,KAAK,EAACiH,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAEzF,CAAC,CAAC,UAAU,CAAC,CAAQ,CAAC,CACtD,IAAK,SAAS,CACV,mBAAOT,IAAA,CAAChB,KAAK,EAACiH,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAEzF,CAAC,CAAC,gBAAgB,CAAC,CAAQ,CAAC,CAC5D,IAAK,UAAU,CACX,mBAAOT,IAAA,CAAChB,KAAK,EAACiH,EAAE,CAAC,QAAQ,CAAAC,QAAA,CAAEzF,CAAC,CAAC,UAAU,CAAC,CAAQ,CAAC,CACrD,IAAK,cAAc,CACf,mBAAOT,IAAA,CAAChB,KAAK,EAACiH,EAAE,CAAC,MAAM,CAAAC,QAAA,CAAEzF,CAAC,CAAC,cAAc,CAAC,CAAQ,CAAC,CACvD,QACI,mBAAOT,IAAA,CAAChB,KAAK,EAACiH,EAAE,CAAC,WAAW,CAAAC,QAAA,CAAEF,MAAM,EAAIvF,CAAC,CAAC,eAAe,CAAC,CAAQ,CAAC,CAC3E,CACJ,CAAC,CAED,KAAM,CAAA0F,YAAY,CAAGA,CAAA,GAAM,CACvB;AACApC,OAAO,CAACqC,GAAG,CAAC,kBAAkB,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC1B;AACAC,KAAK,CAAC7F,CAAC,CAAC,wBAAwB,CAAC,CAAC,CACtC,CAAC,CAED,KAAM,CAAA8F,eAAe,CAAIxB,MAAM,EAAK,CAChCpD,iBAAiB,CAACoD,MAAM,CAAC,CACzBtD,eAAe,CAAC,IAAI,CAAC,CACrBM,WAAW,CAAC,EAAE,CAAC,CACfE,aAAa,CAAC,EAAE,CAAC,CACrB,CAAC,CAED,KAAM,CAAAuE,iBAAiB,CAAG,KAAO,CAAAC,QAAQ,EAAK,CAC1C,GAAI,CAAC/E,cAAc,CAAE,OAErBG,aAAa,CAAC,IAAI,CAAC,CACnBE,WAAW,CAAC,EAAE,CAAC,CACfE,aAAa,CAAC,EAAE,CAAC,CAEjB,GAAI,CACA,KAAM,CAAAgB,QAAQ,CAAGpD,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACoD,QAAQ,CAAE,CACX,KAAM,IAAI,CAAAyD,KAAK,CAAC,4BAA4B,CAAC,CACjD,CAEA,KAAM,CAAExD,IAAI,CAAEK,KAAM,CAAC,CAAG,KAAM,CAAAN,QAAQ,CACjCQ,IAAI,CAAC,mBAAmB,CAAC,CACzBkD,MAAM,CAAC,CAAEpB,aAAa,CAAEkB,QAAS,CAAC,CAAC,CACnC9C,EAAE,CAAC,SAAS,CAAEjC,cAAc,CAACyC,OAAO,CAAC,CACrCT,MAAM,CAAC,CAAC,CAEb,GAAIH,KAAK,CAAE,CACPQ,OAAO,CAACR,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,KAAM,CAAAA,KAAK,CACf,CAEA;AACA,GAAI,CAACL,IAAI,EAAIA,IAAI,CAAC0D,MAAM,GAAK,CAAC,CAAE,CAE5B;AACA,KAAM,CAAE1D,IAAI,CAAE2D,cAAc,CAAEtD,KAAK,CAAEuD,WAAY,CAAC,CAAG,KAAM,CAAA7D,QAAQ,CAC9DQ,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,CAAEjC,cAAc,CAACyC,OAAO,CAAC,CAE1C,GAAI2C,WAAW,CAAE,CACb/C,OAAO,CAACR,KAAK,CAAC,iCAAiC,CAAEuD,WAAW,CAAC,CAC7D,KAAM,CAAAA,WAAW,CACrB,CAEA,GAAI,CAACD,cAAc,EAAIA,cAAc,CAACD,MAAM,GAAK,CAAC,CAAE,CAChD,KAAM,IAAI,CAAAF,KAAK,CAAC,4BAA4B,CAAC,CACjD,CACJ,CAEA;AACA/F,UAAU,CAACoG,WAAW,EAClBA,WAAW,CAAC9C,GAAG,CAACc,MAAM,EAClBA,MAAM,CAACZ,OAAO,GAAKzC,cAAc,CAACyC,OAAO,CACnC,CAAE,GAAGY,MAAM,CAAEQ,aAAa,CAAEkB,QAAS,CAAC,CACtC1B,MACV,CACJ,CAAC,CAED;AACApD,iBAAiB,CAACqF,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEzB,aAAa,CAAEkB,QAAS,CAAC,CAAC,CAAC,CAEjExE,aAAa,CAACwE,QAAQ,GAAK,UAAU,CAAGhG,CAAC,CAAC,sBAAsB,CAAC,CAAGA,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAE9F;AACAwG,UAAU,CAAC,IAAM,CACbxF,eAAe,CAAC,KAAK,CAAC,CACtBE,iBAAiB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAAE,IAAI,CAAC,CAEZ,CAAE,MAAO4B,KAAK,CAAE,CACZQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDxB,WAAW,CAACwB,KAAK,CAAC2D,OAAO,EAAIzG,CAAC,CAAC,kBAAkB,CAAC,CAAC,CACvD,CAAC,OAAS,CACNoB,aAAa,CAAC,KAAK,CAAC,CACxB,CACJ,CAAC,CAED,KAAM,CAAAsF,aAAa,CAAGA,CAAA,GAAM,CACxB1F,eAAe,CAAC,KAAK,CAAC,CACtBE,iBAAiB,CAAC,IAAI,CAAC,CACvBI,WAAW,CAAC,EAAE,CAAC,CACfE,aAAa,CAAC,EAAE,CAAC,CACrB,CAAC,CAED,KAAM,CAAAmF,iBAAiB,CAAG,KAAO,CAAArC,MAAM,EAAK,CACxC1C,oBAAoB,CAAC0C,MAAM,CAAC,CAC5BlC,mBAAmB,CAAC,EAAE,CAAC,CACvBE,qBAAqB,CAAC,EAAE,CAAC,CACzBN,mBAAmB,CAAC,EAAE,CAAC,CAEvB;AACA,KAAM,CAAAmD,oBAAoB,CAAC,CAAC,CAE5BzD,uBAAuB,CAAC,IAAI,CAAC,CACjC,CAAC,CAED,KAAM,CAAAkF,wBAAwB,CAAG,KAAAA,CAAA,GAAY,CACzC,GAAI,CAACjF,iBAAiB,EAAI,CAACI,gBAAgB,CAAE,CACzCK,mBAAmB,CAACpC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAC7C,OACJ,CAEAkC,qBAAqB,CAAC,IAAI,CAAC,CAC3BE,mBAAmB,CAAC,EAAE,CAAC,CACvBE,qBAAqB,CAAC,EAAE,CAAC,CAEzB,GAAI,CACA,KAAM,CAAAE,QAAQ,CAAGpD,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACoD,QAAQ,CAAE,CACX,KAAM,IAAI,CAAAyD,KAAK,CAAC,4BAA4B,CAAC,CACjD,CAEA;AACA,KAAM,CAAExD,IAAI,CAAEK,KAAM,CAAC,CAAG,KAAM,CAAAN,QAAQ,CACjCQ,IAAI,CAAC,mBAAmB,CAAC,CACzBkD,MAAM,CAAC,CAAEW,QAAQ,CAAE9E,gBAAiB,CAAC,CAAC,CACtCmB,EAAE,CAAC,SAAS,CAAEvB,iBAAiB,CAAC+B,OAAO,CAAC,CACxCT,MAAM,CAAC,CAAC,CAEb,GAAIH,KAAK,CAAE,CACPQ,OAAO,CAACR,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,KAAM,CAAAA,KAAK,CACf,CAEA,GAAI,CAACL,IAAI,EAAIA,IAAI,CAAC0D,MAAM,GAAK,CAAC,CAAE,CAC5B,KAAM,IAAI,CAAAF,KAAK,CAAC,mCAAmC,CAAC,CACxD,CAEA;AACA/F,UAAU,CAACoG,WAAW,EAClBA,WAAW,CAAC3C,MAAM,CAACW,MAAM,EAAIA,MAAM,CAACZ,OAAO,GAAK/B,iBAAiB,CAAC+B,OAAO,CAC7E,CAAC,CAEDpB,qBAAqB,CAACtC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAEtD;AACAwG,UAAU,CAAC,IAAM,CACb9E,uBAAuB,CAAC,KAAK,CAAC,CAC9BE,oBAAoB,CAAC,IAAI,CAAC,CAC1BI,mBAAmB,CAAC,EAAE,CAAC,CAC3B,CAAC,CAAE,IAAI,CAAC,CAEZ,CAAE,MAAOc,KAAK,CAAE,CACZQ,OAAO,CAACR,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CV,mBAAmB,CAACU,KAAK,CAAC2D,OAAO,EAAIzG,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACjE,CAAC,OAAS,CACNkC,qBAAqB,CAAC,KAAK,CAAC,CAChC,CACJ,CAAC,CAED,KAAM,CAAA4E,qBAAqB,CAAGA,CAAA,GAAM,CAChCpF,uBAAuB,CAAC,KAAK,CAAC,CAC9BE,oBAAoB,CAAC,IAAI,CAAC,CAC1BI,mBAAmB,CAAC,EAAE,CAAC,CACvBI,mBAAmB,CAAC,EAAE,CAAC,CACvBE,qBAAqB,CAAC,EAAE,CAAC,CAC7B,CAAC,CAGD,GAAInC,OAAO,CAAE,CACT,mBAAOZ,IAAA,QAAAkG,QAAA,CAAMzF,CAAC,CAAC,iBAAiB,CAAC,CAAM,CAAC,CAC5C,CAEA,mBACIP,KAAA,CAACvB,SAAS,EAAAuH,QAAA,eACNlG,IAAA,OAAIwH,SAAS,CAAC,MAAM,CAAAtB,QAAA,CAAEzF,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,cAG5CT,IAAA,CAACpB,GAAG,EAAC4I,SAAS,CAAC,MAAM,CAAAtB,QAAA,cACjBlG,IAAA,CAACnB,GAAG,EAAAqH,QAAA,cACAlG,IAAA,CAAClB,IAAI,EAAAoH,QAAA,cACDlG,IAAA,CAAClB,IAAI,CAAC2I,IAAI,EAAAvB,QAAA,cACNhG,KAAA,CAACtB,GAAG,EAAC4I,SAAS,CAAC,iBAAiB,CAAAtB,QAAA,eAC5BlG,IAAA,CAACnB,GAAG,EAAC6I,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACPhG,KAAA,CAACjB,MAAM,EACH0I,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEvB,eAAgB,CACzBmB,SAAS,CAAC,MAAM,CAAAtB,QAAA,eAEhBlG,IAAA,CAACR,MAAM,EAACgI,SAAS,CAAC,MAAM,CAAE,CAAC,CAC1B/G,CAAC,CAAC,YAAY,CAAC,EACZ,CAAC,CACR,CAAC,cACNT,IAAA,CAACnB,GAAG,EAAC6I,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACPhG,KAAA,CAAChB,IAAI,CAAC2I,KAAK,EAAA3B,QAAA,eACPlG,IAAA,CAACd,IAAI,CAAC4I,KAAK,EAAA5B,QAAA,CAAEzF,CAAC,CAAC,iBAAiB,CAAC,CAAa,CAAC,cAC/CT,IAAA,CAACb,UAAU,EAAA+G,QAAA,cACPlG,IAAA,CAACd,IAAI,CAAC6I,OAAO,EACTC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAExH,CAAC,CAAC,uBAAuB,CAAE,CACxCyH,KAAK,CAAEpH,UAAW,CAClBqH,QAAQ,CAAGC,CAAC,EAAKrH,aAAa,CAACqH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClD,CAAC,CACM,CAAC,EACL,CAAC,CACZ,CAAC,cACNlI,IAAA,CAACnB,GAAG,EAAC6I,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACPhG,KAAA,CAAChB,IAAI,CAAC2I,KAAK,EAAA3B,QAAA,eACPlG,IAAA,CAACd,IAAI,CAAC4I,KAAK,EAAA5B,QAAA,CAAEzF,CAAC,CAAC,eAAe,CAAC,CAAa,CAAC,cAC7CP,KAAA,CAAChB,IAAI,CAACoJ,MAAM,EACRJ,KAAK,CAAElH,YAAa,CACpBmH,QAAQ,CAAGC,CAAC,EAAKnH,eAAe,CAACmH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAhC,QAAA,eAEjDlG,IAAA,WAAQkI,KAAK,CAAC,EAAE,CAAAhC,QAAA,CAAEzF,CAAC,CAAC,sBAAsB,CAAC,CAAS,CAAC,cACrDT,IAAA,WAAQkI,KAAK,CAAC,SAAS,CAAAhC,QAAA,CAAEzF,CAAC,CAAC,gBAAgB,CAAC,CAAS,CAAC,cACtDT,IAAA,WAAQkI,KAAK,CAAC,UAAU,CAAAhC,QAAA,CAAEzF,CAAC,CAAC,UAAU,CAAC,CAAS,CAAC,cACjDT,IAAA,WAAQkI,KAAK,CAAC,UAAU,CAAAhC,QAAA,CAAEzF,CAAC,CAAC,UAAU,CAAC,CAAS,CAAC,cACjDT,IAAA,WAAQkI,KAAK,CAAC,cAAc,CAAAhC,QAAA,CAAEzF,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,EAChD,CAAC,EACN,CAAC,CACZ,CAAC,cACNT,IAAA,CAACnB,GAAG,EAAC6I,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACPhG,KAAA,CAAChB,IAAI,CAAC2I,KAAK,EAAA3B,QAAA,eACPlG,IAAA,CAACd,IAAI,CAAC4I,KAAK,EAAA5B,QAAA,CAAEzF,CAAC,CAAC,YAAY,CAAC,CAAa,CAAC,cAC1CT,IAAA,CAACd,IAAI,CAAC6I,OAAO,EACTC,IAAI,CAAC,MAAM,CACXE,KAAK,CAAEhH,SAAU,CACjBiH,QAAQ,CAAGC,CAAC,EAAKjH,YAAY,CAACiH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjD,CAAC,EACM,CAAC,CACZ,CAAC,cACNlI,IAAA,CAACnB,GAAG,EAAC6I,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACPhG,KAAA,CAAChB,IAAI,CAAC2I,KAAK,EAAA3B,QAAA,eACPlG,IAAA,CAACd,IAAI,CAAC4I,KAAK,EAAA5B,QAAA,CAAEzF,CAAC,CAAC,UAAU,CAAC,CAAa,CAAC,cACxCT,IAAA,CAACd,IAAI,CAAC6I,OAAO,EACTC,IAAI,CAAC,MAAM,CACXE,KAAK,CAAE9G,OAAQ,CACf+G,QAAQ,CAAGC,CAAC,EAAK/G,UAAU,CAAC+G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/C,CAAC,EACM,CAAC,CACZ,CAAC,cACNlI,IAAA,CAACnB,GAAG,EAAC6I,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACPlG,IAAA,CAACf,MAAM,EACH0I,OAAO,CAAC,iBAAiB,CACzBC,OAAO,CAAEzB,YAAa,CACtBqB,SAAS,CAAC,MAAM,CAAAtB,QAAA,cAEhBlG,IAAA,CAACT,QAAQ,GAAE,CAAC,CACR,CAAC,CACR,CAAC,EACL,CAAC,CACC,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAGNS,IAAA,CAACpB,GAAG,EAAAsH,QAAA,cACAlG,IAAA,CAACnB,GAAG,EAAAqH,QAAA,cACAlG,IAAA,CAAClB,IAAI,EAAAoH,QAAA,cACDlG,IAAA,CAAClB,IAAI,CAAC2I,IAAI,EAAAvB,QAAA,cACNhG,KAAA,CAACnB,KAAK,EAACwJ,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAxC,QAAA,eACpClG,IAAA,UAAAkG,QAAA,cACIhG,KAAA,OAAAgG,QAAA,eACIlG,IAAA,OAAAkG,QAAA,CAAKzF,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBT,IAAA,OAAAkG,QAAA,CAAKzF,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBT,IAAA,OAAAkG,QAAA,CAAKzF,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBT,IAAA,OAAAkG,QAAA,CAAKzF,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC9BT,IAAA,OAAAkG,QAAA,CAAKzF,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BT,IAAA,OAAAkG,QAAA,CAAKzF,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBT,IAAA,OAAAkG,QAAA,CAAKzF,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCT,IAAA,OAAAkG,QAAA,CAAKzF,CAAC,CAAC,SAAS,CAAC,CAAK,CAAC,EACvB,CAAC,CACF,CAAC,cACRT,IAAA,UAAAkG,QAAA,CACK5E,eAAe,CAACsF,MAAM,GAAK,CAAC,cACzB5G,IAAA,OAAAkG,QAAA,cACIlG,IAAA,OAAI2I,OAAO,CAAC,GAAG,CAACnB,SAAS,CAAC,aAAa,CAAAtB,QAAA,CAAEzF,CAAC,CAAC,kBAAkB,CAAC,CAAK,CAAC,CACpE,CAAC,CAELa,eAAe,CAAC2C,GAAG,CAACc,MAAM,OAAA6D,cAAA,CAAAC,cAAA,oBACtB3I,KAAA,OAAAgG,QAAA,eACIlG,IAAA,OAAAkG,QAAA,CAAK,EAAA0C,cAAA,CAAA7D,MAAM,CAACH,KAAK,UAAAgE,cAAA,iBAAZA,cAAA,CAAczD,KAAK,GAAI,GAAG,CAAK,CAAC,cACrCnF,IAAA,OAAAkG,QAAA,CAAKnB,MAAM,CAACO,SAAS,EAAI,GAAG,CAAK,CAAC,cAClCtF,IAAA,OAAAkG,QAAA,CAAKnB,MAAM,CAAC+D,SAAS,EAAI,GAAG,CAAK,CAAC,cAClC9I,IAAA,OAAAkG,QAAA,CACKnB,MAAM,CAACgE,YAAY,cAChB/I,IAAA,QACIgJ,GAAG,CAAEjE,MAAM,CAACgE,YAAa,CACzBE,GAAG,CAAC,UAAU,CACdC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,OAAO,CAClBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SACZ,CAAE,CACF3B,OAAO,CAAEA,CAAA,GAAM4B,MAAM,CAACC,IAAI,CAAC1E,MAAM,CAACgE,YAAY,CAAE,QAAQ,CAAE,CAC7D,CAAC,cAEF/I,IAAA,SAAMwH,SAAS,CAAC,YAAY,CAAAtB,QAAA,CAAC,GAAC,CAAM,CACvC,CACD,CAAC,cACLlG,IAAA,OAAAkG,QAAA,CACKnB,MAAM,CAAC2E,WAAW,cACf1J,IAAA,QACIgJ,GAAG,CAAEjE,MAAM,CAAC2E,WAAY,CACxBT,GAAG,CAAC,SAAS,CACbC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,OAAO,CAClBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SACZ,CAAE,CACF3B,OAAO,CAAEA,CAAA,GAAM4B,MAAM,CAACC,IAAI,CAAC1E,MAAM,CAAC2E,WAAW,CAAE,QAAQ,CAAE,CAC5D,CAAC,cAEF1J,IAAA,SAAMwH,SAAS,CAAC,YAAY,CAAAtB,QAAA,CAAC,GAAC,CAAM,CACvC,CACD,CAAC,cACLlG,IAAA,OAAAkG,QAAA,CAAKH,cAAc,CAAChB,MAAM,CAACQ,aAAa,CAAC,CAAK,CAAC,cAC/CvF,IAAA,OAAAkG,QAAA,CAAK,CAAA2C,cAAA,CAAA9D,MAAM,CAACH,KAAK,UAAAiE,cAAA,WAAZA,cAAA,CAAcnD,UAAU,CAAG,GAAI,CAAAD,IAAI,CAACV,MAAM,CAACH,KAAK,CAACc,UAAU,CAAC,CAACiE,cAAc,CAAC,CAAC,CAAG,GAAG,CAAK,CAAC,cAC9F3J,IAAA,OAAAkG,QAAA,cACIhG,KAAA,QAAKsH,SAAS,CAAC,cAAc,CAAAtB,QAAA,eACzBlG,IAAA,CAACf,MAAM,EACH2K,IAAI,CAAC,IAAI,CACTjC,OAAO,CAAC,iBAAiB,CACzBC,OAAO,CAAEA,CAAA,GAAMrB,eAAe,CAACxB,MAAM,CAAE,CACvC8E,KAAK,CAAEpJ,CAAC,CAAC,YAAY,CAAE,CAAAyF,QAAA,cAEvBlG,IAAA,CAACP,WAAW,GAAE,CAAC,CACX,CAAC,cACTO,IAAA,CAACf,MAAM,EACH2K,IAAI,CAAC,IAAI,CACTjC,OAAO,CAAC,iBAAiB,CACzBC,OAAO,CAAEA,CAAA,GAAMR,iBAAiB,CAACrC,MAAM,CAAE,CACzC8E,KAAK,CAAEpJ,CAAC,CAAC,cAAc,CAAE,CAAAyF,QAAA,cAEzBlG,IAAA,CAACN,aAAa,GAAE,CAAC,CACb,CAAC,EACR,CAAC,CACN,CAAC,GA7DAqF,MAAM,CAACZ,OA8DZ,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAGNjE,KAAA,CAACb,KAAK,EAACyK,IAAI,CAAEtI,YAAa,CAACuI,MAAM,CAAE5C,aAAc,CAACyC,IAAI,CAAC,IAAI,CAAA1D,QAAA,eACvDlG,IAAA,CAACX,KAAK,CAAC2K,MAAM,EAACC,WAAW,MAACzC,SAAS,CAAC,qBAAqB,CAAAtB,QAAA,cACrDlG,IAAA,CAACX,KAAK,CAAC6K,KAAK,EAAAhE,QAAA,CAAEzF,CAAC,CAAC,YAAY,CAAC,CAAc,CAAC,CAClC,CAAC,cACfT,IAAA,UAAOmK,uBAAuB,CAAE,CAC5BC,MAAM,CAAE;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBACgB,CAAE,CAAE,CAAC,cACLpK,IAAA,CAACX,KAAK,CAACoI,IAAI,EAAAvB,QAAA,CACNxE,cAAc,eACXxB,KAAA,CAAAE,SAAA,EAAA8F,QAAA,EACKpE,QAAQ,eACL9B,IAAA,CAACV,KAAK,EAACqI,OAAO,CAAC,QAAQ,CAACH,SAAS,CAAC,MAAM,CAAAtB,QAAA,CACnCpE,QAAQ,CACN,CACV,CACAE,UAAU,eACPhC,IAAA,CAACV,KAAK,EAACqI,OAAO,CAAC,SAAS,CAACH,SAAS,CAAC,MAAM,CAAAtB,QAAA,CACpClE,UAAU,CACR,CACV,cAED9B,KAAA,CAACtB,GAAG,EAAAsH,QAAA,eACAlG,IAAA,CAACnB,GAAG,EAAC6I,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACPhG,KAAA,CAACpB,IAAI,EAAC0I,SAAS,CAAC,MAAM,CAAAtB,QAAA,eAClBlG,IAAA,CAAClB,IAAI,CAACkL,MAAM,EAAA9D,QAAA,cACRlG,IAAA,WAAAkG,QAAA,CAASzF,CAAC,CAAC,eAAe,CAAC,CAAS,CAAC,CAC5B,CAAC,cACdP,KAAA,CAACpB,IAAI,CAAC2I,IAAI,EAAAvB,QAAA,eACNhG,KAAA,MAAAgG,QAAA,eAAGhG,KAAA,WAAAgG,QAAA,EAASzF,CAAC,CAAC,UAAU,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC,EAAAH,qBAAA,CAAAoB,cAAc,CAACkD,KAAK,UAAAtE,qBAAA,iBAApBA,qBAAA,CAAsB6E,KAAK,GAAI,GAAG,EAAI,CAAC,cAC7EjF,KAAA,MAAAgG,QAAA,eAAGhG,KAAA,WAAAgG,QAAA,EAASzF,CAAC,CAAC,WAAW,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACiB,cAAc,CAAC4D,SAAS,EAAI,GAAG,EAAI,CAAC,cAC3EpF,KAAA,MAAAgG,QAAA,eAAGhG,KAAA,WAAAgG,QAAA,EAASzF,CAAC,CAAC,WAAW,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACiB,cAAc,CAACoH,SAAS,EAAI,GAAG,EAAI,CAAC,cAC3E5I,KAAA,MAAAgG,QAAA,eAAGhG,KAAA,WAAAgG,QAAA,EAASzF,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACsF,cAAc,CAACrE,cAAc,CAAC6D,aAAa,CAAC,EAAI,CAAC,cAC7FrF,KAAA,MAAAgG,QAAA,eAAGhG,KAAA,WAAAgG,QAAA,EAASzF,CAAC,CAAC,mBAAmB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC,CAAAF,sBAAA,CAAAmB,cAAc,CAACkD,KAAK,UAAArE,sBAAA,WAApBA,sBAAA,CAAsBmF,UAAU,CAAG,GAAI,CAAAD,IAAI,CAAC/D,cAAc,CAACkD,KAAK,CAACc,UAAU,CAAC,CAACiE,cAAc,CAAC,CAAC,CAAG,GAAG,EAAI,CAAC,EAChJ,CAAC,EACV,CAAC,CACN,CAAC,cACN3J,IAAA,CAACnB,GAAG,EAAC6I,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACPhG,KAAA,CAACpB,IAAI,EAAC0I,SAAS,CAAC,MAAM,CAAAtB,QAAA,eAClBlG,IAAA,CAAClB,IAAI,CAACkL,MAAM,EAAA9D,QAAA,cACRlG,IAAA,WAAAkG,QAAA,CAASzF,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAC3B,CAAC,cACdP,KAAA,CAACpB,IAAI,CAAC2I,IAAI,EAAAvB,QAAA,eACNhG,KAAA,QAAKsH,SAAS,CAAC,MAAM,CAAAtB,QAAA,eACjBhG,KAAA,WAAAgG,QAAA,EAASzF,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAC,EAAQ,CAAC,cACvCT,IAAA,QAAKwH,SAAS,CAAC,MAAM,CAAAtB,QAAA,CAChBxE,cAAc,CAACqH,YAAY,cACxB/I,IAAA,QACIgJ,GAAG,CAAEtH,cAAc,CAACqH,YAAa,CACjCE,GAAG,CAAC,UAAU,CACdC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbkB,SAAS,CAAE,OAAO,CAClBhB,SAAS,CAAE,SAAS,CACpBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBe,MAAM,CAAE,mBACZ,CAAE,CACF1C,OAAO,CAAEA,CAAA,GAAM4B,MAAM,CAACC,IAAI,CAAC/H,cAAc,CAACqH,YAAY,CAAE,QAAQ,CAAE,CACrE,CAAC,cAEF/I,IAAA,QAAKwH,SAAS,CAAC,6BAA6B,CAAC0B,KAAK,CAAE,CAAEoB,MAAM,CAAE,oBAAoB,CAAEhB,YAAY,CAAE,KAAM,CAAE,CAAApD,QAAA,CACrGzF,CAAC,CAAC,mBAAmB,CAAC,CACtB,CACR,CACA,CAAC,EACL,CAAC,cACNP,KAAA,QAAKsH,SAAS,CAAC,MAAM,CAAAtB,QAAA,eACjBhG,KAAA,WAAAgG,QAAA,EAASzF,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,cACtCT,IAAA,QAAKwH,SAAS,CAAC,MAAM,CAAAtB,QAAA,CAChBxE,cAAc,CAACgI,WAAW,cACvB1J,IAAA,QACIgJ,GAAG,CAAEtH,cAAc,CAACgI,WAAY,CAChCT,GAAG,CAAC,SAAS,CACbC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbkB,SAAS,CAAE,OAAO,CAClBhB,SAAS,CAAE,SAAS,CACpBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBe,MAAM,CAAE,mBACZ,CAAE,CACF1C,OAAO,CAAEA,CAAA,GAAM4B,MAAM,CAACC,IAAI,CAAC/H,cAAc,CAACgI,WAAW,CAAE,QAAQ,CAAE,CACpE,CAAC,cAEF1J,IAAA,QAAKwH,SAAS,CAAC,6BAA6B,CAAC0B,KAAK,CAAE,CAAEoB,MAAM,CAAE,oBAAoB,CAAEhB,YAAY,CAAE,KAAM,CAAE,CAAApD,QAAA,CACrGzF,CAAC,CAAC,mBAAmB,CAAC,CACtB,CACR,CACA,CAAC,EACL,CAAC,EACC,CAAC,EACV,CAAC,CACN,CAAC,EACL,CAAC,EACR,CACL,CACO,CAAC,cACbP,KAAA,CAACb,KAAK,CAACkL,MAAM,EAAArE,QAAA,eACTlG,IAAA,CAACf,MAAM,EAAC0I,OAAO,CAAC,WAAW,CAACC,OAAO,CAAET,aAAc,CAACqD,QAAQ,CAAE5I,UAAW,CAAAsE,QAAA,CACpEzF,CAAC,CAAC,QAAQ,CAAC,CACR,CAAC,cACTT,IAAA,CAACf,MAAM,EACH0I,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAEA,CAAA,GAAMpB,iBAAiB,CAAC,UAAU,CAAE,CAC7CgE,QAAQ,CAAE5I,UAAU,EAAI,CAAAF,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE6D,aAAa,IAAK,UAAW,CAAAW,QAAA,CAEpEtE,UAAU,cACP1B,KAAA,CAAAE,SAAA,EAAA8F,QAAA,eACIlG,IAAA,SAAMwH,SAAS,CAAC,uCAAuC,CAACiD,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAO,CAAC,CAC/FhK,CAAC,CAAC,YAAY,CAAC,EAClB,CAAC,cAEHP,KAAA,CAAAE,SAAA,EAAA8F,QAAA,eACIlG,IAAA,CAACJ,OAAO,EAAC4H,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B/G,CAAC,CAAC,QAAQ,CAAC,EACd,CACL,CACG,CAAC,cACTT,IAAA,CAACf,MAAM,EACH0I,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,CAAA,GAAMpB,iBAAiB,CAAC,UAAU,CAAE,CAC7CgE,QAAQ,CAAE5I,UAAU,EAAI,CAAAF,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE6D,aAAa,IAAK,UAAW,CAAAW,QAAA,CAEpEtE,UAAU,cACP1B,KAAA,CAAAE,SAAA,EAAA8F,QAAA,eACIlG,IAAA,SAAMwH,SAAS,CAAC,uCAAuC,CAACiD,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAO,CAAC,CAC/FhK,CAAC,CAAC,YAAY,CAAC,EAClB,CAAC,cAEHP,KAAA,CAAAE,SAAA,EAAA8F,QAAA,eACIlG,IAAA,CAACL,OAAO,EAAC6H,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B/G,CAAC,CAAC,SAAS,CAAC,EACf,CACL,CACG,CAAC,EACC,CAAC,EACZ,CAAC,cAGRP,KAAA,CAACb,KAAK,EAACyK,IAAI,CAAE5H,oBAAqB,CAAC6H,MAAM,CAAExC,qBAAsB,CAACqC,IAAI,CAAC,IAAI,CAAA1D,QAAA,eACvElG,IAAA,CAACX,KAAK,CAAC2K,MAAM,EAACC,WAAW,MAAA/D,QAAA,cACrBlG,IAAA,CAACX,KAAK,CAAC6K,KAAK,EAAAhE,QAAA,CAAEzF,CAAC,CAAC,cAAc,CAAC,CAAc,CAAC,CACpC,CAAC,cACfP,KAAA,CAACb,KAAK,CAACoI,IAAI,EAAAvB,QAAA,EACNtD,gBAAgB,eACb5C,IAAA,CAACV,KAAK,EAACqI,OAAO,CAAC,QAAQ,CAACH,SAAS,CAAC,MAAM,CAAAtB,QAAA,CACnCtD,gBAAgB,CACd,CACV,CACAE,kBAAkB,eACf9C,IAAA,CAACV,KAAK,EAACqI,OAAO,CAAC,SAAS,CAACH,SAAS,CAAC,MAAM,CAAAtB,QAAA,CACpCpD,kBAAkB,CAChB,CACV,CAEAV,iBAAiB,eACdpC,IAAA,QAAKwH,SAAS,CAAC,MAAM,CAAAtB,QAAA,cACjBhG,KAAA,CAACpB,IAAI,EAAAoH,QAAA,eACDlG,IAAA,CAAClB,IAAI,CAACkL,MAAM,EAAA9D,QAAA,cACRlG,IAAA,WAAAkG,QAAA,CAASzF,CAAC,CAAC,eAAe,CAAC,CAAS,CAAC,CAC5B,CAAC,cACdP,KAAA,CAACpB,IAAI,CAAC2I,IAAI,EAAAvB,QAAA,eACNhG,KAAA,MAAAgG,QAAA,eAAGhG,KAAA,WAAAgG,QAAA,EAASzF,CAAC,CAAC,UAAU,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC,EAAAD,qBAAA,CAAA4B,iBAAiB,CAACwC,KAAK,UAAApE,qBAAA,iBAAvBA,qBAAA,CAAyB2E,KAAK,GAAI,GAAG,EAAI,CAAC,cAChFjF,KAAA,MAAAgG,QAAA,eAAGhG,KAAA,WAAAgG,QAAA,EAASzF,CAAC,CAAC,WAAW,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC2B,iBAAiB,CAACkD,SAAS,EAAI,GAAG,EAAI,CAAC,cAC9EpF,KAAA,MAAAgG,QAAA,eAAGhG,KAAA,WAAAgG,QAAA,EAASzF,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACsF,cAAc,CAAC3D,iBAAiB,CAACmD,aAAa,CAAC,EAAI,CAAC,EACzF,CAAC,EACV,CAAC,CACN,CACR,cAEDrF,KAAA,CAAChB,IAAI,CAAC2I,KAAK,EAACL,SAAS,CAAC,MAAM,CAAAtB,QAAA,eACxBlG,IAAA,CAACd,IAAI,CAAC4I,KAAK,EAAA5B,QAAA,cAAClG,IAAA,WAAAkG,QAAA,CAASzF,CAAC,CAAC,kBAAkB,CAAC,CAAS,CAAC,CAAY,CAAC,cACjEP,KAAA,CAAChB,IAAI,CAACoJ,MAAM,EACRJ,KAAK,CAAE1F,gBAAiB,CACxB2F,QAAQ,CAAGC,CAAC,EAAK3F,mBAAmB,CAAC2F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrDsC,QAAQ,CAAE9H,kBAAmB,CAAAwD,QAAA,eAE7BlG,IAAA,WAAQkI,KAAK,CAAC,EAAE,CAAAhC,QAAA,CAAEzF,CAAC,CAAC,qBAAqB,CAAC,CAAS,CAAC,CACnD6B,eAAe,CAAC2B,GAAG,CAACyG,KAAK,OAAAC,YAAA,oBACtBzK,KAAA,WAA4BgI,KAAK,CAAEwC,KAAK,CAACvG,OAAQ,CAAA+B,QAAA,EAC5CwE,KAAK,CAACE,UAAU,IAAAD,YAAA,CAAID,KAAK,CAAC9F,KAAK,UAAA+F,YAAA,iBAAXA,YAAA,CAAaxF,KAAK,GAAIuF,KAAK,CAACvG,OAAO,CACvDuG,KAAK,CAACG,cAAc,EAAI,KAAKH,KAAK,CAACG,cAAc,IAAI,GAF7CH,KAAK,CAACvG,OAGX,CAAC,EACZ,CAAC,EACO,CAAC,CACb7B,eAAe,CAACsE,MAAM,GAAK,CAAC,eACzB5G,IAAA,CAACd,IAAI,CAAC4L,IAAI,EAACtD,SAAS,CAAC,YAAY,CAAAtB,QAAA,CAC5BzF,CAAC,CAAC,qBAAqB,CAAC,CAClB,CACd,EACO,CAAC,EACL,CAAC,cACbP,KAAA,CAACb,KAAK,CAACkL,MAAM,EAAArE,QAAA,eACTlG,IAAA,CAACf,MAAM,EAAC0I,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEL,qBAAsB,CAACiD,QAAQ,CAAE9H,kBAAmB,CAAAwD,QAAA,CACpFzF,CAAC,CAAC,QAAQ,CAAC,CACR,CAAC,cACTT,IAAA,CAACf,MAAM,EACH0I,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEP,wBAAyB,CAClCmD,QAAQ,CAAE9H,kBAAkB,EAAI,CAACF,gBAAgB,EAAIF,eAAe,CAACsE,MAAM,GAAK,CAAE,CAAAV,QAAA,CAEjFxD,kBAAkB,cACfxC,KAAA,CAAAE,SAAA,EAAA8F,QAAA,eACIlG,IAAA,SAAMwH,SAAS,CAAC,uCAAuC,CAACiD,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAO,CAAC,CAC/FhK,CAAC,CAAC,YAAY,CAAC,EAClB,CAAC,cAEHP,KAAA,CAAAE,SAAA,EAAA8F,QAAA,eACIlG,IAAA,CAACN,aAAa,EAAC8H,SAAS,CAAC,MAAM,CAAE,CAAC,CACjC/G,CAAC,CAAC,gBAAgB,CAAC,EACtB,CACL,CACG,CAAC,EACC,CAAC,EACZ,CAAC,EACD,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}