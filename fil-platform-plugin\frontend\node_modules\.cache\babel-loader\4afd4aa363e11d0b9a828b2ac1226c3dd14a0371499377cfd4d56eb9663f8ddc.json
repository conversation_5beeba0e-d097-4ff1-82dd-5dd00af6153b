{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge,Button,Form,InputGroup,Dropdown,Modal,Alert}from'react-bootstrap';import{FaSearch,FaPlus,FaUserCheck,FaExchangeAlt,FaCheck,FaTimes}from'react-icons/fa';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Members=()=>{var _selectedMember$users,_selectedMember$users2,_changeAgentMember$us;const{t}=useTranslation();const[members,setMembers]=useState([]);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[statusFilter,setStatusFilter]=useState('');const[startDate,setStartDate]=useState('');const[endDate,setEndDate]=useState('');const[filteredMembers,setFilteredMembers]=useState([]);const[showKycModal,setShowKycModal]=useState(false);const[selectedMember,setSelectedMember]=useState(null);const[kycLoading,setKycLoading]=useState(false);const[kycError,setKycError]=useState('');const[kycSuccess,setKycSuccess]=useState('');// Change Agent Modal states\nconst[showChangeAgentModal,setShowChangeAgentModal]=useState(false);const[changeAgentMember,setChangeAgentMember]=useState(null);const[availableAgents,setAvailableAgents]=useState([]);const[selectedNewAgent,setSelectedNewAgent]=useState('');const[changeAgentLoading,setChangeAgentLoading]=useState(false);const[changeAgentError,setChangeAgentError]=useState('');const[changeAgentSuccess,setChangeAgentSuccess]=useState('');// Add Member Modal states\nconst[showAddMemberModal,setShowAddMemberModal]=useState(false);const[newMemberEmail,setNewMemberEmail]=useState('');const[newMemberPassword,setNewMemberPassword]=useState('');const[newMemberInviteCode,setNewMemberInviteCode]=useState('');const[addMemberLoading,setAddMemberLoading]=useState(false);const[addMemberError,setAddMemberError]=useState('');const[addMemberSuccess,setAddMemberSuccess]=useState('');useEffect(()=>{const fetchMembers=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;}// Step 1: 查询 customer_profiles\nconst{data:customers,error:profileError}=await supabase.from('customer_profiles').select('user_id, real_name, id_number, id_img_front, id_img_back, verify_status').eq('agent_id',user.id).order('created_at',{ascending:false});if(profileError||!customers){console.error('Error fetching customer_profiles:',profileError);setLoading(false);return;}// Step 2: 查询 users 表\nconst userIds=customers.map(c=>c.user_id).filter(Boolean);const{data:userInfoList,error:userError}=await supabase.from('users').select('id, email, created_at').in('id',userIds);if(userError){console.error('Error fetching users:',userError);}// Step 3: 合并结果\nconst usersMap=new Map((userInfoList||[]).map(u=>[u.id,u]));const enrichedMembers=customers.map(c=>({...c,users:usersMap.get(c.user_id)||{}}));setMembers(enrichedMembers);setLoading(false);};fetchMembers();},[]);// Filter members based on search criteria\nuseEffect(()=>{let filtered=members;// Search by username (email)\nif(searchTerm){filtered=filtered.filter(member=>{var _member$users,_member$users$email,_member$real_name;return((_member$users=member.users)===null||_member$users===void 0?void 0:(_member$users$email=_member$users.email)===null||_member$users$email===void 0?void 0:_member$users$email.toLowerCase().includes(searchTerm.toLowerCase()))||((_member$real_name=member.real_name)===null||_member$real_name===void 0?void 0:_member$real_name.toLowerCase().includes(searchTerm.toLowerCase()));});}// Filter by status\nif(statusFilter){filtered=filtered.filter(member=>member.verify_status===statusFilter);}// Filter by date range\nif(startDate){filtered=filtered.filter(member=>{var _member$users2;return new Date((_member$users2=member.users)===null||_member$users2===void 0?void 0:_member$users2.created_at)>=new Date(startDate);});}if(endDate){filtered=filtered.filter(member=>{var _member$users3;return new Date((_member$users3=member.users)===null||_member$users3===void 0?void 0:_member$users3.created_at)<=new Date(endDate);});}setFilteredMembers(filtered);},[members,searchTerm,statusFilter,startDate,endDate]);// Fetch available agents for change agent modal\nconst fetchAvailableAgents=async()=>{const supabase=getSupabase();if(!supabase)return;try{// Get current user (should be an agent)\nconst{data:{user}}=await supabase.auth.getUser();if(!user)return;// Query agent_profiles and join with users to get email\nconst{data:agents,error}=await supabase.from('agent_profiles').select(`\n                    user_id,\n                    brand_name,\n                    commission_pct,\n                    users:user_id (\n                        email\n                    )\n                `).neq('user_id',user.id);// Exclude current agent\nif(error){console.error('Error fetching agents:',error);return;}setAvailableAgents(agents||[]);}catch(error){console.error('Error in fetchAvailableAgents:',error);}};const getStatusBadge=status=>{switch(status){case'approved':return/*#__PURE__*/_jsx(Badge,{bg:\"success\",children:t('approved')});case'pending':return/*#__PURE__*/_jsx(Badge,{bg:\"warning\",children:t('pending_review')});case'rejected':return/*#__PURE__*/_jsx(Badge,{bg:\"danger\",children:t('rejected')});case'under_review':return/*#__PURE__*/_jsx(Badge,{bg:\"info\",children:t('under_review')});default:return/*#__PURE__*/_jsx(Badge,{bg:\"secondary\",children:status||t('not_submitted')});}};const handleSearch=()=>{// Search is handled by useEffect, this function can be used for additional logic if needed\nconsole.log('Search triggered');};const handleAddMember=()=>{setShowAddMemberModal(true);setNewMemberEmail('');setNewMemberPassword('');setNewMemberInviteCode('');setAddMemberError('');setAddMemberSuccess('');};const handleConfirmAddMember=async()=>{if(!newMemberEmail||!newMemberPassword||!newMemberInviteCode){setAddMemberError(t('all_fields_required'));return;}// Validate email format\nconst emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;if(!emailRegex.test(newMemberEmail)){setAddMemberError(t('invalid_email_format'));return;}// Validate password length\nif(newMemberPassword.length<6){setAddMemberError(t('password_min_length'));return;}setAddMemberLoading(true);setAddMemberError('');setAddMemberSuccess('');try{const supabase=getSupabase();if(!supabase){throw new Error('Database connection failed');}// Get current user (agent)\nconst{data:{user:currentUser}}=await supabase.auth.getUser();if(!currentUser){throw new Error('Agent not authenticated');}// Step 1: Create auth user using admin API\n// Note: This requires service key, so we need to call a backend endpoint\nconst createUserResponse=await fetch(`${window.wpData.apiUrl}create-member`,{method:'POST',headers:{'Content-Type':'application/json','X-WP-Nonce':window.wpData.nonce},body:JSON.stringify({email:newMemberEmail,password:newMemberPassword,invite_code:newMemberInviteCode,agent_id:currentUser.id})});if(!createUserResponse.ok){const errorData=await createUserResponse.json();throw new Error(errorData.message||'Failed to create member');}const result=await createUserResponse.json();if(!result.success){throw new Error(result.message||'Failed to create member');}setAddMemberSuccess(t('member_created_successfully'));// Close modal and refresh after 2 seconds to show success message\nsetTimeout(()=>{setShowAddMemberModal(false);setNewMemberEmail('');setNewMemberPassword('');setNewMemberInviteCode('');// Refresh the members list\nwindow.location.reload();},2000);}catch(error){console.error('Error creating member:',error);setAddMemberError(error.message||t('member_creation_error'));}finally{setAddMemberLoading(false);}};const closeAddMemberModal=()=>{setShowAddMemberModal(false);setNewMemberEmail('');setNewMemberPassword('');setNewMemberInviteCode('');setAddMemberError('');setAddMemberSuccess('');};const handleKycReview=member=>{setSelectedMember(member);setShowKycModal(true);setKycError('');setKycSuccess('');};const handleKycDecision=async decision=>{if(!selectedMember)return;setKycLoading(true);setKycError('');setKycSuccess('');try{const supabase=getSupabase();if(!supabase){throw new Error('Database connection failed');}const{data,error}=await supabase.from('customer_profiles').update({verify_status:decision}).eq('user_id',selectedMember.user_id).select();if(error){console.error('Database error:',error);throw error;}// Check if any rows were updated\nif(!data||data.length===0){// Try to find the record\nconst{data:existingRecord,error:selectError}=await supabase.from('customer_profiles').select('*').eq('user_id',selectedMember.user_id);if(selectError){console.error('Error checking existing record:',selectError);throw selectError;}if(!existingRecord||existingRecord.length===0){throw new Error('Customer profile not found');}}// Update local state\nsetMembers(prevMembers=>prevMembers.map(member=>member.user_id===selectedMember.user_id?{...member,verify_status:decision}:member));// Also update the selected member\nsetSelectedMember(prev=>({...prev,verify_status:decision}));setKycSuccess(decision==='approved'?t('kyc_approved_success'):t('kyc_rejected_success'));// Close modal after 1.5 seconds\nsetTimeout(()=>{setShowKycModal(false);setSelectedMember(null);},1500);}catch(error){console.error('Error updating KYC status:',error);setKycError(error.message||t('kyc_update_error'));}finally{setKycLoading(false);}};const closeKycModal=()=>{setShowKycModal(false);setSelectedMember(null);setKycError('');setKycSuccess('');};const handleChangeAgent=async member=>{setChangeAgentMember(member);setChangeAgentError('');setChangeAgentSuccess('');setSelectedNewAgent('');// Fetch available agents\nawait fetchAvailableAgents();setShowChangeAgentModal(true);};const handleConfirmChangeAgent=async()=>{if(!changeAgentMember||!selectedNewAgent){setChangeAgentError(t('please_select_agent'));return;}setChangeAgentLoading(true);setChangeAgentError('');setChangeAgentSuccess('');try{const supabase=getSupabase();if(!supabase){throw new Error('Database connection failed');}// Update customer's agent_id\nconst{data,error}=await supabase.from('customer_profiles').update({agent_id:selectedNewAgent}).eq('user_id',changeAgentMember.user_id).select();if(error){console.error('Database error:',error);throw error;}if(!data||data.length===0){throw new Error('Failed to update agent assignment');}// Remove the member from current list since they're no longer assigned to current agent\nsetMembers(prevMembers=>prevMembers.filter(member=>member.user_id!==changeAgentMember.user_id));setChangeAgentSuccess(t('agent_changed_successfully'));// Close modal after 1.5 seconds\nsetTimeout(()=>{setShowChangeAgentModal(false);setChangeAgentMember(null);setSelectedNewAgent('');},1500);}catch(error){console.error('Error changing agent:',error);setChangeAgentError(error.message||t('agent_change_error'));}finally{setChangeAgentLoading(false);}};const closeChangeAgentModal=()=>{setShowChangeAgentModal(false);setChangeAgentMember(null);setSelectedNewAgent('');setChangeAgentError('');setChangeAgentSuccess('');};if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_members')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('member_list')}),/*#__PURE__*/_jsx(Row,{className:\"mb-4\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{className:\"align-items-end\",children:[/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Button,{variant:\"primary\",onClick:handleAddMember,className:\"mb-2\",children:[/*#__PURE__*/_jsx(FaPlus,{className:\"me-1\"}),t('add_member')]})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('search_username')}),/*#__PURE__*/_jsx(InputGroup,{children:/*#__PURE__*/_jsx(Form.Control,{type:\"text\",placeholder:t('please_enter_username'),value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})})]})}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('status_filter')}),/*#__PURE__*/_jsxs(Form.Select,{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:t('please_select_status')}),/*#__PURE__*/_jsx(\"option\",{value:\"pending\",children:t('pending_review')}),/*#__PURE__*/_jsx(\"option\",{value:\"approved\",children:t('approved')}),/*#__PURE__*/_jsx(\"option\",{value:\"rejected\",children:t('rejected')}),/*#__PURE__*/_jsx(\"option\",{value:\"under_review\",children:t('under_review')})]})]})}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('start_date')}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:startDate,onChange:e=>setStartDate(e.target.value)})]})}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('end_date')}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:endDate,onChange:e=>setEndDate(e.target.value)})]})}),/*#__PURE__*/_jsx(Col,{md:1,children:/*#__PURE__*/_jsx(Button,{variant:\"outline-primary\",onClick:handleSearch,className:\"mb-2\",children:/*#__PURE__*/_jsx(FaSearch,{})})})]})})})})}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('username')}),/*#__PURE__*/_jsx(\"th\",{children:t('real_name')}),/*#__PURE__*/_jsx(\"th\",{children:t('id_number')}),/*#__PURE__*/_jsx(\"th\",{children:t('id_front_image')}),/*#__PURE__*/_jsx(\"th\",{children:t('id_back_image')}),/*#__PURE__*/_jsx(\"th\",{children:t('status')}),/*#__PURE__*/_jsx(\"th\",{children:t('registration_time')}),/*#__PURE__*/_jsx(\"th\",{children:t('actions')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:filteredMembers.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"9\",className:\"text-center\",children:t('no_members_found')})}):filteredMembers.map(member=>{var _member$users4,_member$users5;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:((_member$users4=member.users)===null||_member$users4===void 0?void 0:_member$users4.email)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:member.real_name||'-'}),/*#__PURE__*/_jsx(\"td\",{children:member.id_number||'-'}),/*#__PURE__*/_jsx(\"td\",{children:member.id_img_front?/*#__PURE__*/_jsx(\"img\",{src:member.id_img_front,alt:\"ID Front\",style:{width:'60px',height:'40px',objectFit:'cover',borderRadius:'4px',cursor:'pointer'},onClick:()=>window.open(member.id_img_front,'_blank')}):/*#__PURE__*/_jsx(\"span\",{className:\"text-muted\",children:\"-\"})}),/*#__PURE__*/_jsx(\"td\",{children:member.id_img_back?/*#__PURE__*/_jsx(\"img\",{src:member.id_img_back,alt:\"ID Back\",style:{width:'60px',height:'40px',objectFit:'cover',borderRadius:'4px',cursor:'pointer'},onClick:()=>window.open(member.id_img_back,'_blank')}):/*#__PURE__*/_jsx(\"span\",{className:\"text-muted\",children:\"-\"})}),/*#__PURE__*/_jsx(\"td\",{children:getStatusBadge(member.verify_status)}),/*#__PURE__*/_jsx(\"td\",{children:(_member$users5=member.users)!==null&&_member$users5!==void 0&&_member$users5.created_at?new Date(member.users.created_at).toLocaleString():'-'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between\",children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-primary\",onClick:()=>handleKycReview(member),title:t('kyc_review'),children:/*#__PURE__*/_jsx(FaUserCheck,{})}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-warning\",onClick:()=>handleChangeAgent(member),title:t('change_agent'),children:/*#__PURE__*/_jsx(FaExchangeAlt,{})})]})})]},member.user_id);})})]})})})})}),/*#__PURE__*/_jsxs(Modal,{show:showKycModal,onHide:closeKycModal,size:\"lg\",children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,className:\"custom-modal-header\",children:/*#__PURE__*/_jsx(Modal.Title,{children:t('kyc_review')})}),/*#__PURE__*/_jsx(\"style\",{dangerouslySetInnerHTML:{__html:`\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    `}}),/*#__PURE__*/_jsx(Modal.Body,{children:selectedMember&&/*#__PURE__*/_jsxs(_Fragment,{children:[kycError&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"mb-3\",children:kycError}),kycSuccess&&/*#__PURE__*/_jsx(Alert,{variant:\"success\",className:\"mb-3\",children:kycSuccess}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('customer_info')})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('username'),\":\"]}),\" \",((_selectedMember$users=selectedMember.users)===null||_selectedMember$users===void 0?void 0:_selectedMember$users.email)||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('real_name'),\":\"]}),\" \",selectedMember.real_name||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('id_number'),\":\"]}),\" \",selectedMember.id_number||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('current_status'),\":\"]}),\" \",getStatusBadge(selectedMember.verify_status)]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('registration_time'),\":\"]}),\" \",(_selectedMember$users2=selectedMember.users)!==null&&_selectedMember$users2!==void 0&&_selectedMember$users2.created_at?new Date(selectedMember.users.created_at).toLocaleString():'-']})]})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('id_documents')})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('id_front_image'),\":\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:selectedMember.id_img_front?/*#__PURE__*/_jsx(\"img\",{src:selectedMember.id_img_front,alt:\"ID Front\",style:{width:'100%',maxHeight:'150px',objectFit:'contain',borderRadius:'4px',cursor:'pointer',border:'1px solid #dee2e6'},onClick:()=>window.open(selectedMember.id_img_front,'_blank')}):/*#__PURE__*/_jsx(\"div\",{className:\"text-muted text-center py-3\",style:{border:'1px dashed #dee2e6',borderRadius:'4px'},children:t('no_image_uploaded')})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('id_back_image'),\":\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:selectedMember.id_img_back?/*#__PURE__*/_jsx(\"img\",{src:selectedMember.id_img_back,alt:\"ID Back\",style:{width:'100%',maxHeight:'150px',objectFit:'contain',borderRadius:'4px',cursor:'pointer',border:'1px solid #dee2e6'},onClick:()=>window.open(selectedMember.id_img_back,'_blank')}):/*#__PURE__*/_jsx(\"div\",{className:\"text-muted text-center py-3\",style:{border:'1px dashed #dee2e6',borderRadius:'4px'},children:t('no_image_uploaded')})})]})]})]})})]})]})}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:closeKycModal,disabled:kycLoading,children:t('cancel')}),/*#__PURE__*/_jsx(Button,{variant:\"danger\",onClick:()=>handleKycDecision('rejected'),disabled:kycLoading||(selectedMember===null||selectedMember===void 0?void 0:selectedMember.verify_status)==='rejected',children:kycLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\",\"aria-hidden\":\"true\"}),t('processing')]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaTimes,{className:\"me-1\"}),t('reject')]})}),/*#__PURE__*/_jsx(Button,{variant:\"success\",onClick:()=>handleKycDecision('approved'),disabled:kycLoading||(selectedMember===null||selectedMember===void 0?void 0:selectedMember.verify_status)==='approved',children:kycLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\",\"aria-hidden\":\"true\"}),t('processing')]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaCheck,{className:\"me-1\"}),t('approve')]})})]})]}),/*#__PURE__*/_jsxs(Modal,{show:showChangeAgentModal,onHide:closeChangeAgentModal,size:\"md\",children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsx(Modal.Title,{children:t('change_agent')})}),/*#__PURE__*/_jsx(\"style\",{dangerouslySetInnerHTML:{__html:`\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    `}}),/*#__PURE__*/_jsxs(Modal.Body,{children:[changeAgentError&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"mb-3\",children:changeAgentError}),changeAgentSuccess&&/*#__PURE__*/_jsx(Alert,{variant:\"success\",className:\"mb-3\",children:changeAgentSuccess}),changeAgentMember&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('customer_info')})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('username'),\":\"]}),\" \",((_changeAgentMember$us=changeAgentMember.users)===null||_changeAgentMember$us===void 0?void 0:_changeAgentMember$us.email)||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('real_name'),\":\"]}),\" \",changeAgentMember.real_name||'-']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('current_status'),\":\"]}),\" \",getStatusBadge(changeAgentMember.verify_status)]})]})]})}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('select_new_agent')})}),/*#__PURE__*/_jsxs(Form.Select,{value:selectedNewAgent,onChange:e=>setSelectedNewAgent(e.target.value),disabled:changeAgentLoading,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:t('please_select_agent')}),availableAgents.map(agent=>{var _agent$users;return/*#__PURE__*/_jsxs(\"option\",{value:agent.user_id,children:[agent.brand_name||((_agent$users=agent.users)===null||_agent$users===void 0?void 0:_agent$users.email)||agent.user_id,agent.commission_pct&&` (${agent.commission_pct}%)`]},agent.user_id);})]}),availableAgents.length===0&&/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:t('no_available_agents')})]})]}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:closeChangeAgentModal,disabled:changeAgentLoading,children:t('cancel')}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:handleConfirmChangeAgent,disabled:changeAgentLoading||!selectedNewAgent||availableAgents.length===0,children:changeAgentLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\",\"aria-hidden\":\"true\"}),t('processing')]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaExchangeAlt,{className:\"me-1\"}),t('confirm_change')]})})]})]}),/*#__PURE__*/_jsxs(Modal,{show:showAddMemberModal,onHide:closeAddMemberModal,size:\"md\",children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsx(Modal.Title,{children:t('add_member')})}),/*#__PURE__*/_jsxs(Modal.Body,{children:[addMemberError&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"mb-3\",children:addMemberError}),addMemberSuccess&&/*#__PURE__*/_jsx(Alert,{variant:\"success\",className:\"mb-3\",children:addMemberSuccess}),/*#__PURE__*/_jsxs(Form,{children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('email_address')})}),/*#__PURE__*/_jsx(Form.Control,{type:\"email\",value:newMemberEmail,onChange:e=>setNewMemberEmail(e.target.value),placeholder:t('enter_email_address'),disabled:addMemberLoading,required:true}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:t('member_email_help')})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('password')})}),/*#__PURE__*/_jsx(Form.Control,{type:\"password\",value:newMemberPassword,onChange:e=>setNewMemberPassword(e.target.value),placeholder:t('enter_password'),disabled:addMemberLoading,minLength:6,required:true}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:t('password_min_6_chars')})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:t('invite_code')})}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",value:newMemberInviteCode,onChange:e=>setNewMemberInviteCode(e.target.value),placeholder:t('enter_invite_code'),disabled:addMemberLoading,required:true}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:t('invite_code_help')})]})]})]}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:closeAddMemberModal,disabled:addMemberLoading,children:t('cancel')}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:handleConfirmAddMember,disabled:addMemberLoading||!newMemberEmail||!newMemberPassword||!newMemberInviteCode,children:addMemberLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\",\"aria-hidden\":\"true\"}),t('creating')]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaPlus,{className:\"me-1\"}),t('create_member')]})})]})]})]});};export default Members;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "<PERSON><PERSON>", "Form", "InputGroup", "Dropdown", "Modal", "<PERSON><PERSON>", "FaSearch", "FaPlus", "FaUserCheck", "FaExchangeAlt", "FaCheck", "FaTimes", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Members", "_selectedMember$users", "_selectedMember$users2", "_changeAgentMember$us", "t", "members", "setMembers", "loading", "setLoading", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "startDate", "setStartDate", "endDate", "setEndDate", "filteredMembers", "setFilteredMembers", "showKycModal", "setShowKycModal", "selected<PERSON><PERSON>ber", "setSelectedMember", "kycLoading", "setKycLoading", "kycError", "setKycError", "kycSuccess", "setKycSuccess", "showChangeAgentModal", "setShowChangeAgentModal", "changeAgentMember", "setChangeAgentMember", "availableAgents", "setAvailableAgents", "selectedNewAgent", "setSelectedNewAgent", "changeAgentLoading", "setChangeAgentLoading", "changeAgentError", "setChangeAgentError", "changeAgentSuccess", "setChangeAgentSuccess", "showAddMemberModal", "setShowAddMemberModal", "newMemberEmail", "setNewMemberEmail", "newMemberPassword", "setNewMemberPassword", "newMemberInviteCode", "setNewMemberInviteCode", "addMemberLoading", "setAddMemberLoading", "addMemberError", "setAddMemberError", "addMemberSuccess", "setAddMemberSuccess", "fetchMembers", "supabase", "data", "user", "auth", "getUser", "customers", "error", "profileError", "from", "select", "eq", "id", "order", "ascending", "console", "userIds", "map", "c", "user_id", "filter", "Boolean", "userInfoList", "userError", "in", "usersMap", "Map", "u", "enrichedMembers", "users", "get", "filtered", "member", "_member$users", "_member$users$email", "_member$real_name", "email", "toLowerCase", "includes", "real_name", "verify_status", "_member$users2", "Date", "created_at", "_member$users3", "fetchAvailableAgents", "agents", "neq", "getStatusBadge", "status", "bg", "children", "handleSearch", "log", "handleAddMember", "handleConfirmAddMember", "emailRegex", "test", "length", "Error", "currentUser", "createUserResponse", "fetch", "window", "wpData", "apiUrl", "method", "headers", "nonce", "body", "JSON", "stringify", "password", "invite_code", "agent_id", "ok", "errorData", "json", "message", "result", "success", "setTimeout", "location", "reload", "closeAddMemberModal", "handleKycReview", "handleKycDecision", "decision", "update", "existingRecord", "selectError", "prevMembers", "prev", "closeKycModal", "handleChangeAgent", "handleConfirmChangeAgent", "closeChangeAgentModal", "className", "Body", "md", "variant", "onClick", "Group", "Label", "Control", "type", "placeholder", "value", "onChange", "e", "target", "Select", "striped", "bordered", "hover", "responsive", "colSpan", "_member$users4", "_member$users5", "id_number", "id_img_front", "src", "alt", "style", "width", "height", "objectFit", "borderRadius", "cursor", "open", "id_img_back", "toLocaleString", "size", "title", "show", "onHide", "Header", "closeButton", "Title", "dangerouslySetInnerHTML", "__html", "maxHeight", "border", "Footer", "disabled", "role", "agent", "_agent$users", "brand_name", "commission_pct", "Text", "required", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/Members.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge, Button, Form, InputGroup, Dropdown, Modal, Alert } from 'react-bootstrap';\nimport { FaSearch, FaPlus, FaUserCheck, FaExchangeAlt, FaCheck, FaTimes } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Members = () => {\n    const { t } = useTranslation();\n    const [members, setMembers] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [statusFilter, setStatusFilter] = useState('');\n    const [startDate, setStartDate] = useState('');\n    const [endDate, setEndDate] = useState('');\n    const [filteredMembers, setFilteredMembers] = useState([]);\n    const [showKycModal, setShowKycModal] = useState(false);\n    const [selectedMember, setSelectedMember] = useState(null);\n    const [kycLoading, setKycLoading] = useState(false);\n    const [kycError, setKycError] = useState('');\n    const [kycSuccess, setKycSuccess] = useState('');\n\n    // Change Agent Modal states\n    const [showChangeAgentModal, setShowChangeAgentModal] = useState(false);\n    const [changeAgentMember, setChangeAgentMember] = useState(null);\n    const [availableAgents, setAvailableAgents] = useState([]);\n    const [selectedNewAgent, setSelectedNewAgent] = useState('');\n    const [changeAgentLoading, setChangeAgentLoading] = useState(false);\n    const [changeAgentError, setChangeAgentError] = useState('');\n    const [changeAgentSuccess, setChangeAgentSuccess] = useState('');\n\n    // Add Member Modal states\n    const [showAddMemberModal, setShowAddMemberModal] = useState(false);\n    const [newMemberEmail, setNewMemberEmail] = useState('');\n    const [newMemberPassword, setNewMemberPassword] = useState('');\n    const [newMemberInviteCode, setNewMemberInviteCode] = useState('');\n    const [addMemberLoading, setAddMemberLoading] = useState(false);\n    const [addMemberError, setAddMemberError] = useState('');\n    const [addMemberSuccess, setAddMemberSuccess] = useState('');\n\n    useEffect(() => {\n            const fetchMembers = async () => {\n                const supabase = getSupabase();\n                if (!supabase) return;\n    \n                setLoading(true);\n                const { data: { user } } = await supabase.auth.getUser();\n    \n                if (!user) {\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 1: 查询 customer_profiles\n                const { data: customers, error: profileError } = await supabase\n                    .from('customer_profiles')\n                    .select('user_id, real_name, id_number, id_img_front, id_img_back, verify_status')\n                    .eq('agent_id', user.id)\n                    .order('created_at', { ascending: false });\n    \n                if (profileError || !customers) {\n                    console.error('Error fetching customer_profiles:', profileError);\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 2: 查询 users 表\n                const userIds = customers.map(c => c.user_id).filter(Boolean);\n    \n                const { data: userInfoList, error: userError } = await supabase\n                    .from('users')\n                    .select('id, email, created_at')\n                    .in('id', userIds);\n\n                if (userError) {\n                    console.error('Error fetching users:', userError);\n                }\n    \n                // Step 3: 合并结果\n                const usersMap = new Map((userInfoList || []).map(u => [u.id, u]));\n    \n                const enrichedMembers = customers.map(c => ({\n                    ...c,\n                    users: usersMap.get(c.user_id) || {}\n                }));\n    \n                setMembers(enrichedMembers);\n                setLoading(false);\n            };\n\n        fetchMembers();\n    }, []);\n\n    // Filter members based on search criteria\n    useEffect(() => {\n        let filtered = members;\n\n        // Search by username (email)\n        if (searchTerm) {\n            filtered = filtered.filter(member =>\n                member.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                member.real_name?.toLowerCase().includes(searchTerm.toLowerCase())\n            );\n        }\n\n        // Filter by status\n        if (statusFilter) {\n            filtered = filtered.filter(member => member.verify_status === statusFilter);\n        }\n\n        // Filter by date range\n        if (startDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) >= new Date(startDate)\n            );\n        }\n        if (endDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) <= new Date(endDate)\n            );\n        }\n\n        setFilteredMembers(filtered);\n    }, [members, searchTerm, statusFilter, startDate, endDate]);\n\n    // Fetch available agents for change agent modal\n    const fetchAvailableAgents = async () => {\n        const supabase = getSupabase();\n        if (!supabase) return;\n\n        try {\n            // Get current user (should be an agent)\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) return;\n\n            // Query agent_profiles and join with users to get email\n            const { data: agents, error } = await supabase\n                .from('agent_profiles')\n                .select(`\n                    user_id,\n                    brand_name,\n                    commission_pct,\n                    users:user_id (\n                        email\n                    )\n                `)\n                .neq('user_id', user.id); // Exclude current agent\n\n            if (error) {\n                console.error('Error fetching agents:', error);\n                return;\n            }\n\n            setAvailableAgents(agents || []);\n        } catch (error) {\n            console.error('Error in fetchAvailableAgents:', error);\n        }\n    };\n\n    const getStatusBadge = (status) => {\n        switch (status) {\n            case 'approved':\n                return <Badge bg=\"success\">{t('approved')}</Badge>;\n            case 'pending':\n                return <Badge bg=\"warning\">{t('pending_review')}</Badge>;\n            case 'rejected':\n                return <Badge bg=\"danger\">{t('rejected')}</Badge>;\n            case 'under_review':\n                return <Badge bg=\"info\">{t('under_review')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{status || t('not_submitted')}</Badge>;\n        }\n    };\n\n    const handleSearch = () => {\n        // Search is handled by useEffect, this function can be used for additional logic if needed\n        console.log('Search triggered');\n    };\n\n    const handleAddMember = () => {\n        setShowAddMemberModal(true);\n        setNewMemberEmail('');\n        setNewMemberPassword('');\n        setNewMemberInviteCode('');\n        setAddMemberError('');\n        setAddMemberSuccess('');\n    };\n\n    const handleConfirmAddMember = async () => {\n        if (!newMemberEmail || !newMemberPassword || !newMemberInviteCode) {\n            setAddMemberError(t('all_fields_required'));\n            return;\n        }\n\n        // Validate email format\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(newMemberEmail)) {\n            setAddMemberError(t('invalid_email_format'));\n            return;\n        }\n\n        // Validate password length\n        if (newMemberPassword.length < 6) {\n            setAddMemberError(t('password_min_length'));\n            return;\n        }\n\n        setAddMemberLoading(true);\n        setAddMemberError('');\n        setAddMemberSuccess('');\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error('Database connection failed');\n            }\n\n            // Get current user (agent)\n            const { data: { user: currentUser } } = await supabase.auth.getUser();\n            if (!currentUser) {\n                throw new Error('Agent not authenticated');\n            }\n\n            // Step 1: Create auth user using admin API\n            // Note: This requires service key, so we need to call a backend endpoint\n            const createUserResponse = await fetch(`${window.wpData.apiUrl}create-member`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'X-WP-Nonce': window.wpData.nonce\n                },\n                body: JSON.stringify({\n                    email: newMemberEmail,\n                    password: newMemberPassword,\n                    invite_code: newMemberInviteCode,\n                    agent_id: currentUser.id\n                })\n            });\n\n            if (!createUserResponse.ok) {\n                const errorData = await createUserResponse.json();\n                throw new Error(errorData.message || 'Failed to create member');\n            }\n\n            const result = await createUserResponse.json();\n\n            if (!result.success) {\n                throw new Error(result.message || 'Failed to create member');\n            }\n\n            setAddMemberSuccess(t('member_created_successfully'));\n\n            // Close modal and refresh after 2 seconds to show success message\n            setTimeout(() => {\n                setShowAddMemberModal(false);\n                setNewMemberEmail('');\n                setNewMemberPassword('');\n                setNewMemberInviteCode('');\n                // Refresh the members list\n                window.location.reload();\n            }, 2000);\n\n        } catch (error) {\n            console.error('Error creating member:', error);\n            setAddMemberError(error.message || t('member_creation_error'));\n        } finally {\n            setAddMemberLoading(false);\n        }\n    };\n\n    const closeAddMemberModal = () => {\n        setShowAddMemberModal(false);\n        setNewMemberEmail('');\n        setNewMemberPassword('');\n        setNewMemberInviteCode('');\n        setAddMemberError('');\n        setAddMemberSuccess('');\n    };\n\n    const handleKycReview = (member) => {\n        setSelectedMember(member);\n        setShowKycModal(true);\n        setKycError('');\n        setKycSuccess('');\n    };\n\n    const handleKycDecision = async (decision) => {\n        if (!selectedMember) return;\n\n        setKycLoading(true);\n        setKycError('');\n        setKycSuccess('');\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error('Database connection failed');\n            }\n\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .update({ verify_status: decision })\n                .eq('user_id', selectedMember.user_id)\n                .select();\n\n            if (error) {\n                console.error('Database error:', error);\n                throw error;\n            }\n\n            // Check if any rows were updated\n            if (!data || data.length === 0) {\n                \n                // Try to find the record\n                const { data: existingRecord, error: selectError } = await supabase\n                    .from('customer_profiles')\n                    .select('*')\n                    .eq('user_id', selectedMember.user_id);\n\n                if (selectError) {\n                    console.error('Error checking existing record:', selectError);\n                    throw selectError;\n                }\n                \n                if (!existingRecord || existingRecord.length === 0) {\n                    throw new Error('Customer profile not found');\n                }\n            }\n\n            // Update local state\n            setMembers(prevMembers => \n                prevMembers.map(member => \n                    member.user_id === selectedMember.user_id \n                        ? { ...member, verify_status: decision }\n                        : member\n                )\n            );\n\n            // Also update the selected member\n            setSelectedMember(prev => ({ ...prev, verify_status: decision }));\n\n            setKycSuccess(decision === 'approved' ? t('kyc_approved_success') : t('kyc_rejected_success'));\n            \n            // Close modal after 1.5 seconds\n            setTimeout(() => {\n                setShowKycModal(false);\n                setSelectedMember(null);\n            }, 1500);\n\n        } catch (error) {\n            console.error('Error updating KYC status:', error);\n            setKycError(error.message || t('kyc_update_error'));\n        } finally {\n            setKycLoading(false);\n        }\n    };\n\n    const closeKycModal = () => {\n        setShowKycModal(false);\n        setSelectedMember(null);\n        setKycError('');\n        setKycSuccess('');\n    };\n\n    const handleChangeAgent = async (member) => {\n        setChangeAgentMember(member);\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n        setSelectedNewAgent('');\n        \n        // Fetch available agents\n        await fetchAvailableAgents();\n        \n        setShowChangeAgentModal(true);\n    };\n\n    const handleConfirmChangeAgent = async () => {\n        if (!changeAgentMember || !selectedNewAgent) {\n            setChangeAgentError(t('please_select_agent'));\n            return;\n        }\n\n        setChangeAgentLoading(true);\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error('Database connection failed');\n            }\n\n            // Update customer's agent_id\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .update({ agent_id: selectedNewAgent })\n                .eq('user_id', changeAgentMember.user_id)\n                .select();\n\n            if (error) {\n                console.error('Database error:', error);\n                throw error;\n            }\n\n            if (!data || data.length === 0) {\n                throw new Error('Failed to update agent assignment');\n            }\n\n            // Remove the member from current list since they're no longer assigned to current agent\n            setMembers(prevMembers => \n                prevMembers.filter(member => member.user_id !== changeAgentMember.user_id)\n            );\n\n            setChangeAgentSuccess(t('agent_changed_successfully'));\n            \n            // Close modal after 1.5 seconds\n            setTimeout(() => {\n                setShowChangeAgentModal(false);\n                setChangeAgentMember(null);\n                setSelectedNewAgent('');\n            }, 1500);\n\n        } catch (error) {\n            console.error('Error changing agent:', error);\n            setChangeAgentError(error.message || t('agent_change_error'));\n        } finally {\n            setChangeAgentLoading(false);\n        }\n    };\n\n    const closeChangeAgentModal = () => {\n        setShowChangeAgentModal(false);\n        setChangeAgentMember(null);\n        setSelectedNewAgent('');\n        setChangeAgentError('');\n        setChangeAgentSuccess('');\n    };\n\n\n    if (loading) {\n        return <div>{t('loading_members')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('member_list')}</h2>\n            \n            {/* Top Operation Bar */}\n            <Row className=\"mb-4\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={2}>\n                                    <Button \n                                        variant=\"primary\" \n                                        onClick={handleAddMember}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaPlus className=\"me-1\" />\n                                        {t('add_member')}\n                                    </Button>\n                                </Col>\n                                <Col md={3}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_username')}</Form.Label>\n                                        <InputGroup>\n                                            <Form.Control\n                                                type=\"text\"\n                                                placeholder={t('please_enter_username')}\n                                                value={searchTerm}\n                                                onChange={(e) => setSearchTerm(e.target.value)}\n                                            />\n                                        </InputGroup>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('status_filter')}</Form.Label>\n                                        <Form.Select\n                                            value={statusFilter}\n                                            onChange={(e) => setStatusFilter(e.target.value)}\n                                        >\n                                            <option value=\"\">{t('please_select_status')}</option>\n                                            <option value=\"pending\">{t('pending_review')}</option>\n                                            <option value=\"approved\">{t('approved')}</option>\n                                            <option value=\"rejected\">{t('rejected')}</option>\n                                            <option value=\"under_review\">{t('under_review')}</option>\n                                        </Form.Select>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('start_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={startDate}\n                                            onChange={(e) => setStartDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('end_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={endDate}\n                                            onChange={(e) => setEndDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={1}>\n                                    <Button \n                                        variant=\"outline-primary\" \n                                        onClick={handleSearch}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaSearch />\n                                    </Button>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* Members Table */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('username')}</th>\n                                        <th>{t('real_name')}</th>\n                                        <th>{t('id_number')}</th>\n                                        <th>{t('id_front_image')}</th>\n                                        <th>{t('id_back_image')}</th>\n                                        <th>{t('status')}</th>\n                                        <th>{t('registration_time')}</th>\n                                        <th>{t('actions')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {filteredMembers.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"9\" className=\"text-center\">{t('no_members_found')}</td>\n                                        </tr>\n                                    ) : (\n                                        filteredMembers.map(member => (\n                                            <tr key={member.user_id}>\n                                                <td>{member.users?.email || '-'}</td>\n                                                <td>{member.real_name || '-'}</td>\n                                                <td>{member.id_number || '-'}</td>\n                                                <td>\n                                                    {member.id_img_front ? (\n                                                        <img \n                                                            src={member.id_img_front} \n                                                            alt=\"ID Front\" \n                                                            style={{ \n                                                                width: '60px', \n                                                                height: '40px', \n                                                                objectFit: 'cover', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer'\n                                                            }}\n                                                            onClick={() => window.open(member.id_img_front, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <span className=\"text-muted\">-</span>\n                                                    )}\n                                                </td>\n                                                <td>\n                                                    {member.id_img_back ? (\n                                                        <img \n                                                            src={member.id_img_back} \n                                                            alt=\"ID Back\" \n                                                            style={{ \n                                                                width: '60px', \n                                                                height: '40px', \n                                                                objectFit: 'cover', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer'\n                                                            }}\n                                                            onClick={() => window.open(member.id_img_back, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <span className=\"text-muted\">-</span>\n                                                    )}\n                                                </td>\n                                                <td>{getStatusBadge(member.verify_status)}</td>\n                                                <td>{member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'}</td>\n                                                <td>\n                                                    <div className=\"d-flex justify-content-between\">\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-primary\"\n                                                            onClick={() => handleKycReview(member)}\n                                                            title={t('kyc_review')}\n                                                        >\n                                                            <FaUserCheck />\n                                                        </Button>\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-warning\"\n                                                            onClick={() => handleChangeAgent(member)}\n                                                            title={t('change_agent')}\n                                                        >\n                                                            <FaExchangeAlt />\n                                                        </Button>\n                                                    </div>\n                                                </td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* KYC Review Modal */}\n            <Modal show={showKycModal} onHide={closeKycModal} size=\"lg\">\n                <Modal.Header closeButton className=\"custom-modal-header\">\n                    <Modal.Title>{t('kyc_review')}</Modal.Title>\n                </Modal.Header>\n                <style dangerouslySetInnerHTML={{\n                    __html: `\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    `\n                }} />\n                <Modal.Body>\n                    {selectedMember && (\n                        <>\n                            {kycError && (\n                                <Alert variant=\"danger\" className=\"mb-3\">\n                                    {kycError}\n                                </Alert>\n                            )}\n                            {kycSuccess && (\n                                <Alert variant=\"success\" className=\"mb-3\">\n                                    {kycSuccess}\n                                </Alert>\n                            )}\n                            \n                            <Row>\n                                <Col md={6}>\n                                    <Card className=\"mb-3\">\n                                        <Card.Header>\n                                            <strong>{t('customer_info')}</strong>\n                                        </Card.Header>\n                                        <Card.Body>\n                                            <p><strong>{t('username')}:</strong> {selectedMember.users?.email || '-'}</p>\n                                            <p><strong>{t('real_name')}:</strong> {selectedMember.real_name || '-'}</p>\n                                            <p><strong>{t('id_number')}:</strong> {selectedMember.id_number || '-'}</p>\n                                            <p><strong>{t('current_status')}:</strong> {getStatusBadge(selectedMember.verify_status)}</p>\n                                            <p><strong>{t('registration_time')}:</strong> {selectedMember.users?.created_at ? new Date(selectedMember.users.created_at).toLocaleString() : '-'}</p>\n                                        </Card.Body>\n                                    </Card>\n                                </Col>\n                                <Col md={6}>\n                                    <Card className=\"mb-3\">\n                                        <Card.Header>\n                                            <strong>{t('id_documents')}</strong>\n                                        </Card.Header>\n                                        <Card.Body>\n                                            <div className=\"mb-3\">\n                                                <strong>{t('id_front_image')}:</strong>\n                                                <div className=\"mt-2\">\n                                                    {selectedMember.id_img_front ? (\n                                                        <img \n                                                            src={selectedMember.id_img_front} \n                                                            alt=\"ID Front\" \n                                                            style={{ \n                                                                width: '100%', \n                                                                maxHeight: '150px', \n                                                                objectFit: 'contain', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer',\n                                                                border: '1px solid #dee2e6'\n                                                            }}\n                                                            onClick={() => window.open(selectedMember.id_img_front, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <div className=\"text-muted text-center py-3\" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>\n                                                            {t('no_image_uploaded')}\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                            <div className=\"mb-3\">\n                                                <strong>{t('id_back_image')}:</strong>\n                                                <div className=\"mt-2\">\n                                                    {selectedMember.id_img_back ? (\n                                                        <img \n                                                            src={selectedMember.id_img_back} \n                                                            alt=\"ID Back\" \n                                                            style={{ \n                                                                width: '100%', \n                                                                maxHeight: '150px', \n                                                                objectFit: 'contain', \n                                                                borderRadius: '4px',\n                                                                cursor: 'pointer',\n                                                                border: '1px solid #dee2e6'\n                                                            }}\n                                                            onClick={() => window.open(selectedMember.id_img_back, '_blank')}\n                                                        />\n                                                    ) : (\n                                                        <div className=\"text-muted text-center py-3\" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>\n                                                            {t('no_image_uploaded')}\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                        </Card.Body>\n                                    </Card>\n                                </Col>\n                            </Row>\n                        </>\n                    )}\n                </Modal.Body>\n                <Modal.Footer>\n                    <Button variant=\"secondary\" onClick={closeKycModal} disabled={kycLoading}>\n                        {t('cancel')}\n                    </Button>\n                    <Button \n                        variant=\"danger\" \n                        onClick={() => handleKycDecision('rejected')}\n                        disabled={kycLoading || selectedMember?.verify_status === 'rejected'}\n                    >\n                        {kycLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaTimes className=\"me-1\" />\n                                {t('reject')}\n                            </>\n                        )}\n                    </Button>\n                    <Button \n                        variant=\"success\" \n                        onClick={() => handleKycDecision('approved')}\n                        disabled={kycLoading || selectedMember?.verify_status === 'approved'}\n                    >\n                        {kycLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaCheck className=\"me-1\" />\n                                {t('approve')}\n                            </>\n                        )}\n                    </Button>\n                </Modal.Footer>\n            </Modal>\n\n            {/* Change Agent Modal */}\n            <Modal show={showChangeAgentModal} onHide={closeChangeAgentModal} size=\"md\">\n                <Modal.Header closeButton>\n                    <Modal.Title>{t('change_agent')}</Modal.Title>\n                </Modal.Header>\n                <style dangerouslySetInnerHTML={{\n                    __html: `\n                        .custom-modal-header .btn-close {\n                            background: none !important;\n                            border: none !important;\n                            opacity: 0.8 !important;\n                            filter: invert(1) !important;\n                        }\n                        .custom-modal-header .btn-close:hover {\n                            opacity: 1 !important;\n                            background-color: rgba(255, 255, 255, 0.1) !important;\n                        }\n                    `\n                }} />\n                <Modal.Body>\n                    {changeAgentError && (\n                        <Alert variant=\"danger\" className=\"mb-3\">\n                            {changeAgentError}\n                        </Alert>\n                    )}\n                    {changeAgentSuccess && (\n                        <Alert variant=\"success\" className=\"mb-3\">\n                            {changeAgentSuccess}\n                        </Alert>\n                    )}\n                    \n                    {changeAgentMember && (\n                        <div className=\"mb-4\">\n                            <Card>\n                                <Card.Header>\n                                    <strong>{t('customer_info')}</strong>\n                                </Card.Header>\n                                <Card.Body>\n                                    <p><strong>{t('username')}:</strong> {changeAgentMember.users?.email || '-'}</p>\n                                    <p><strong>{t('real_name')}:</strong> {changeAgentMember.real_name || '-'}</p>\n                                    <p><strong>{t('current_status')}:</strong> {getStatusBadge(changeAgentMember.verify_status)}</p>\n                                </Card.Body>\n                            </Card>\n                        </div>\n                    )}\n\n                    <Form.Group className=\"mb-3\">\n                        <Form.Label><strong>{t('select_new_agent')}</strong></Form.Label>\n                        <Form.Select\n                            value={selectedNewAgent}\n                            onChange={(e) => setSelectedNewAgent(e.target.value)}\n                            disabled={changeAgentLoading}\n                        >\n                            <option value=\"\">{t('please_select_agent')}</option>\n                            {availableAgents.map(agent => (\n                                <option key={agent.user_id} value={agent.user_id}>\n                                    {agent.brand_name || agent.users?.email || agent.user_id} \n                                    {agent.commission_pct && ` (${agent.commission_pct}%)`}\n                                </option>\n                            ))}\n                        </Form.Select>\n                        {availableAgents.length === 0 && (\n                            <Form.Text className=\"text-muted\">\n                                {t('no_available_agents')}\n                            </Form.Text>\n                        )}\n                    </Form.Group>\n                </Modal.Body>\n                <Modal.Footer>\n                    <Button variant=\"secondary\" onClick={closeChangeAgentModal} disabled={changeAgentLoading}>\n                        {t('cancel')}\n                    </Button>\n                    <Button \n                        variant=\"primary\" \n                        onClick={handleConfirmChangeAgent}\n                        disabled={changeAgentLoading || !selectedNewAgent || availableAgents.length === 0}\n                    >\n                        {changeAgentLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('processing')}\n                            </>\n                        ) : (\n                            <>\n                                <FaExchangeAlt className=\"me-1\" />\n                                {t('confirm_change')}\n                            </>\n                        )}\n                    </Button>\n                </Modal.Footer>\n            </Modal>\n\n            {/* Add Member Modal */}\n            <Modal show={showAddMemberModal} onHide={closeAddMemberModal} size=\"md\">\n                <Modal.Header closeButton>\n                    <Modal.Title>{t('add_member')}</Modal.Title>\n                </Modal.Header>\n                <Modal.Body>\n                    {addMemberError && (\n                        <Alert variant=\"danger\" className=\"mb-3\">\n                            {addMemberError}\n                        </Alert>\n                    )}\n                    {addMemberSuccess && (\n                        <Alert variant=\"success\" className=\"mb-3\">\n                            {addMemberSuccess}\n                        </Alert>\n                    )}\n\n                    <Form>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label><strong>{t('email_address')}</strong></Form.Label>\n                            <Form.Control\n                                type=\"email\"\n                                value={newMemberEmail}\n                                onChange={(e) => setNewMemberEmail(e.target.value)}\n                                placeholder={t('enter_email_address')}\n                                disabled={addMemberLoading}\n                                required\n                            />\n                            <Form.Text className=\"text-muted\">\n                                {t('member_email_help')}\n                            </Form.Text>\n                        </Form.Group>\n\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label><strong>{t('password')}</strong></Form.Label>\n                            <Form.Control\n                                type=\"password\"\n                                value={newMemberPassword}\n                                onChange={(e) => setNewMemberPassword(e.target.value)}\n                                placeholder={t('enter_password')}\n                                disabled={addMemberLoading}\n                                minLength={6}\n                                required\n                            />\n                            <Form.Text className=\"text-muted\">\n                                {t('password_min_6_chars')}\n                            </Form.Text>\n                        </Form.Group>\n\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label><strong>{t('invite_code')}</strong></Form.Label>\n                            <Form.Control\n                                type=\"text\"\n                                value={newMemberInviteCode}\n                                onChange={(e) => setNewMemberInviteCode(e.target.value)}\n                                placeholder={t('enter_invite_code')}\n                                disabled={addMemberLoading}\n                                required\n                            />\n                            <Form.Text className=\"text-muted\">\n                                {t('invite_code_help')}\n                            </Form.Text>\n                        </Form.Group>\n                    </Form>\n                </Modal.Body>\n                <Modal.Footer>\n                    <Button variant=\"secondary\" onClick={closeAddMemberModal} disabled={addMemberLoading}>\n                        {t('cancel')}\n                    </Button>\n                    <Button\n                        variant=\"primary\"\n                        onClick={handleConfirmAddMember}\n                        disabled={addMemberLoading || !newMemberEmail || !newMemberPassword || !newMemberInviteCode}\n                    >\n                        {addMemberLoading ? (\n                            <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                {t('creating')}\n                            </>\n                        ) : (\n                            <>\n                                <FaPlus className=\"me-1\" />\n                                {t('create_member')}\n                            </>\n                        )}\n                    </Button>\n                </Modal.Footer>\n            </Modal>\n        </Container>\n    );\n};\n\nexport default Members;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,MAAM,CAAEC,IAAI,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CAC3H,OAASC,QAAQ,CAAEC,MAAM,CAAEC,WAAW,CAAEC,aAAa,CAAEC,OAAO,CAAEC,OAAO,KAAQ,gBAAgB,CAC/F,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/C,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAClB,KAAM,CAAEC,CAAE,CAAC,CAAGX,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACY,OAAO,CAAEC,UAAU,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACmC,OAAO,CAAEC,UAAU,CAAC,CAAGpC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqC,UAAU,CAAEC,aAAa,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACuC,YAAY,CAAEC,eAAe,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACyC,SAAS,CAAEC,YAAY,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC2C,OAAO,CAAEC,UAAU,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC6C,eAAe,CAAEC,kBAAkB,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC+C,YAAY,CAAEC,eAAe,CAAC,CAAGhD,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACiD,cAAc,CAAEC,iBAAiB,CAAC,CAAGlD,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACmD,UAAU,CAAEC,aAAa,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACqD,QAAQ,CAAEC,WAAW,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACuD,UAAU,CAAEC,aAAa,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CAEhD;AACA,KAAM,CAACyD,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG1D,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAAC2D,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5D,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAAC6D,eAAe,CAAEC,kBAAkB,CAAC,CAAG9D,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC+D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACiE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlE,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACmE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpE,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACqE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAACuE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxE,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACyE,cAAc,CAAEC,iBAAiB,CAAC,CAAG1E,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC2E,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5E,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAAC6E,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG9E,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAAC+E,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGhF,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACiF,cAAc,CAAEC,iBAAiB,CAAC,CAAGlF,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACmF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpF,QAAQ,CAAC,EAAE,CAAC,CAE5DC,SAAS,CAAC,IAAM,CACR,KAAM,CAAAoF,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,QAAQ,CAAGlE,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACkE,QAAQ,CAAE,OAEflD,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEmD,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPpD,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEmD,IAAI,CAAEI,SAAS,CAAEC,KAAK,CAAEC,YAAa,CAAC,CAAG,KAAM,CAAAP,QAAQ,CAC1DQ,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,yEAAyE,CAAC,CACjFC,EAAE,CAAC,UAAU,CAAER,IAAI,CAACS,EAAE,CAAC,CACvBC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIN,YAAY,EAAI,CAACF,SAAS,CAAE,CAC5BS,OAAO,CAACR,KAAK,CAAC,mCAAmC,CAAEC,YAAY,CAAC,CAChEzD,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAAiE,OAAO,CAAGV,SAAS,CAACW,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAE7D,KAAM,CAAEnB,IAAI,CAAEoB,YAAY,CAAEf,KAAK,CAAEgB,SAAU,CAAC,CAAG,KAAM,CAAAtB,QAAQ,CAC1DQ,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,uBAAuB,CAAC,CAC/Bc,EAAE,CAAC,IAAI,CAAER,OAAO,CAAC,CAEtB,GAAIO,SAAS,CAAE,CACXR,OAAO,CAACR,KAAK,CAAC,uBAAuB,CAAEgB,SAAS,CAAC,CACrD,CAEA;AACA,KAAM,CAAAE,QAAQ,CAAG,GAAI,CAAAC,GAAG,CAAC,CAACJ,YAAY,EAAI,EAAE,EAAEL,GAAG,CAACU,CAAC,EAAI,CAACA,CAAC,CAACf,EAAE,CAAEe,CAAC,CAAC,CAAC,CAAC,CAElE,KAAM,CAAAC,eAAe,CAAGtB,SAAS,CAACW,GAAG,CAACC,CAAC,GAAK,CACxC,GAAGA,CAAC,CACJW,KAAK,CAAEJ,QAAQ,CAACK,GAAG,CAACZ,CAAC,CAACC,OAAO,CAAC,EAAI,CAAC,CACvC,CAAC,CAAC,CAAC,CAEHtE,UAAU,CAAC+E,eAAe,CAAC,CAC3B7E,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAELiD,YAAY,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN;AACApF,SAAS,CAAC,IAAM,CACZ,GAAI,CAAAmH,QAAQ,CAAGnF,OAAO,CAEtB;AACA,GAAII,UAAU,CAAE,CACZ+E,QAAQ,CAAGA,QAAQ,CAACX,MAAM,CAACY,MAAM,OAAAC,aAAA,CAAAC,mBAAA,CAAAC,iBAAA,OAC7B,EAAAF,aAAA,CAAAD,MAAM,CAACH,KAAK,UAAAI,aAAA,kBAAAC,mBAAA,CAAZD,aAAA,CAAcG,KAAK,UAAAF,mBAAA,iBAAnBA,mBAAA,CAAqBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtF,UAAU,CAACqF,WAAW,CAAC,CAAC,CAAC,KAAAF,iBAAA,CACrEH,MAAM,CAACO,SAAS,UAAAJ,iBAAA,iBAAhBA,iBAAA,CAAkBE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtF,UAAU,CAACqF,WAAW,CAAC,CAAC,CAAC,GACtE,CAAC,CACL,CAEA;AACA,GAAInF,YAAY,CAAE,CACd6E,QAAQ,CAAGA,QAAQ,CAACX,MAAM,CAACY,MAAM,EAAIA,MAAM,CAACQ,aAAa,GAAKtF,YAAY,CAAC,CAC/E,CAEA;AACA,GAAIE,SAAS,CAAE,CACX2E,QAAQ,CAAGA,QAAQ,CAACX,MAAM,CAACY,MAAM,OAAAS,cAAA,OAC7B,IAAI,CAAAC,IAAI,EAAAD,cAAA,CAACT,MAAM,CAACH,KAAK,UAAAY,cAAA,iBAAZA,cAAA,CAAcE,UAAU,CAAC,EAAI,GAAI,CAAAD,IAAI,CAACtF,SAAS,CAAC,EAC7D,CAAC,CACL,CACA,GAAIE,OAAO,CAAE,CACTyE,QAAQ,CAAGA,QAAQ,CAACX,MAAM,CAACY,MAAM,OAAAY,cAAA,OAC7B,IAAI,CAAAF,IAAI,EAAAE,cAAA,CAACZ,MAAM,CAACH,KAAK,UAAAe,cAAA,iBAAZA,cAAA,CAAcD,UAAU,CAAC,EAAI,GAAI,CAAAD,IAAI,CAACpF,OAAO,CAAC,EAC3D,CAAC,CACL,CAEAG,kBAAkB,CAACsE,QAAQ,CAAC,CAChC,CAAC,CAAE,CAACnF,OAAO,CAAEI,UAAU,CAAEE,YAAY,CAAEE,SAAS,CAAEE,OAAO,CAAC,CAAC,CAE3D;AACA,KAAM,CAAAuF,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACrC,KAAM,CAAA5C,QAAQ,CAAGlE,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACkE,QAAQ,CAAE,OAEf,GAAI,CACA;AACA,KAAM,CAAEC,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CACxD,GAAI,CAACF,IAAI,CAAE,OAEX;AACA,KAAM,CAAED,IAAI,CAAE4C,MAAM,CAAEvC,KAAM,CAAC,CAAG,KAAM,CAAAN,QAAQ,CACzCQ,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDqC,GAAG,CAAC,SAAS,CAAE5C,IAAI,CAACS,EAAE,CAAC,CAAE;AAE9B,GAAIL,KAAK,CAAE,CACPQ,OAAO,CAACR,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,OACJ,CAEA9B,kBAAkB,CAACqE,MAAM,EAAI,EAAE,CAAC,CACpC,CAAE,MAAOvC,KAAK,CAAE,CACZQ,OAAO,CAACR,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CAC1D,CACJ,CAAC,CAED,KAAM,CAAAyC,cAAc,CAAIC,MAAM,EAAK,CAC/B,OAAQA,MAAM,EACV,IAAK,UAAU,CACX,mBAAO/G,IAAA,CAAChB,KAAK,EAACgI,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAExG,CAAC,CAAC,UAAU,CAAC,CAAQ,CAAC,CACtD,IAAK,SAAS,CACV,mBAAOT,IAAA,CAAChB,KAAK,EAACgI,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAExG,CAAC,CAAC,gBAAgB,CAAC,CAAQ,CAAC,CAC5D,IAAK,UAAU,CACX,mBAAOT,IAAA,CAAChB,KAAK,EAACgI,EAAE,CAAC,QAAQ,CAAAC,QAAA,CAAExG,CAAC,CAAC,UAAU,CAAC,CAAQ,CAAC,CACrD,IAAK,cAAc,CACf,mBAAOT,IAAA,CAAChB,KAAK,EAACgI,EAAE,CAAC,MAAM,CAAAC,QAAA,CAAExG,CAAC,CAAC,cAAc,CAAC,CAAQ,CAAC,CACvD,QACI,mBAAOT,IAAA,CAAChB,KAAK,EAACgI,EAAE,CAAC,WAAW,CAAAC,QAAA,CAAEF,MAAM,EAAItG,CAAC,CAAC,eAAe,CAAC,CAAQ,CAAC,CAC3E,CACJ,CAAC,CAED,KAAM,CAAAyG,YAAY,CAAGA,CAAA,GAAM,CACvB;AACArC,OAAO,CAACsC,GAAG,CAAC,kBAAkB,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC1BnE,qBAAqB,CAAC,IAAI,CAAC,CAC3BE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,sBAAsB,CAAC,EAAE,CAAC,CAC1BI,iBAAiB,CAAC,EAAE,CAAC,CACrBE,mBAAmB,CAAC,EAAE,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAwD,sBAAsB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CAACnE,cAAc,EAAI,CAACE,iBAAiB,EAAI,CAACE,mBAAmB,CAAE,CAC/DK,iBAAiB,CAAClD,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAC3C,OACJ,CAEA;AACA,KAAM,CAAA6G,UAAU,CAAG,4BAA4B,CAC/C,GAAI,CAACA,UAAU,CAACC,IAAI,CAACrE,cAAc,CAAC,CAAE,CAClCS,iBAAiB,CAAClD,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAC5C,OACJ,CAEA;AACA,GAAI2C,iBAAiB,CAACoE,MAAM,CAAG,CAAC,CAAE,CAC9B7D,iBAAiB,CAAClD,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAC3C,OACJ,CAEAgD,mBAAmB,CAAC,IAAI,CAAC,CACzBE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,mBAAmB,CAAC,EAAE,CAAC,CAEvB,GAAI,CACA,KAAM,CAAAE,QAAQ,CAAGlE,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACkE,QAAQ,CAAE,CACX,KAAM,IAAI,CAAA0D,KAAK,CAAC,4BAA4B,CAAC,CACjD,CAEA;AACA,KAAM,CAAEzD,IAAI,CAAE,CAAEC,IAAI,CAAEyD,WAAY,CAAE,CAAC,CAAG,KAAM,CAAA3D,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CACrE,GAAI,CAACuD,WAAW,CAAE,CACd,KAAM,IAAI,CAAAD,KAAK,CAAC,yBAAyB,CAAC,CAC9C,CAEA;AACA;AACA,KAAM,CAAAE,kBAAkB,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGC,MAAM,CAACC,MAAM,CAACC,MAAM,eAAe,CAAE,CAC3EC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACL,cAAc,CAAE,kBAAkB,CAClC,YAAY,CAAEJ,MAAM,CAACC,MAAM,CAACI,KAChC,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACjBnC,KAAK,CAAEhD,cAAc,CACrBoF,QAAQ,CAAElF,iBAAiB,CAC3BmF,WAAW,CAAEjF,mBAAmB,CAChCkF,QAAQ,CAAEd,WAAW,CAAChD,EAC1B,CAAC,CACL,CAAC,CAAC,CAEF,GAAI,CAACiD,kBAAkB,CAACc,EAAE,CAAE,CACxB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAf,kBAAkB,CAACgB,IAAI,CAAC,CAAC,CACjD,KAAM,IAAI,CAAAlB,KAAK,CAACiB,SAAS,CAACE,OAAO,EAAI,yBAAyB,CAAC,CACnE,CAEA,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAlB,kBAAkB,CAACgB,IAAI,CAAC,CAAC,CAE9C,GAAI,CAACE,MAAM,CAACC,OAAO,CAAE,CACjB,KAAM,IAAI,CAAArB,KAAK,CAACoB,MAAM,CAACD,OAAO,EAAI,yBAAyB,CAAC,CAChE,CAEA/E,mBAAmB,CAACpD,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAErD;AACAsI,UAAU,CAAC,IAAM,CACb9F,qBAAqB,CAAC,KAAK,CAAC,CAC5BE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,sBAAsB,CAAC,EAAE,CAAC,CAC1B;AACAsE,MAAM,CAACmB,QAAQ,CAACC,MAAM,CAAC,CAAC,CAC5B,CAAC,CAAE,IAAI,CAAC,CAEZ,CAAE,MAAO5E,KAAK,CAAE,CACZQ,OAAO,CAACR,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CV,iBAAiB,CAACU,KAAK,CAACuE,OAAO,EAAInI,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAClE,CAAC,OAAS,CACNgD,mBAAmB,CAAC,KAAK,CAAC,CAC9B,CACJ,CAAC,CAED,KAAM,CAAAyF,mBAAmB,CAAGA,CAAA,GAAM,CAC9BjG,qBAAqB,CAAC,KAAK,CAAC,CAC5BE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,sBAAsB,CAAC,EAAE,CAAC,CAC1BI,iBAAiB,CAAC,EAAE,CAAC,CACrBE,mBAAmB,CAAC,EAAE,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAsF,eAAe,CAAIrD,MAAM,EAAK,CAChCnE,iBAAiB,CAACmE,MAAM,CAAC,CACzBrE,eAAe,CAAC,IAAI,CAAC,CACrBM,WAAW,CAAC,EAAE,CAAC,CACfE,aAAa,CAAC,EAAE,CAAC,CACrB,CAAC,CAED,KAAM,CAAAmH,iBAAiB,CAAG,KAAO,CAAAC,QAAQ,EAAK,CAC1C,GAAI,CAAC3H,cAAc,CAAE,OAErBG,aAAa,CAAC,IAAI,CAAC,CACnBE,WAAW,CAAC,EAAE,CAAC,CACfE,aAAa,CAAC,EAAE,CAAC,CAEjB,GAAI,CACA,KAAM,CAAA8B,QAAQ,CAAGlE,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACkE,QAAQ,CAAE,CACX,KAAM,IAAI,CAAA0D,KAAK,CAAC,4BAA4B,CAAC,CACjD,CAEA,KAAM,CAAEzD,IAAI,CAAEK,KAAM,CAAC,CAAG,KAAM,CAAAN,QAAQ,CACjCQ,IAAI,CAAC,mBAAmB,CAAC,CACzB+E,MAAM,CAAC,CAAEhD,aAAa,CAAE+C,QAAS,CAAC,CAAC,CACnC5E,EAAE,CAAC,SAAS,CAAE/C,cAAc,CAACuD,OAAO,CAAC,CACrCT,MAAM,CAAC,CAAC,CAEb,GAAIH,KAAK,CAAE,CACPQ,OAAO,CAACR,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,KAAM,CAAAA,KAAK,CACf,CAEA;AACA,GAAI,CAACL,IAAI,EAAIA,IAAI,CAACwD,MAAM,GAAK,CAAC,CAAE,CAE5B;AACA,KAAM,CAAExD,IAAI,CAAEuF,cAAc,CAAElF,KAAK,CAAEmF,WAAY,CAAC,CAAG,KAAM,CAAAzF,QAAQ,CAC9DQ,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,CAAE/C,cAAc,CAACuD,OAAO,CAAC,CAE1C,GAAIuE,WAAW,CAAE,CACb3E,OAAO,CAACR,KAAK,CAAC,iCAAiC,CAAEmF,WAAW,CAAC,CAC7D,KAAM,CAAAA,WAAW,CACrB,CAEA,GAAI,CAACD,cAAc,EAAIA,cAAc,CAAC/B,MAAM,GAAK,CAAC,CAAE,CAChD,KAAM,IAAI,CAAAC,KAAK,CAAC,4BAA4B,CAAC,CACjD,CACJ,CAEA;AACA9G,UAAU,CAAC8I,WAAW,EAClBA,WAAW,CAAC1E,GAAG,CAACe,MAAM,EAClBA,MAAM,CAACb,OAAO,GAAKvD,cAAc,CAACuD,OAAO,CACnC,CAAE,GAAGa,MAAM,CAAEQ,aAAa,CAAE+C,QAAS,CAAC,CACtCvD,MACV,CACJ,CAAC,CAED;AACAnE,iBAAiB,CAAC+H,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEpD,aAAa,CAAE+C,QAAS,CAAC,CAAC,CAAC,CAEjEpH,aAAa,CAACoH,QAAQ,GAAK,UAAU,CAAG5I,CAAC,CAAC,sBAAsB,CAAC,CAAGA,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAE9F;AACAsI,UAAU,CAAC,IAAM,CACbtH,eAAe,CAAC,KAAK,CAAC,CACtBE,iBAAiB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAAE,IAAI,CAAC,CAEZ,CAAE,MAAO0C,KAAK,CAAE,CACZQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDtC,WAAW,CAACsC,KAAK,CAACuE,OAAO,EAAInI,CAAC,CAAC,kBAAkB,CAAC,CAAC,CACvD,CAAC,OAAS,CACNoB,aAAa,CAAC,KAAK,CAAC,CACxB,CACJ,CAAC,CAED,KAAM,CAAA8H,aAAa,CAAGA,CAAA,GAAM,CACxBlI,eAAe,CAAC,KAAK,CAAC,CACtBE,iBAAiB,CAAC,IAAI,CAAC,CACvBI,WAAW,CAAC,EAAE,CAAC,CACfE,aAAa,CAAC,EAAE,CAAC,CACrB,CAAC,CAED,KAAM,CAAA2H,iBAAiB,CAAG,KAAO,CAAA9D,MAAM,EAAK,CACxCzD,oBAAoB,CAACyD,MAAM,CAAC,CAC5BjD,mBAAmB,CAAC,EAAE,CAAC,CACvBE,qBAAqB,CAAC,EAAE,CAAC,CACzBN,mBAAmB,CAAC,EAAE,CAAC,CAEvB;AACA,KAAM,CAAAkE,oBAAoB,CAAC,CAAC,CAE5BxE,uBAAuB,CAAC,IAAI,CAAC,CACjC,CAAC,CAED,KAAM,CAAA0H,wBAAwB,CAAG,KAAAA,CAAA,GAAY,CACzC,GAAI,CAACzH,iBAAiB,EAAI,CAACI,gBAAgB,CAAE,CACzCK,mBAAmB,CAACpC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAC7C,OACJ,CAEAkC,qBAAqB,CAAC,IAAI,CAAC,CAC3BE,mBAAmB,CAAC,EAAE,CAAC,CACvBE,qBAAqB,CAAC,EAAE,CAAC,CAEzB,GAAI,CACA,KAAM,CAAAgB,QAAQ,CAAGlE,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACkE,QAAQ,CAAE,CACX,KAAM,IAAI,CAAA0D,KAAK,CAAC,4BAA4B,CAAC,CACjD,CAEA;AACA,KAAM,CAAEzD,IAAI,CAAEK,KAAM,CAAC,CAAG,KAAM,CAAAN,QAAQ,CACjCQ,IAAI,CAAC,mBAAmB,CAAC,CACzB+E,MAAM,CAAC,CAAEd,QAAQ,CAAEhG,gBAAiB,CAAC,CAAC,CACtCiC,EAAE,CAAC,SAAS,CAAErC,iBAAiB,CAAC6C,OAAO,CAAC,CACxCT,MAAM,CAAC,CAAC,CAEb,GAAIH,KAAK,CAAE,CACPQ,OAAO,CAACR,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,KAAM,CAAAA,KAAK,CACf,CAEA,GAAI,CAACL,IAAI,EAAIA,IAAI,CAACwD,MAAM,GAAK,CAAC,CAAE,CAC5B,KAAM,IAAI,CAAAC,KAAK,CAAC,mCAAmC,CAAC,CACxD,CAEA;AACA9G,UAAU,CAAC8I,WAAW,EAClBA,WAAW,CAACvE,MAAM,CAACY,MAAM,EAAIA,MAAM,CAACb,OAAO,GAAK7C,iBAAiB,CAAC6C,OAAO,CAC7E,CAAC,CAEDlC,qBAAqB,CAACtC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAEtD;AACAsI,UAAU,CAAC,IAAM,CACb5G,uBAAuB,CAAC,KAAK,CAAC,CAC9BE,oBAAoB,CAAC,IAAI,CAAC,CAC1BI,mBAAmB,CAAC,EAAE,CAAC,CAC3B,CAAC,CAAE,IAAI,CAAC,CAEZ,CAAE,MAAO4B,KAAK,CAAE,CACZQ,OAAO,CAACR,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CxB,mBAAmB,CAACwB,KAAK,CAACuE,OAAO,EAAInI,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACjE,CAAC,OAAS,CACNkC,qBAAqB,CAAC,KAAK,CAAC,CAChC,CACJ,CAAC,CAED,KAAM,CAAAmH,qBAAqB,CAAGA,CAAA,GAAM,CAChC3H,uBAAuB,CAAC,KAAK,CAAC,CAC9BE,oBAAoB,CAAC,IAAI,CAAC,CAC1BI,mBAAmB,CAAC,EAAE,CAAC,CACvBI,mBAAmB,CAAC,EAAE,CAAC,CACvBE,qBAAqB,CAAC,EAAE,CAAC,CAC7B,CAAC,CAGD,GAAInC,OAAO,CAAE,CACT,mBAAOZ,IAAA,QAAAiH,QAAA,CAAMxG,CAAC,CAAC,iBAAiB,CAAC,CAAM,CAAC,CAC5C,CAEA,mBACIP,KAAA,CAACvB,SAAS,EAAAsI,QAAA,eACNjH,IAAA,OAAI+J,SAAS,CAAC,MAAM,CAAA9C,QAAA,CAAExG,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,cAG5CT,IAAA,CAACpB,GAAG,EAACmL,SAAS,CAAC,MAAM,CAAA9C,QAAA,cACjBjH,IAAA,CAACnB,GAAG,EAAAoI,QAAA,cACAjH,IAAA,CAAClB,IAAI,EAAAmI,QAAA,cACDjH,IAAA,CAAClB,IAAI,CAACkL,IAAI,EAAA/C,QAAA,cACN/G,KAAA,CAACtB,GAAG,EAACmL,SAAS,CAAC,iBAAiB,CAAA9C,QAAA,eAC5BjH,IAAA,CAACnB,GAAG,EAACoL,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACP/G,KAAA,CAACjB,MAAM,EACHiL,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAE/C,eAAgB,CACzB2C,SAAS,CAAC,MAAM,CAAA9C,QAAA,eAEhBjH,IAAA,CAACR,MAAM,EAACuK,SAAS,CAAC,MAAM,CAAE,CAAC,CAC1BtJ,CAAC,CAAC,YAAY,CAAC,EACZ,CAAC,CACR,CAAC,cACNT,IAAA,CAACnB,GAAG,EAACoL,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACP/G,KAAA,CAAChB,IAAI,CAACkL,KAAK,EAAAnD,QAAA,eACPjH,IAAA,CAACd,IAAI,CAACmL,KAAK,EAAApD,QAAA,CAAExG,CAAC,CAAC,iBAAiB,CAAC,CAAa,CAAC,cAC/CT,IAAA,CAACb,UAAU,EAAA8H,QAAA,cACPjH,IAAA,CAACd,IAAI,CAACoL,OAAO,EACTC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAE/J,CAAC,CAAC,uBAAuB,CAAE,CACxCgK,KAAK,CAAE3J,UAAW,CAClB4J,QAAQ,CAAGC,CAAC,EAAK5J,aAAa,CAAC4J,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClD,CAAC,CACM,CAAC,EACL,CAAC,CACZ,CAAC,cACNzK,IAAA,CAACnB,GAAG,EAACoL,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACP/G,KAAA,CAAChB,IAAI,CAACkL,KAAK,EAAAnD,QAAA,eACPjH,IAAA,CAACd,IAAI,CAACmL,KAAK,EAAApD,QAAA,CAAExG,CAAC,CAAC,eAAe,CAAC,CAAa,CAAC,cAC7CP,KAAA,CAAChB,IAAI,CAAC2L,MAAM,EACRJ,KAAK,CAAEzJ,YAAa,CACpB0J,QAAQ,CAAGC,CAAC,EAAK1J,eAAe,CAAC0J,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAxD,QAAA,eAEjDjH,IAAA,WAAQyK,KAAK,CAAC,EAAE,CAAAxD,QAAA,CAAExG,CAAC,CAAC,sBAAsB,CAAC,CAAS,CAAC,cACrDT,IAAA,WAAQyK,KAAK,CAAC,SAAS,CAAAxD,QAAA,CAAExG,CAAC,CAAC,gBAAgB,CAAC,CAAS,CAAC,cACtDT,IAAA,WAAQyK,KAAK,CAAC,UAAU,CAAAxD,QAAA,CAAExG,CAAC,CAAC,UAAU,CAAC,CAAS,CAAC,cACjDT,IAAA,WAAQyK,KAAK,CAAC,UAAU,CAAAxD,QAAA,CAAExG,CAAC,CAAC,UAAU,CAAC,CAAS,CAAC,cACjDT,IAAA,WAAQyK,KAAK,CAAC,cAAc,CAAAxD,QAAA,CAAExG,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,EAChD,CAAC,EACN,CAAC,CACZ,CAAC,cACNT,IAAA,CAACnB,GAAG,EAACoL,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACP/G,KAAA,CAAChB,IAAI,CAACkL,KAAK,EAAAnD,QAAA,eACPjH,IAAA,CAACd,IAAI,CAACmL,KAAK,EAAApD,QAAA,CAAExG,CAAC,CAAC,YAAY,CAAC,CAAa,CAAC,cAC1CT,IAAA,CAACd,IAAI,CAACoL,OAAO,EACTC,IAAI,CAAC,MAAM,CACXE,KAAK,CAAEvJ,SAAU,CACjBwJ,QAAQ,CAAGC,CAAC,EAAKxJ,YAAY,CAACwJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjD,CAAC,EACM,CAAC,CACZ,CAAC,cACNzK,IAAA,CAACnB,GAAG,EAACoL,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACP/G,KAAA,CAAChB,IAAI,CAACkL,KAAK,EAAAnD,QAAA,eACPjH,IAAA,CAACd,IAAI,CAACmL,KAAK,EAAApD,QAAA,CAAExG,CAAC,CAAC,UAAU,CAAC,CAAa,CAAC,cACxCT,IAAA,CAACd,IAAI,CAACoL,OAAO,EACTC,IAAI,CAAC,MAAM,CACXE,KAAK,CAAErJ,OAAQ,CACfsJ,QAAQ,CAAGC,CAAC,EAAKtJ,UAAU,CAACsJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/C,CAAC,EACM,CAAC,CACZ,CAAC,cACNzK,IAAA,CAACnB,GAAG,EAACoL,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACPjH,IAAA,CAACf,MAAM,EACHiL,OAAO,CAAC,iBAAiB,CACzBC,OAAO,CAAEjD,YAAa,CACtB6C,SAAS,CAAC,MAAM,CAAA9C,QAAA,cAEhBjH,IAAA,CAACT,QAAQ,GAAE,CAAC,CACR,CAAC,CACR,CAAC,EACL,CAAC,CACC,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAGNS,IAAA,CAACpB,GAAG,EAAAqI,QAAA,cACAjH,IAAA,CAACnB,GAAG,EAAAoI,QAAA,cACAjH,IAAA,CAAClB,IAAI,EAAAmI,QAAA,cACDjH,IAAA,CAAClB,IAAI,CAACkL,IAAI,EAAA/C,QAAA,cACN/G,KAAA,CAACnB,KAAK,EAAC+L,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAhE,QAAA,eACpCjH,IAAA,UAAAiH,QAAA,cACI/G,KAAA,OAAA+G,QAAA,eACIjH,IAAA,OAAAiH,QAAA,CAAKxG,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBT,IAAA,OAAAiH,QAAA,CAAKxG,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBT,IAAA,OAAAiH,QAAA,CAAKxG,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBT,IAAA,OAAAiH,QAAA,CAAKxG,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC9BT,IAAA,OAAAiH,QAAA,CAAKxG,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BT,IAAA,OAAAiH,QAAA,CAAKxG,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBT,IAAA,OAAAiH,QAAA,CAAKxG,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCT,IAAA,OAAAiH,QAAA,CAAKxG,CAAC,CAAC,SAAS,CAAC,CAAK,CAAC,EACvB,CAAC,CACF,CAAC,cACRT,IAAA,UAAAiH,QAAA,CACK3F,eAAe,CAACkG,MAAM,GAAK,CAAC,cACzBxH,IAAA,OAAAiH,QAAA,cACIjH,IAAA,OAAIkL,OAAO,CAAC,GAAG,CAACnB,SAAS,CAAC,aAAa,CAAA9C,QAAA,CAAExG,CAAC,CAAC,kBAAkB,CAAC,CAAK,CAAC,CACpE,CAAC,CAELa,eAAe,CAACyD,GAAG,CAACe,MAAM,OAAAqF,cAAA,CAAAC,cAAA,oBACtBlL,KAAA,OAAA+G,QAAA,eACIjH,IAAA,OAAAiH,QAAA,CAAK,EAAAkE,cAAA,CAAArF,MAAM,CAACH,KAAK,UAAAwF,cAAA,iBAAZA,cAAA,CAAcjF,KAAK,GAAI,GAAG,CAAK,CAAC,cACrClG,IAAA,OAAAiH,QAAA,CAAKnB,MAAM,CAACO,SAAS,EAAI,GAAG,CAAK,CAAC,cAClCrG,IAAA,OAAAiH,QAAA,CAAKnB,MAAM,CAACuF,SAAS,EAAI,GAAG,CAAK,CAAC,cAClCrL,IAAA,OAAAiH,QAAA,CACKnB,MAAM,CAACwF,YAAY,cAChBtL,IAAA,QACIuL,GAAG,CAAEzF,MAAM,CAACwF,YAAa,CACzBE,GAAG,CAAC,UAAU,CACdC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,OAAO,CAClBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SACZ,CAAE,CACF3B,OAAO,CAAEA,CAAA,GAAMtC,MAAM,CAACkE,IAAI,CAACjG,MAAM,CAACwF,YAAY,CAAE,QAAQ,CAAE,CAC7D,CAAC,cAEFtL,IAAA,SAAM+J,SAAS,CAAC,YAAY,CAAA9C,QAAA,CAAC,GAAC,CAAM,CACvC,CACD,CAAC,cACLjH,IAAA,OAAAiH,QAAA,CACKnB,MAAM,CAACkG,WAAW,cACfhM,IAAA,QACIuL,GAAG,CAAEzF,MAAM,CAACkG,WAAY,CACxBR,GAAG,CAAC,SAAS,CACbC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,OAAO,CAClBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SACZ,CAAE,CACF3B,OAAO,CAAEA,CAAA,GAAMtC,MAAM,CAACkE,IAAI,CAACjG,MAAM,CAACkG,WAAW,CAAE,QAAQ,CAAE,CAC5D,CAAC,cAEFhM,IAAA,SAAM+J,SAAS,CAAC,YAAY,CAAA9C,QAAA,CAAC,GAAC,CAAM,CACvC,CACD,CAAC,cACLjH,IAAA,OAAAiH,QAAA,CAAKH,cAAc,CAAChB,MAAM,CAACQ,aAAa,CAAC,CAAK,CAAC,cAC/CtG,IAAA,OAAAiH,QAAA,CAAK,CAAAmE,cAAA,CAAAtF,MAAM,CAACH,KAAK,UAAAyF,cAAA,WAAZA,cAAA,CAAc3E,UAAU,CAAG,GAAI,CAAAD,IAAI,CAACV,MAAM,CAACH,KAAK,CAACc,UAAU,CAAC,CAACwF,cAAc,CAAC,CAAC,CAAG,GAAG,CAAK,CAAC,cAC9FjM,IAAA,OAAAiH,QAAA,cACI/G,KAAA,QAAK6J,SAAS,CAAC,gCAAgC,CAAA9C,QAAA,eAC3CjH,IAAA,CAACf,MAAM,EACHiN,IAAI,CAAC,IAAI,CACThC,OAAO,CAAC,iBAAiB,CACzBC,OAAO,CAAEA,CAAA,GAAMhB,eAAe,CAACrD,MAAM,CAAE,CACvCqG,KAAK,CAAE1L,CAAC,CAAC,YAAY,CAAE,CAAAwG,QAAA,cAEvBjH,IAAA,CAACP,WAAW,GAAE,CAAC,CACX,CAAC,cACTO,IAAA,CAACf,MAAM,EACHiN,IAAI,CAAC,IAAI,CACThC,OAAO,CAAC,iBAAiB,CACzBC,OAAO,CAAEA,CAAA,GAAMP,iBAAiB,CAAC9D,MAAM,CAAE,CACzCqG,KAAK,CAAE1L,CAAC,CAAC,cAAc,CAAE,CAAAwG,QAAA,cAEzBjH,IAAA,CAACN,aAAa,GAAE,CAAC,CACb,CAAC,EACR,CAAC,CACN,CAAC,GA7DAoG,MAAM,CAACb,OA8DZ,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAGN/E,KAAA,CAACb,KAAK,EAAC+M,IAAI,CAAE5K,YAAa,CAAC6K,MAAM,CAAE1C,aAAc,CAACuC,IAAI,CAAC,IAAI,CAAAjF,QAAA,eACvDjH,IAAA,CAACX,KAAK,CAACiN,MAAM,EAACC,WAAW,MAACxC,SAAS,CAAC,qBAAqB,CAAA9C,QAAA,cACrDjH,IAAA,CAACX,KAAK,CAACmN,KAAK,EAAAvF,QAAA,CAAExG,CAAC,CAAC,YAAY,CAAC,CAAc,CAAC,CAClC,CAAC,cACfT,IAAA,UAAOyM,uBAAuB,CAAE,CAC5BC,MAAM,CAAE;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBACgB,CAAE,CAAE,CAAC,cACL1M,IAAA,CAACX,KAAK,CAAC2K,IAAI,EAAA/C,QAAA,CACNvF,cAAc,eACXxB,KAAA,CAAAE,SAAA,EAAA6G,QAAA,EACKnF,QAAQ,eACL9B,IAAA,CAACV,KAAK,EAAC4K,OAAO,CAAC,QAAQ,CAACH,SAAS,CAAC,MAAM,CAAA9C,QAAA,CACnCnF,QAAQ,CACN,CACV,CACAE,UAAU,eACPhC,IAAA,CAACV,KAAK,EAAC4K,OAAO,CAAC,SAAS,CAACH,SAAS,CAAC,MAAM,CAAA9C,QAAA,CACpCjF,UAAU,CACR,CACV,cAED9B,KAAA,CAACtB,GAAG,EAAAqI,QAAA,eACAjH,IAAA,CAACnB,GAAG,EAACoL,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACP/G,KAAA,CAACpB,IAAI,EAACiL,SAAS,CAAC,MAAM,CAAA9C,QAAA,eAClBjH,IAAA,CAAClB,IAAI,CAACwN,MAAM,EAAArF,QAAA,cACRjH,IAAA,WAAAiH,QAAA,CAASxG,CAAC,CAAC,eAAe,CAAC,CAAS,CAAC,CAC5B,CAAC,cACdP,KAAA,CAACpB,IAAI,CAACkL,IAAI,EAAA/C,QAAA,eACN/G,KAAA,MAAA+G,QAAA,eAAG/G,KAAA,WAAA+G,QAAA,EAASxG,CAAC,CAAC,UAAU,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC,EAAAH,qBAAA,CAAAoB,cAAc,CAACiE,KAAK,UAAArF,qBAAA,iBAApBA,qBAAA,CAAsB4F,KAAK,GAAI,GAAG,EAAI,CAAC,cAC7EhG,KAAA,MAAA+G,QAAA,eAAG/G,KAAA,WAAA+G,QAAA,EAASxG,CAAC,CAAC,WAAW,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACiB,cAAc,CAAC2E,SAAS,EAAI,GAAG,EAAI,CAAC,cAC3EnG,KAAA,MAAA+G,QAAA,eAAG/G,KAAA,WAAA+G,QAAA,EAASxG,CAAC,CAAC,WAAW,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACiB,cAAc,CAAC2J,SAAS,EAAI,GAAG,EAAI,CAAC,cAC3EnL,KAAA,MAAA+G,QAAA,eAAG/G,KAAA,WAAA+G,QAAA,EAASxG,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACqG,cAAc,CAACpF,cAAc,CAAC4E,aAAa,CAAC,EAAI,CAAC,cAC7FpG,KAAA,MAAA+G,QAAA,eAAG/G,KAAA,WAAA+G,QAAA,EAASxG,CAAC,CAAC,mBAAmB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC,CAAAF,sBAAA,CAAAmB,cAAc,CAACiE,KAAK,UAAApF,sBAAA,WAApBA,sBAAA,CAAsBkG,UAAU,CAAG,GAAI,CAAAD,IAAI,CAAC9E,cAAc,CAACiE,KAAK,CAACc,UAAU,CAAC,CAACwF,cAAc,CAAC,CAAC,CAAG,GAAG,EAAI,CAAC,EAChJ,CAAC,EACV,CAAC,CACN,CAAC,cACNjM,IAAA,CAACnB,GAAG,EAACoL,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACP/G,KAAA,CAACpB,IAAI,EAACiL,SAAS,CAAC,MAAM,CAAA9C,QAAA,eAClBjH,IAAA,CAAClB,IAAI,CAACwN,MAAM,EAAArF,QAAA,cACRjH,IAAA,WAAAiH,QAAA,CAASxG,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAC3B,CAAC,cACdP,KAAA,CAACpB,IAAI,CAACkL,IAAI,EAAA/C,QAAA,eACN/G,KAAA,QAAK6J,SAAS,CAAC,MAAM,CAAA9C,QAAA,eACjB/G,KAAA,WAAA+G,QAAA,EAASxG,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAC,EAAQ,CAAC,cACvCT,IAAA,QAAK+J,SAAS,CAAC,MAAM,CAAA9C,QAAA,CAChBvF,cAAc,CAAC4J,YAAY,cACxBtL,IAAA,QACIuL,GAAG,CAAE7J,cAAc,CAAC4J,YAAa,CACjCE,GAAG,CAAC,UAAU,CACdC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbiB,SAAS,CAAE,OAAO,CAClBf,SAAS,CAAE,SAAS,CACpBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBc,MAAM,CAAE,mBACZ,CAAE,CACFzC,OAAO,CAAEA,CAAA,GAAMtC,MAAM,CAACkE,IAAI,CAACrK,cAAc,CAAC4J,YAAY,CAAE,QAAQ,CAAE,CACrE,CAAC,cAEFtL,IAAA,QAAK+J,SAAS,CAAC,6BAA6B,CAAC0B,KAAK,CAAE,CAAEmB,MAAM,CAAE,oBAAoB,CAAEf,YAAY,CAAE,KAAM,CAAE,CAAA5E,QAAA,CACrGxG,CAAC,CAAC,mBAAmB,CAAC,CACtB,CACR,CACA,CAAC,EACL,CAAC,cACNP,KAAA,QAAK6J,SAAS,CAAC,MAAM,CAAA9C,QAAA,eACjB/G,KAAA,WAAA+G,QAAA,EAASxG,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,cACtCT,IAAA,QAAK+J,SAAS,CAAC,MAAM,CAAA9C,QAAA,CAChBvF,cAAc,CAACsK,WAAW,cACvBhM,IAAA,QACIuL,GAAG,CAAE7J,cAAc,CAACsK,WAAY,CAChCR,GAAG,CAAC,SAAS,CACbC,KAAK,CAAE,CACHC,KAAK,CAAE,MAAM,CACbiB,SAAS,CAAE,OAAO,CAClBf,SAAS,CAAE,SAAS,CACpBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBc,MAAM,CAAE,mBACZ,CAAE,CACFzC,OAAO,CAAEA,CAAA,GAAMtC,MAAM,CAACkE,IAAI,CAACrK,cAAc,CAACsK,WAAW,CAAE,QAAQ,CAAE,CACpE,CAAC,cAEFhM,IAAA,QAAK+J,SAAS,CAAC,6BAA6B,CAAC0B,KAAK,CAAE,CAAEmB,MAAM,CAAE,oBAAoB,CAAEf,YAAY,CAAE,KAAM,CAAE,CAAA5E,QAAA,CACrGxG,CAAC,CAAC,mBAAmB,CAAC,CACtB,CACR,CACA,CAAC,EACL,CAAC,EACC,CAAC,EACV,CAAC,CACN,CAAC,EACL,CAAC,EACR,CACL,CACO,CAAC,cACbP,KAAA,CAACb,KAAK,CAACwN,MAAM,EAAA5F,QAAA,eACTjH,IAAA,CAACf,MAAM,EAACiL,OAAO,CAAC,WAAW,CAACC,OAAO,CAAER,aAAc,CAACmD,QAAQ,CAAElL,UAAW,CAAAqF,QAAA,CACpExG,CAAC,CAAC,QAAQ,CAAC,CACR,CAAC,cACTT,IAAA,CAACf,MAAM,EACHiL,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAEA,CAAA,GAAMf,iBAAiB,CAAC,UAAU,CAAE,CAC7C0D,QAAQ,CAAElL,UAAU,EAAI,CAAAF,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE4E,aAAa,IAAK,UAAW,CAAAW,QAAA,CAEpErF,UAAU,cACP1B,KAAA,CAAAE,SAAA,EAAA6G,QAAA,eACIjH,IAAA,SAAM+J,SAAS,CAAC,uCAAuC,CAACgD,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAO,CAAC,CAC/FtM,CAAC,CAAC,YAAY,CAAC,EAClB,CAAC,cAEHP,KAAA,CAAAE,SAAA,EAAA6G,QAAA,eACIjH,IAAA,CAACJ,OAAO,EAACmK,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3BtJ,CAAC,CAAC,QAAQ,CAAC,EACd,CACL,CACG,CAAC,cACTT,IAAA,CAACf,MAAM,EACHiL,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,CAAA,GAAMf,iBAAiB,CAAC,UAAU,CAAE,CAC7C0D,QAAQ,CAAElL,UAAU,EAAI,CAAAF,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE4E,aAAa,IAAK,UAAW,CAAAW,QAAA,CAEpErF,UAAU,cACP1B,KAAA,CAAAE,SAAA,EAAA6G,QAAA,eACIjH,IAAA,SAAM+J,SAAS,CAAC,uCAAuC,CAACgD,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAO,CAAC,CAC/FtM,CAAC,CAAC,YAAY,CAAC,EAClB,CAAC,cAEHP,KAAA,CAAAE,SAAA,EAAA6G,QAAA,eACIjH,IAAA,CAACL,OAAO,EAACoK,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3BtJ,CAAC,CAAC,SAAS,CAAC,EACf,CACL,CACG,CAAC,EACC,CAAC,EACZ,CAAC,cAGRP,KAAA,CAACb,KAAK,EAAC+M,IAAI,CAAElK,oBAAqB,CAACmK,MAAM,CAAEvC,qBAAsB,CAACoC,IAAI,CAAC,IAAI,CAAAjF,QAAA,eACvEjH,IAAA,CAACX,KAAK,CAACiN,MAAM,EAACC,WAAW,MAAAtF,QAAA,cACrBjH,IAAA,CAACX,KAAK,CAACmN,KAAK,EAAAvF,QAAA,CAAExG,CAAC,CAAC,cAAc,CAAC,CAAc,CAAC,CACpC,CAAC,cACfT,IAAA,UAAOyM,uBAAuB,CAAE,CAC5BC,MAAM,CAAE;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBACgB,CAAE,CAAE,CAAC,cACLxM,KAAA,CAACb,KAAK,CAAC2K,IAAI,EAAA/C,QAAA,EACNrE,gBAAgB,eACb5C,IAAA,CAACV,KAAK,EAAC4K,OAAO,CAAC,QAAQ,CAACH,SAAS,CAAC,MAAM,CAAA9C,QAAA,CACnCrE,gBAAgB,CACd,CACV,CACAE,kBAAkB,eACf9C,IAAA,CAACV,KAAK,EAAC4K,OAAO,CAAC,SAAS,CAACH,SAAS,CAAC,MAAM,CAAA9C,QAAA,CACpCnE,kBAAkB,CAChB,CACV,CAEAV,iBAAiB,eACdpC,IAAA,QAAK+J,SAAS,CAAC,MAAM,CAAA9C,QAAA,cACjB/G,KAAA,CAACpB,IAAI,EAAAmI,QAAA,eACDjH,IAAA,CAAClB,IAAI,CAACwN,MAAM,EAAArF,QAAA,cACRjH,IAAA,WAAAiH,QAAA,CAASxG,CAAC,CAAC,eAAe,CAAC,CAAS,CAAC,CAC5B,CAAC,cACdP,KAAA,CAACpB,IAAI,CAACkL,IAAI,EAAA/C,QAAA,eACN/G,KAAA,MAAA+G,QAAA,eAAG/G,KAAA,WAAA+G,QAAA,EAASxG,CAAC,CAAC,UAAU,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC,EAAAD,qBAAA,CAAA4B,iBAAiB,CAACuD,KAAK,UAAAnF,qBAAA,iBAAvBA,qBAAA,CAAyB0F,KAAK,GAAI,GAAG,EAAI,CAAC,cAChFhG,KAAA,MAAA+G,QAAA,eAAG/G,KAAA,WAAA+G,QAAA,EAASxG,CAAC,CAAC,WAAW,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC2B,iBAAiB,CAACiE,SAAS,EAAI,GAAG,EAAI,CAAC,cAC9EnG,KAAA,MAAA+G,QAAA,eAAG/G,KAAA,WAAA+G,QAAA,EAASxG,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACqG,cAAc,CAAC1E,iBAAiB,CAACkE,aAAa,CAAC,EAAI,CAAC,EACzF,CAAC,EACV,CAAC,CACN,CACR,cAEDpG,KAAA,CAAChB,IAAI,CAACkL,KAAK,EAACL,SAAS,CAAC,MAAM,CAAA9C,QAAA,eACxBjH,IAAA,CAACd,IAAI,CAACmL,KAAK,EAAApD,QAAA,cAACjH,IAAA,WAAAiH,QAAA,CAASxG,CAAC,CAAC,kBAAkB,CAAC,CAAS,CAAC,CAAY,CAAC,cACjEP,KAAA,CAAChB,IAAI,CAAC2L,MAAM,EACRJ,KAAK,CAAEjI,gBAAiB,CACxBkI,QAAQ,CAAGC,CAAC,EAAKlI,mBAAmB,CAACkI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrDqC,QAAQ,CAAEpK,kBAAmB,CAAAuE,QAAA,eAE7BjH,IAAA,WAAQyK,KAAK,CAAC,EAAE,CAAAxD,QAAA,CAAExG,CAAC,CAAC,qBAAqB,CAAC,CAAS,CAAC,CACnD6B,eAAe,CAACyC,GAAG,CAACiI,KAAK,OAAAC,YAAA,oBACtB/M,KAAA,WAA4BuK,KAAK,CAAEuC,KAAK,CAAC/H,OAAQ,CAAAgC,QAAA,EAC5C+F,KAAK,CAACE,UAAU,IAAAD,YAAA,CAAID,KAAK,CAACrH,KAAK,UAAAsH,YAAA,iBAAXA,YAAA,CAAa/G,KAAK,GAAI8G,KAAK,CAAC/H,OAAO,CACvD+H,KAAK,CAACG,cAAc,EAAI,KAAKH,KAAK,CAACG,cAAc,IAAI,GAF7CH,KAAK,CAAC/H,OAGX,CAAC,EACZ,CAAC,EACO,CAAC,CACb3C,eAAe,CAACkF,MAAM,GAAK,CAAC,eACzBxH,IAAA,CAACd,IAAI,CAACkO,IAAI,EAACrD,SAAS,CAAC,YAAY,CAAA9C,QAAA,CAC5BxG,CAAC,CAAC,qBAAqB,CAAC,CAClB,CACd,EACO,CAAC,EACL,CAAC,cACbP,KAAA,CAACb,KAAK,CAACwN,MAAM,EAAA5F,QAAA,eACTjH,IAAA,CAACf,MAAM,EAACiL,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEL,qBAAsB,CAACgD,QAAQ,CAAEpK,kBAAmB,CAAAuE,QAAA,CACpFxG,CAAC,CAAC,QAAQ,CAAC,CACR,CAAC,cACTT,IAAA,CAACf,MAAM,EACHiL,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEN,wBAAyB,CAClCiD,QAAQ,CAAEpK,kBAAkB,EAAI,CAACF,gBAAgB,EAAIF,eAAe,CAACkF,MAAM,GAAK,CAAE,CAAAP,QAAA,CAEjFvE,kBAAkB,cACfxC,KAAA,CAAAE,SAAA,EAAA6G,QAAA,eACIjH,IAAA,SAAM+J,SAAS,CAAC,uCAAuC,CAACgD,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAO,CAAC,CAC/FtM,CAAC,CAAC,YAAY,CAAC,EAClB,CAAC,cAEHP,KAAA,CAAAE,SAAA,EAAA6G,QAAA,eACIjH,IAAA,CAACN,aAAa,EAACqK,SAAS,CAAC,MAAM,CAAE,CAAC,CACjCtJ,CAAC,CAAC,gBAAgB,CAAC,EACtB,CACL,CACG,CAAC,EACC,CAAC,EACZ,CAAC,cAGRP,KAAA,CAACb,KAAK,EAAC+M,IAAI,CAAEpJ,kBAAmB,CAACqJ,MAAM,CAAEnD,mBAAoB,CAACgD,IAAI,CAAC,IAAI,CAAAjF,QAAA,eACnEjH,IAAA,CAACX,KAAK,CAACiN,MAAM,EAACC,WAAW,MAAAtF,QAAA,cACrBjH,IAAA,CAACX,KAAK,CAACmN,KAAK,EAAAvF,QAAA,CAAExG,CAAC,CAAC,YAAY,CAAC,CAAc,CAAC,CAClC,CAAC,cACfP,KAAA,CAACb,KAAK,CAAC2K,IAAI,EAAA/C,QAAA,EACNvD,cAAc,eACX1D,IAAA,CAACV,KAAK,EAAC4K,OAAO,CAAC,QAAQ,CAACH,SAAS,CAAC,MAAM,CAAA9C,QAAA,CACnCvD,cAAc,CACZ,CACV,CACAE,gBAAgB,eACb5D,IAAA,CAACV,KAAK,EAAC4K,OAAO,CAAC,SAAS,CAACH,SAAS,CAAC,MAAM,CAAA9C,QAAA,CACpCrD,gBAAgB,CACd,CACV,cAED1D,KAAA,CAAChB,IAAI,EAAA+H,QAAA,eACD/G,KAAA,CAAChB,IAAI,CAACkL,KAAK,EAACL,SAAS,CAAC,MAAM,CAAA9C,QAAA,eACxBjH,IAAA,CAACd,IAAI,CAACmL,KAAK,EAAApD,QAAA,cAACjH,IAAA,WAAAiH,QAAA,CAASxG,CAAC,CAAC,eAAe,CAAC,CAAS,CAAC,CAAY,CAAC,cAC9DT,IAAA,CAACd,IAAI,CAACoL,OAAO,EACTC,IAAI,CAAC,OAAO,CACZE,KAAK,CAAEvH,cAAe,CACtBwH,QAAQ,CAAGC,CAAC,EAAKxH,iBAAiB,CAACwH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnDD,WAAW,CAAE/J,CAAC,CAAC,qBAAqB,CAAE,CACtCqM,QAAQ,CAAEtJ,gBAAiB,CAC3B6J,QAAQ,MACX,CAAC,cACFrN,IAAA,CAACd,IAAI,CAACkO,IAAI,EAACrD,SAAS,CAAC,YAAY,CAAA9C,QAAA,CAC5BxG,CAAC,CAAC,mBAAmB,CAAC,CAChB,CAAC,EACJ,CAAC,cAEbP,KAAA,CAAChB,IAAI,CAACkL,KAAK,EAACL,SAAS,CAAC,MAAM,CAAA9C,QAAA,eACxBjH,IAAA,CAACd,IAAI,CAACmL,KAAK,EAAApD,QAAA,cAACjH,IAAA,WAAAiH,QAAA,CAASxG,CAAC,CAAC,UAAU,CAAC,CAAS,CAAC,CAAY,CAAC,cACzDT,IAAA,CAACd,IAAI,CAACoL,OAAO,EACTC,IAAI,CAAC,UAAU,CACfE,KAAK,CAAErH,iBAAkB,CACzBsH,QAAQ,CAAGC,CAAC,EAAKtH,oBAAoB,CAACsH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACtDD,WAAW,CAAE/J,CAAC,CAAC,gBAAgB,CAAE,CACjCqM,QAAQ,CAAEtJ,gBAAiB,CAC3B8J,SAAS,CAAE,CAAE,CACbD,QAAQ,MACX,CAAC,cACFrN,IAAA,CAACd,IAAI,CAACkO,IAAI,EAACrD,SAAS,CAAC,YAAY,CAAA9C,QAAA,CAC5BxG,CAAC,CAAC,sBAAsB,CAAC,CACnB,CAAC,EACJ,CAAC,cAEbP,KAAA,CAAChB,IAAI,CAACkL,KAAK,EAACL,SAAS,CAAC,MAAM,CAAA9C,QAAA,eACxBjH,IAAA,CAACd,IAAI,CAACmL,KAAK,EAAApD,QAAA,cAACjH,IAAA,WAAAiH,QAAA,CAASxG,CAAC,CAAC,aAAa,CAAC,CAAS,CAAC,CAAY,CAAC,cAC5DT,IAAA,CAACd,IAAI,CAACoL,OAAO,EACTC,IAAI,CAAC,MAAM,CACXE,KAAK,CAAEnH,mBAAoB,CAC3BoH,QAAQ,CAAGC,CAAC,EAAKpH,sBAAsB,CAACoH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACxDD,WAAW,CAAE/J,CAAC,CAAC,mBAAmB,CAAE,CACpCqM,QAAQ,CAAEtJ,gBAAiB,CAC3B6J,QAAQ,MACX,CAAC,cACFrN,IAAA,CAACd,IAAI,CAACkO,IAAI,EAACrD,SAAS,CAAC,YAAY,CAAA9C,QAAA,CAC5BxG,CAAC,CAAC,kBAAkB,CAAC,CACf,CAAC,EACJ,CAAC,EACX,CAAC,EACC,CAAC,cACbP,KAAA,CAACb,KAAK,CAACwN,MAAM,EAAA5F,QAAA,eACTjH,IAAA,CAACf,MAAM,EAACiL,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEjB,mBAAoB,CAAC4D,QAAQ,CAAEtJ,gBAAiB,CAAAyD,QAAA,CAChFxG,CAAC,CAAC,QAAQ,CAAC,CACR,CAAC,cACTT,IAAA,CAACf,MAAM,EACHiL,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAE9C,sBAAuB,CAChCyF,QAAQ,CAAEtJ,gBAAgB,EAAI,CAACN,cAAc,EAAI,CAACE,iBAAiB,EAAI,CAACE,mBAAoB,CAAA2D,QAAA,CAE3FzD,gBAAgB,cACbtD,KAAA,CAAAE,SAAA,EAAA6G,QAAA,eACIjH,IAAA,SAAM+J,SAAS,CAAC,uCAAuC,CAACgD,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAO,CAAC,CAC/FtM,CAAC,CAAC,UAAU,CAAC,EAChB,CAAC,cAEHP,KAAA,CAAAE,SAAA,EAAA6G,QAAA,eACIjH,IAAA,CAACR,MAAM,EAACuK,SAAS,CAAC,MAAM,CAAE,CAAC,CAC1BtJ,CAAC,CAAC,eAAe,CAAC,EACrB,CACL,CACG,CAAC,EACC,CAAC,EACZ,CAAC,EACD,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}