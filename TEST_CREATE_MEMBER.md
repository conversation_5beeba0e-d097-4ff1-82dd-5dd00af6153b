# 测试新增会员功能

## 当前问题

从错误消息可以看出：
```
Failed to create member: Users record creation error: {"code":"23502","details":"Failing row contains (null, <EMAIL>, null, customer, customer02, ee66690f-d3eb-49d9-afa5-77ef9d0cdaab, 2025-07-12 08:58:54.579993).","hint":null,"message":"null value in column \"id\" of relation \"users\" violates not-null constraint"}
```

问题分析：
1. `id` 字段为 null，违反了非空约束
2. 其他字段都有值：email, role, invite_code, referred_by
3. 这意味着从 Supabase Auth API 获取用户 ID 时出现了问题

## 已添加的调试功能

1. **详细的响应日志记录**：
   - 记录完整的 Auth API 响应
   - 记录解析后的 JSON 数据
   - 记录提取的用户 ID

2. **多种响应格式支持**：
   - `auth_data['user']['id']` - 标准格式
   - `auth_data['id']` - 直接格式  
   - `auth_data['data']['user']['id']` - 嵌套格式

3. **UUID 格式验证**：
   - 验证提取的用户 ID 是否为有效的 UUID 格式

## 测试步骤

1. **检查 WordPress 错误日志**：
   ```bash
   tail -f /path/to/wordpress/wp-content/debug.log
   ```

2. **尝试创建新会员**：
   - 登录为代理商
   - 进入会员列表页面
   - 点击"新增会员"
   - 填写测试数据：
     - Email: <EMAIL>
     - Password: test123456
     - Invite Code: TEST001

3. **查看日志输出**：
   - 查找 "FIL Platform: Auth response body:" 
   - 查找 "FIL Platform: Successfully extracted user ID:"
   - 查找任何错误消息

## 可能的解决方案

### 方案 1：检查 Supabase 配置
确保 WordPress 设置中的 Supabase URL 和 Service Key 配置正确：
- Supabase URL: `https://your-project.supabase.co`
- Service Key: 以 `eyJ` 开头的长字符串

### 方案 2：使用 JavaScript SDK
如果 HTTP API 有问题，可以考虑在后端使用 Supabase JavaScript SDK：

```php
// 需要安装 Node.js 并创建一个小的 Node.js 脚本
// 然后从 PHP 调用该脚本
```

### 方案 3：检查 API 端点
确认使用的 API 端点是否正确：
- 当前使用：`/auth/v1/admin/users`
- 可能需要：`/auth/v1/admin/users/` (带尾部斜杠)

### 方案 4：检查请求头
确认请求头是否完整：
```php
'headers' => [
    'apikey' => $supabase_service_key,
    'Authorization' => 'Bearer ' . $supabase_service_key,
    'Content-Type' => 'application/json'
]
```

## 预期的调试输出

如果一切正常，应该看到类似这样的日志：
```
FIL Platform: Auth response body: {"user":{"id":"12345678-1234-1234-1234-123456789012",...}}
FIL Platform: Found user ID in auth_data[user][id]: 12345678-1234-1234-1234-123456789012
FIL Platform: Successfully extracted user ID: 12345678-1234-1234-1234-123456789012
FIL Platform: Creating users record with data: {"id":"12345678-1234-1234-1234-123456789012",...}
```

## 下一步

1. 运行测试并检查日志
2. 根据日志输出确定问题所在
3. 如果 Auth API 响应格式不同，调整解析逻辑
4. 如果 API 端点有问题，尝试不同的端点格式
