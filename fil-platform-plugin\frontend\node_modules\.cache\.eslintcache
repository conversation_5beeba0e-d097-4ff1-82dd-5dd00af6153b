[{"D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js": "1", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js": "2", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js": "3", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js": "4", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js": "6", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js": "7", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js": "8", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js": "9", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js": "10", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js": "11", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js": "12", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js": "13", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js": "14", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js": "15", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js": "16", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js": "17", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js": "18", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js": "19", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js": "20", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js": "21", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js": "22", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js": "23", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js": "24", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js": "25", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js": "26", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js": "27", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js": "28", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js": "29", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\DebugAgent.js": "30", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Recommend.js": "31", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\NetworkStats.js": "32", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\CapacityRequest.js": "33", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Members.js": "34", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\WithdrawList.js": "35", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\OrderReports.js": "36", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\WalletFlow.js": "37", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Filfox.js": "38", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ChangeLoginPass.js": "39"}, {"size": 408, "mtime": 1751951658003, "results": "40", "hashOfConfig": "41"}, {"size": 20083, "mtime": 1752304809427, "results": "42", "hashOfConfig": "41"}, {"size": 61798, "mtime": 1752310336951, "results": "43", "hashOfConfig": "41"}, {"size": 1212, "mtime": 1751873051207, "results": "44", "hashOfConfig": "41"}, {"size": 3321, "mtime": 1752304822286, "results": "45", "hashOfConfig": "41"}, {"size": 4791, "mtime": 1751960448046, "results": "46", "hashOfConfig": "41"}, {"size": 3994, "mtime": 1752113564124, "results": "47", "hashOfConfig": "41"}, {"size": 4610, "mtime": 1751946228349, "results": "48", "hashOfConfig": "41"}, {"size": 5273, "mtime": 1751960463052, "results": "49", "hashOfConfig": "41"}, {"size": 8634, "mtime": 1752232579878, "results": "50", "hashOfConfig": "41"}, {"size": 4230, "mtime": 1751940026705, "results": "51", "hashOfConfig": "41"}, {"size": 6286, "mtime": 1752231129843, "results": "52", "hashOfConfig": "41"}, {"size": 4495, "mtime": 1751940037703, "results": "53", "hashOfConfig": "41"}, {"size": 3655, "mtime": 1751948557098, "results": "54", "hashOfConfig": "41"}, {"size": 11599, "mtime": 1752224430085, "results": "55", "hashOfConfig": "41"}, {"size": 9624, "mtime": 1752211816825, "results": "56", "hashOfConfig": "41"}, {"size": 3912, "mtime": 1752303969411, "results": "57", "hashOfConfig": "41"}, {"size": 13565, "mtime": 1752120431667, "results": "58", "hashOfConfig": "41"}, {"size": 10902, "mtime": 1752120494989, "results": "59", "hashOfConfig": "41"}, {"size": 4650, "mtime": 1752111918233, "results": "60", "hashOfConfig": "41"}, {"size": 5979, "mtime": 1752111934504, "results": "61", "hashOfConfig": "41"}, {"size": 4355, "mtime": 1752111904775, "results": "62", "hashOfConfig": "41"}, {"size": 6983, "mtime": 1752115841378, "results": "63", "hashOfConfig": "41"}, {"size": 7638, "mtime": 1752115874068, "results": "64", "hashOfConfig": "41"}, {"size": 4798, "mtime": 1752121974201, "results": "65", "hashOfConfig": "41"}, {"size": 3043, "mtime": 1752220020332, "results": "66", "hashOfConfig": "41"}, {"size": 6196, "mtime": 1752114298217, "results": "67", "hashOfConfig": "41"}, {"size": 7110, "mtime": 1752120864192, "results": "68", "hashOfConfig": "41"}, {"size": 7223, "mtime": 1752119937411, "results": "69", "hashOfConfig": "41"}, {"size": 7467, "mtime": 1752122455950, "results": "70", "hashOfConfig": "41"}, {"size": 19472, "mtime": 1752213477302, "results": "71", "hashOfConfig": "41"}, {"size": 3043, "mtime": 1752220085745, "results": "72", "hashOfConfig": "41"}, {"size": 7923, "mtime": 1752196766555, "results": "73", "hashOfConfig": "41"}, {"size": 44153, "mtime": 1752310430208, "results": "74", "hashOfConfig": "41"}, {"size": 12028, "mtime": 1752209647792, "results": "75", "hashOfConfig": "41"}, {"size": 7157, "mtime": 1752211590271, "results": "76", "hashOfConfig": "41"}, {"size": 8839, "mtime": 1752209016116, "results": "77", "hashOfConfig": "41"}, {"size": 14340, "mtime": 1752229967935, "results": "78", "hashOfConfig": "41"}, {"size": 6585, "mtime": 1752231344587, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ji7irk", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js", ["197", "198", "199", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js", ["218"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js", ["219"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js", ["220"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js", ["221"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js", ["222"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js", ["223"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js", ["224"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js", ["225"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js", ["226"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js", ["227"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\DebugAgent.js", ["228", "229"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Recommend.js", ["230", "231", "232", "233"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\NetworkStats.js", ["234"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\CapacityRequest.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Members.js", ["235"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\WithdrawList.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\OrderReports.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\WalletFlow.js", ["236"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Filfox.js", ["237", "238", "239", "240", "241", "242", "243", "244", "245", "246", "247"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ChangeLoginPass.js", [], [], {"ruleId": "248", "severity": 1, "message": "249", "line": 196, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 196, "endColumn": 18}, {"ruleId": "248", "severity": 1, "message": "252", "line": 198, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 198, "endColumn": 23}, {"ruleId": "248", "severity": 1, "message": "253", "line": 315, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 315, "endColumn": 17}, {"ruleId": "248", "severity": 1, "message": "254", "line": 362, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 362, "endColumn": 26}, {"ruleId": "248", "severity": 1, "message": "255", "line": 363, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 363, "endColumn": 27}, {"ruleId": "248", "severity": 1, "message": "256", "line": 378, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 378, "endColumn": 21}, {"ruleId": "248", "severity": 1, "message": "257", "line": 379, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 379, "endColumn": 20}, {"ruleId": "248", "severity": 1, "message": "249", "line": 634, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 634, "endColumn": 18}, {"ruleId": "248", "severity": 1, "message": "252", "line": 636, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 636, "endColumn": 23}, {"ruleId": "248", "severity": 1, "message": "253", "line": 753, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 753, "endColumn": 17}, {"ruleId": "248", "severity": 1, "message": "254", "line": 800, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 800, "endColumn": 26}, {"ruleId": "248", "severity": 1, "message": "255", "line": 801, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 801, "endColumn": 27}, {"ruleId": "248", "severity": 1, "message": "256", "line": 816, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 816, "endColumn": 21}, {"ruleId": "248", "severity": 1, "message": "257", "line": 817, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 817, "endColumn": 20}, {"ruleId": "248", "severity": 1, "message": "249", "line": 1072, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 1072, "endColumn": 18}, {"ruleId": "248", "severity": 1, "message": "252", "line": 1074, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 1074, "endColumn": 23}, {"ruleId": "248", "severity": 1, "message": "253", "line": 1191, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 1191, "endColumn": 17}, {"ruleId": "248", "severity": 1, "message": "254", "line": 1238, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 1238, "endColumn": 26}, {"ruleId": "248", "severity": 1, "message": "255", "line": 1239, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 1239, "endColumn": 27}, {"ruleId": "248", "severity": 1, "message": "256", "line": 1254, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 1254, "endColumn": 21}, {"ruleId": "248", "severity": 1, "message": "257", "line": 1255, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 1255, "endColumn": 20}, {"ruleId": "258", "severity": 1, "message": "259", "line": 49, "column": 8, "nodeType": "260", "endLine": 49, "endColumn": 10, "suggestions": "261"}, {"ruleId": "262", "severity": 1, "message": "263", "line": 3, "column": 55, "nodeType": "264", "messageId": "265", "endLine": 3, "endColumn": 61}, {"ruleId": "262", "severity": 1, "message": "266", "line": 4, "column": 56, "nodeType": "264", "messageId": "265", "endLine": 4, "endColumn": 63}, {"ruleId": "262", "severity": 1, "message": "267", "line": 23, "column": 46, "nodeType": "264", "messageId": "265", "endLine": 23, "endColumn": 57}, {"ruleId": "262", "severity": 1, "message": "268", "line": 3, "column": 44, "nodeType": "264", "messageId": "265", "endLine": 3, "endColumn": 49}, {"ruleId": "262", "severity": 1, "message": "268", "line": 2, "column": 44, "nodeType": "264", "messageId": "265", "endLine": 2, "endColumn": 49}, {"ruleId": "262", "severity": 1, "message": "268", "line": 2, "column": 44, "nodeType": "264", "messageId": "265", "endLine": 2, "endColumn": 49}, {"ruleId": "262", "severity": 1, "message": "268", "line": 2, "column": 44, "nodeType": "264", "messageId": "265", "endLine": 2, "endColumn": 49}, {"ruleId": "262", "severity": 1, "message": "268", "line": 2, "column": 44, "nodeType": "264", "messageId": "265", "endLine": 2, "endColumn": 49}, {"ruleId": "262", "severity": 1, "message": "268", "line": 2, "column": 44, "nodeType": "264", "messageId": "265", "endLine": 2, "endColumn": 49}, {"ruleId": "262", "severity": 1, "message": "269", "line": 7, "column": 13, "nodeType": "264", "messageId": "265", "endLine": 7, "endColumn": 14}, {"ruleId": "262", "severity": 1, "message": "270", "line": 90, "column": 21, "nodeType": "264", "messageId": "265", "endLine": 90, "endColumn": 25}, {"ruleId": "262", "severity": 1, "message": "271", "line": 3, "column": 98, "nodeType": "264", "messageId": "265", "endLine": 3, "endColumn": 106}, {"ruleId": "262", "severity": 1, "message": "272", "line": 25, "column": 17, "nodeType": "264", "messageId": "265", "endLine": 25, "endColumn": 26}, {"ruleId": "262", "severity": 1, "message": "273", "line": 40, "column": 31, "nodeType": "264", "messageId": "265", "endLine": 40, "endColumn": 43}, {"ruleId": "262", "severity": 1, "message": "274", "line": 300, "column": 11, "nodeType": "264", "messageId": "265", "endLine": 300, "endColumn": 23}, {"ruleId": "262", "severity": 1, "message": "268", "line": 2, "column": 44, "nodeType": "264", "messageId": "265", "endLine": 2, "endColumn": 49}, {"ruleId": "262", "severity": 1, "message": "275", "line": 2, "column": 77, "nodeType": "264", "messageId": "265", "endLine": 2, "endColumn": 85}, {"ruleId": "262", "severity": 1, "message": "273", "line": 26, "column": 31, "nodeType": "264", "messageId": "265", "endLine": 26, "endColumn": 43}, {"ruleId": "262", "severity": 1, "message": "276", "line": 4, "column": 10, "nodeType": "264", "messageId": "265", "endLine": 4, "endColumn": 19}, {"ruleId": "262", "severity": 1, "message": "277", "line": 4, "column": 21, "nodeType": "264", "messageId": "265", "endLine": 4, "endColumn": 25}, {"ruleId": "262", "severity": 1, "message": "278", "line": 4, "column": 27, "nodeType": "264", "messageId": "265", "endLine": 4, "endColumn": 32}, {"ruleId": "262", "severity": 1, "message": "279", "line": 4, "column": 34, "nodeType": "264", "messageId": "265", "endLine": 4, "endColumn": 39}, {"ruleId": "262", "severity": 1, "message": "280", "line": 4, "column": 41, "nodeType": "264", "messageId": "265", "endLine": 4, "endColumn": 54}, {"ruleId": "262", "severity": 1, "message": "266", "line": 4, "column": 56, "nodeType": "264", "messageId": "265", "endLine": 4, "endColumn": 63}, {"ruleId": "262", "severity": 1, "message": "281", "line": 4, "column": 65, "nodeType": "264", "messageId": "265", "endLine": 4, "endColumn": 71}, {"ruleId": "262", "severity": 1, "message": "282", "line": 4, "column": 73, "nodeType": "264", "messageId": "265", "endLine": 4, "endColumn": 92}, {"ruleId": "262", "severity": 1, "message": "283", "line": 36, "column": 12, "nodeType": "264", "messageId": "265", "endLine": 36, "endColumn": 26}, {"ruleId": "258", "severity": 1, "message": "284", "line": 136, "column": 8, "nodeType": "260", "endLine": 136, "endColumn": 10, "suggestions": "285"}, {"ruleId": "262", "severity": 1, "message": "286", "line": 151, "column": 11, "nodeType": "264", "messageId": "265", "endLine": 151, "endColumn": 21}, "no-dupe-keys", "Duplicate key 'no_assets'.", "ObjectExpression", "unexpected", "Duplicate key 'my_invite_code'.", "Duplicate key 'batch_id'.", "Duplicate key 'member_management'.", "Duplicate key 'product_management'.", "Duplicate key 'requested_at'.", "Duplicate key 'reviewed_at'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["287"], "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'Tooltip' is defined but never used.", "'memberError' is assigned a value but never used.", "'Badge' is defined but never used.", "'t' is assigned a value but never used.", "'data' is assigned a value but never used.", "'FaSearch' is defined but never used.", "'customers' is assigned a value but never used.", "'agentProfile' is assigned a value but never used.", "'formatUserId' is assigned a value but never used.", "'Dropdown' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Legend' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'historicalData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchNetworkStats'. Either include it or remove the dependency array.", ["288"], "'formatDate' is assigned a value but never used.", {"desc": "289", "fix": "290"}, {"desc": "291", "fix": "292"}, "Update the dependencies array to be: [t]", {"range": "293", "text": "294"}, "Update the dependencies array to be: [fetchNetworkStats]", {"range": "295", "text": "296"}, [1981, 1983], "[t]", [5261, 5263], "[fetchNetworkStats]"]