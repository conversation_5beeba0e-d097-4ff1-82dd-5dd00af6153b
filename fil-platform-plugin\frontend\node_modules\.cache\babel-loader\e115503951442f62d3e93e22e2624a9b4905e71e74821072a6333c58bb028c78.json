{"ast": null, "code": "import React,{useState}from'react';import{<PERSON>,<PERSON><PERSON>,Card,Alert}from'react-bootstrap';import{useTranslation}from'react-i18next';import{getSupabase}from'../supabaseClient';import{useNavigate}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=()=>{const{t}=useTranslation();const[email,setEmail]=useState('');const[password,setPassword]=useState('');const[error,setError]=useState('');const[loading,setLoading]=useState(false);const navigate=useNavigate();const handleSubmit=async e=>{e.preventDefault();setError('');setLoading(true);try{var _user$user_metadata;const supabase=getSupabase();/* ① 登录 */const{error:signError}=await supabase.auth.signInWithPassword({email,password});if(signError)throw signError;/* ② 取当前用户 & role */const{data:{user},error:userErr}=await supabase.auth.getUser();if(userErr)throw userErr;let role=user===null||user===void 0?void 0:(_user$user_metadata=user.user_metadata)===null||_user$user_metadata===void 0?void 0:_user$user_metadata.role;// 如果 user_metadata 里没有 role，就去 public.users 表查询\nif(!role){const{data,error:profileErr}=await supabase.from('users').select('role').eq('id',user.id).single();if(profileErr)throw profileErr;role=data.role;}/* ③ 把 role 存到 localStorage，供前端使用 */localStorage.setItem('user_role',role);/* ④ 触发自定义事件，通知 App.js 更新角色 */window.dispatchEvent(new Event('roleUpdated'));/* ⑤ 根据 role 重定向 */switch(role){case'maker':navigate('/maker',{replace:true});break;case'agent':navigate('/agent',{replace:true});break;default:navigate('/',{replace:true});// customer\n}}catch(err){console.error('Login Error:',err);setError(err.message||t('login_failed'));}setLoading(false);};return/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-center align-items-center vh-100\",children:/*#__PURE__*/_jsx(Card,{style:{width:'400px'},children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{className:\"text-center mb-4\",children:t('login')}),error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error}),/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:t('email_address')}),/*#__PURE__*/_jsx(Form.Control,{type:\"email\",value:email,onChange:e=>setEmail(e.target.value),required:true})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:t('password')}),/*#__PURE__*/_jsx(Form.Control,{type:\"password\",value:password,onChange:e=>setPassword(e.target.value),required:true})]}),/*#__PURE__*/_jsx(Button,{type:\"submit\",className:\"w-100\",disabled:loading,children:loading?t('logging_in'):t('login')})]})]})})});};export default LoginPage;", "map": {"version": 3, "names": ["React", "useState", "Form", "<PERSON><PERSON>", "Card", "<PERSON><PERSON>", "useTranslation", "getSupabase", "useNavigate", "jsx", "_jsx", "jsxs", "_jsxs", "LoginPage", "t", "email", "setEmail", "password", "setPassword", "error", "setError", "loading", "setLoading", "navigate", "handleSubmit", "e", "preventDefault", "_user$user_metadata", "supabase", "signError", "auth", "signInWithPassword", "data", "user", "userErr", "getUser", "role", "user_metadata", "profileErr", "from", "select", "eq", "id", "single", "localStorage", "setItem", "window", "dispatchEvent", "Event", "replace", "err", "console", "message", "className", "children", "style", "width", "Body", "Title", "variant", "onSubmit", "Group", "Label", "Control", "type", "value", "onChange", "target", "required", "disabled"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/LoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON>, Button, Card, Alert } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { getSupabase } from '../supabaseClient';\nimport { useNavigate } from 'react-router-dom';\n\nconst LoginPage = () => {\n  const { t } = useTranslation();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    try {\n      const supabase = getSupabase();\n\n      /* ① 登录 */\n      const { error: signError } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n      if (signError) throw signError;\n\n      /* ② 取当前用户 & role */\n      const {\n        data: { user },\n        error: userErr,\n      } = await supabase.auth.getUser();\n      if (userErr) throw userErr;\n\n      let role = user?.user_metadata?.role;\n\n      // 如果 user_metadata 里没有 role，就去 public.users 表查询\n      if (!role) {\n        const { data, error: profileErr } = await supabase\n          .from('users')\n          .select('role')\n          .eq('id', user.id)\n          .single();\n        if (profileErr) throw profileErr;\n        role = data.role;\n      }\n\n      /* ③ 把 role 存到 localStorage，供前端使用 */\n      localStorage.setItem('user_role', role);\n\n      /* ④ 触发自定义事件，通知 App.js 更新角色 */\n      window.dispatchEvent(new Event('roleUpdated'));\n\n      /* ⑤ 根据 role 重定向 */\n      switch (role) {\n        case 'maker':\n          navigate('/maker', { replace: true });\n          break;\n        case 'agent':\n          navigate('/agent', { replace: true });\n          break;\n        default:\n          navigate('/', { replace: true }); // customer\n      }\n    } catch (err) {\n      console.error('Login Error:', err);\n      setError(err.message || t('login_failed'));\n    }\n\n    setLoading(false);\n  };\n\n  return (\n    <div className=\"d-flex justify-content-center align-items-center vh-100\">\n      <Card style={{ width: '400px' }}>\n        <Card.Body>\n          <Card.Title className=\"text-center mb-4\">{t('login')}</Card.Title>\n          {error && <Alert variant=\"danger\">{error}</Alert>}\n          <Form onSubmit={handleSubmit}>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>{t('email_address')}</Form.Label>\n              <Form.Control\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n              />\n            </Form.Group>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>{t('password')}</Form.Label>\n              <Form.Control\n                type=\"password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n              />\n            </Form.Group>\n            <Button type=\"submit\" className=\"w-100\" disabled={loading}>\n              {loading ? t('logging_in') : t('login')}\n            </Button>\n          </Form>\n        </Card.Body>\n      </Card>\n    </div>\n  );\n};\n\nexport default LoginPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,MAAM,CAAEC,IAAI,CAAEC,KAAK,KAAQ,iBAAiB,CAC3D,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,mBAAmB,CAC/C,OAASC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAEC,CAAE,CAAC,CAAGR,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACS,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACgB,QAAQ,CAAEC,WAAW,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAAsB,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAgB,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBN,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,KAAAK,mBAAA,CACF,KAAM,CAAAC,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAE9B,UACA,KAAM,CAAEY,KAAK,CAAEU,SAAU,CAAC,CAAG,KAAM,CAAAD,QAAQ,CAACE,IAAI,CAACC,kBAAkB,CAAC,CAClEhB,KAAK,CACLE,QACF,CAAC,CAAC,CACF,GAAIY,SAAS,CAAE,KAAM,CAAAA,SAAS,CAE9B,oBACA,KAAM,CACJG,IAAI,CAAE,CAAEC,IAAK,CAAC,CACdd,KAAK,CAAEe,OACT,CAAC,CAAG,KAAM,CAAAN,QAAQ,CAACE,IAAI,CAACK,OAAO,CAAC,CAAC,CACjC,GAAID,OAAO,CAAE,KAAM,CAAAA,OAAO,CAE1B,GAAI,CAAAE,IAAI,CAAGH,IAAI,SAAJA,IAAI,kBAAAN,mBAAA,CAAJM,IAAI,CAAEI,aAAa,UAAAV,mBAAA,iBAAnBA,mBAAA,CAAqBS,IAAI,CAEpC;AACA,GAAI,CAACA,IAAI,CAAE,CACT,KAAM,CAAEJ,IAAI,CAAEb,KAAK,CAAEmB,UAAW,CAAC,CAAG,KAAM,CAAAV,QAAQ,CAC/CW,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,MAAM,CAAC,CACdC,EAAE,CAAC,IAAI,CAAER,IAAI,CAACS,EAAE,CAAC,CACjBC,MAAM,CAAC,CAAC,CACX,GAAIL,UAAU,CAAE,KAAM,CAAAA,UAAU,CAChCF,IAAI,CAAGJ,IAAI,CAACI,IAAI,CAClB,CAEA,oCACAQ,YAAY,CAACC,OAAO,CAAC,WAAW,CAAET,IAAI,CAAC,CAEvC,8BACAU,MAAM,CAACC,aAAa,CAAC,GAAI,CAAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAE9C,mBACA,OAAQZ,IAAI,EACV,IAAK,OAAO,CACVb,QAAQ,CAAC,QAAQ,CAAE,CAAE0B,OAAO,CAAE,IAAK,CAAC,CAAC,CACrC,MACF,IAAK,OAAO,CACV1B,QAAQ,CAAC,QAAQ,CAAE,CAAE0B,OAAO,CAAE,IAAK,CAAC,CAAC,CACrC,MACF,QACE1B,QAAQ,CAAC,GAAG,CAAE,CAAE0B,OAAO,CAAE,IAAK,CAAC,CAAC,CAAE;AACtC,CACF,CAAE,MAAOC,GAAG,CAAE,CACZC,OAAO,CAAChC,KAAK,CAAC,cAAc,CAAE+B,GAAG,CAAC,CAClC9B,QAAQ,CAAC8B,GAAG,CAACE,OAAO,EAAItC,CAAC,CAAC,cAAc,CAAC,CAAC,CAC5C,CAEAQ,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,mBACEZ,IAAA,QAAK2C,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cACtE5C,IAAA,CAACN,IAAI,EAACmD,KAAK,CAAE,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAF,QAAA,cAC9B1C,KAAA,CAACR,IAAI,CAACqD,IAAI,EAAAH,QAAA,eACR5C,IAAA,CAACN,IAAI,CAACsD,KAAK,EAACL,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAExC,CAAC,CAAC,OAAO,CAAC,CAAa,CAAC,CACjEK,KAAK,eAAIT,IAAA,CAACL,KAAK,EAACsD,OAAO,CAAC,QAAQ,CAAAL,QAAA,CAAEnC,KAAK,CAAQ,CAAC,cACjDP,KAAA,CAACV,IAAI,EAAC0D,QAAQ,CAAEpC,YAAa,CAAA8B,QAAA,eAC3B1C,KAAA,CAACV,IAAI,CAAC2D,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1B5C,IAAA,CAACR,IAAI,CAAC4D,KAAK,EAAAR,QAAA,CAAExC,CAAC,CAAC,eAAe,CAAC,CAAa,CAAC,cAC7CJ,IAAA,CAACR,IAAI,CAAC6D,OAAO,EACXC,IAAI,CAAC,OAAO,CACZC,KAAK,CAAElD,KAAM,CACbmD,QAAQ,CAAGzC,CAAC,EAAKT,QAAQ,CAACS,CAAC,CAAC0C,MAAM,CAACF,KAAK,CAAE,CAC1CG,QAAQ,MACT,CAAC,EACQ,CAAC,cACbxD,KAAA,CAACV,IAAI,CAAC2D,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1B5C,IAAA,CAACR,IAAI,CAAC4D,KAAK,EAAAR,QAAA,CAAExC,CAAC,CAAC,UAAU,CAAC,CAAa,CAAC,cACxCJ,IAAA,CAACR,IAAI,CAAC6D,OAAO,EACXC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEhD,QAAS,CAChBiD,QAAQ,CAAGzC,CAAC,EAAKP,WAAW,CAACO,CAAC,CAAC0C,MAAM,CAACF,KAAK,CAAE,CAC7CG,QAAQ,MACT,CAAC,EACQ,CAAC,cACb1D,IAAA,CAACP,MAAM,EAAC6D,IAAI,CAAC,QAAQ,CAACX,SAAS,CAAC,OAAO,CAACgB,QAAQ,CAAEhD,OAAQ,CAAAiC,QAAA,CACvDjC,OAAO,CAAGP,CAAC,CAAC,YAAY,CAAC,CAAGA,CAAC,CAAC,OAAO,CAAC,CACjC,CAAC,EACL,CAAC,EACE,CAAC,CACR,CAAC,CACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}