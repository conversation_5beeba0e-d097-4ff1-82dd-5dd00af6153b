<?php
/**
 * WordPress Cron Debug Script
 * 
 * This script helps debug WordPress cron issues for the FIL Platform plugin
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== WordPress Cron Debug Report ===\n\n";

// Check if WP Cron is disabled
echo "1. WP Cron Status:\n";
if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
    echo "   ❌ WP Cron is DISABLED (DISABLE_WP_CRON = true)\n";
    echo "   You need to set up a system cron job to trigger wp-cron.php\n";
} else {
    echo "   ✅ WP Cron is ENABLED\n";
}
echo "\n";

// Check scheduled events
echo "2. Scheduled Events:\n";
$cron_jobs = _get_cron_array();
$found_filfox = false;

foreach ($cron_jobs as $timestamp => $cron) {
    foreach ($cron as $hook => $dings) {
        if ($hook === 'fil_platform_filfox_scraper') {
            $found_filfox = true;
            echo "   ✅ Found filfox scraper job:\n";
            echo "      Hook: $hook\n";
            echo "      Scheduled: " . date('Y-m-d H:i:s', $timestamp) . " UTC\n";
            echo "      Scheduled JST: " . date('Y-m-d H:i:s', $timestamp + 9 * 3600) . " JST\n";
            echo "      Time until next run: " . human_time_diff($timestamp, time()) . "\n";
            
            if ($timestamp < time()) {
                echo "      ⚠️  WARNING: This job is overdue!\n";
            }
        }
    }
}

if (!$found_filfox) {
    echo "   ❌ No filfox scraper job found in cron schedule\n";
}
echo "\n";

// Check plugin status
echo "3. Plugin Status:\n";
$active_plugins = get_option('active_plugins');
$fil_plugin_active = false;
foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'fil-platform-plugin') !== false) {
        $fil_plugin_active = true;
        echo "   ✅ FIL Platform plugin is active: $plugin\n";
        break;
    }
}

if (!$fil_plugin_active) {
    echo "   ❌ FIL Platform plugin is not active\n";
}
echo "\n";

// Check last run times
echo "4. Scraper History:\n";
$last_run = get_option('fil_platform_last_scrape_run', 'Never');
$last_success = get_option('fil_platform_last_scrape_success', 'Never');
echo "   Last run: $last_run\n";
echo "   Last success: $last_success\n";
echo "\n";

// Check Supabase configuration
echo "5. Supabase Configuration:\n";
$supabase_url = get_option('fil_platform_supabase_url');
$supabase_anon_key = get_option('fil_platform_supabase_anon_key');
$supabase_service_key = get_option('fil_platform_supabase_service_key');

if ($supabase_url && $supabase_url !== 'YOUR_SUPABASE_URL') {
    echo "   ✅ Supabase URL configured\n";
} else {
    echo "   ❌ Supabase URL not configured\n";
}

if ($supabase_service_key && $supabase_service_key !== 'YOUR_SUPABASE_SERVICE_KEY') {
    echo "   ✅ Supabase Service Key configured\n";
} else {
    echo "   ❌ Supabase Service Key not configured\n";
}
echo "\n";

// Test manual scraper
echo "6. Manual Scraper Test:\n";
if (class_exists('FIL_Platform_Filfox_Scraper')) {
    echo "   ✅ Scraper class exists\n";
    
    // Try to create an instance and test
    try {
        $scraper = new FIL_Platform_Filfox_Scraper();
        echo "   ✅ Scraper instance created successfully\n";
        
        // Check if we can call the scraper method
        if (method_exists($scraper, 'run_filfox_scraper')) {
            echo "   ✅ run_filfox_scraper method exists\n";
            echo "   💡 You can test manually by calling the scraper\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Error creating scraper instance: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ❌ Scraper class not found\n";
}
echo "\n";

// Check server time
echo "7. Server Time Information:\n";
echo "   Current server time (UTC): " . gmdate('Y-m-d H:i:s') . "\n";
echo "   Current server time (local): " . date('Y-m-d H:i:s') . "\n";
echo "   Server timezone: " . date_default_timezone_get() . "\n";
echo "   WordPress timezone: " . get_option('timezone_string', 'Not set') . "\n";
echo "\n";

// Recommendations
echo "8. Recommendations:\n";
if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
    echo "   🔧 Set up system cron job: */15 * * * * wget -q -O - " . site_url('wp-cron.php') . " >/dev/null 2>&1\n";
}

if (!$found_filfox) {
    echo "   🔧 Deactivate and reactivate the FIL Platform plugin to reschedule cron job\n";
}

if (!$supabase_service_key || $supabase_service_key === 'YOUR_SUPABASE_SERVICE_KEY') {
    echo "   🔧 Configure Supabase Service Key in WordPress admin settings\n";
}

echo "\n=== End Debug Report ===\n";

/**
 * Helper function to format time differences
 */
function human_time_diff($from, $to = 0) {
    if (empty($to)) {
        $to = time();
    }
    
    $diff = (int) abs($to - $from);
    
    if ($diff < HOUR_IN_SECONDS) {
        $mins = round($diff / MINUTE_IN_SECONDS);
        if ($mins <= 1) {
            $mins = 1;
        }
        $since = sprintf(_n('%s min', '%s mins', $mins), $mins);
    } elseif ($diff < DAY_IN_SECONDS && $diff >= HOUR_IN_SECONDS) {
        $hours = round($diff / HOUR_IN_SECONDS);
        if ($hours <= 1) {
            $hours = 1;
        }
        $since = sprintf(_n('%s hour', '%s hours', $hours), $hours);
    } elseif ($diff < WEEK_IN_SECONDS && $diff >= DAY_IN_SECONDS) {
        $days = round($diff / DAY_IN_SECONDS);
        if ($days <= 1) {
            $days = 1;
        }
        $since = sprintf(_n('%s day', '%s days', $days), $days);
    }
    
    return $since;
}
?>
