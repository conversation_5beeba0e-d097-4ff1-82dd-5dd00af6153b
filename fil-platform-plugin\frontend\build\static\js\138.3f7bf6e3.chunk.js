"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[138],{9138:(e,r,s)=>{s.r(r),s.d(r,{default:()=>v});var n=s(5043),t=s(4063),i=s(3519),a=s(1072),d=s(8602),l=s(8628),c=s(4282),o=s(3722),u=s(7994),h=s(4196),m=s(3083),x=s(1719),j=s(3204),_=s(4312),g=s(4117),p=s(579);const v=()=>{var e,r,s;const{t:v}=(0,g.Bd)(),[b,f]=(0,n.useState)([]),[A,w]=(0,n.useState)(!0),[y,k]=(0,n.useState)(""),[C,N]=(0,n.useState)(""),[S,E]=(0,n.useState)(""),[F,L]=(0,n.useState)(""),[B,D]=(0,n.useState)([]),[H,G]=(0,n.useState)(!1),[z,R]=(0,n.useState)(null),[T,q]=(0,n.useState)(!1),[I,M]=(0,n.useState)(""),[K,O]=(0,n.useState)(""),[U,Q]=(0,n.useState)(!1),[Y,$]=(0,n.useState)(null),[J,P]=(0,n.useState)([]),[V,W]=(0,n.useState)(""),[X,Z]=(0,n.useState)(!1),[ee,re]=(0,n.useState)(""),[se,ne]=(0,n.useState)("");(0,n.useEffect)(()=>{(async()=>{const e=(0,_.b)();if(!e)return;w(!0);const{data:{user:r}}=await e.auth.getUser();if(!r)return void w(!1);const{data:s,error:n}=await e.from("customer_profiles").select("user_id, real_name, id_number, id_img_front, id_img_back, verify_status").eq("agent_id",r.id).order("created_at",{ascending:!1});if(n||!s)return console.error("Error fetching customer_profiles:",n),void w(!1);s.map(e=>e.user_id).filter(Boolean);const{data:t,error:i}=await e.from("users").select("id, email, created_at");i&&console.error("Error fetching users:",i);const a=new Map((t||[]).map(e=>[e.id,e])),d=s.map(e=>({...e,users:a.get(e.user_id)||{}}));f(d),w(!1)})()},[]),(0,n.useEffect)(()=>{let e=b;y&&(e=e.filter(e=>{var r,s,n;return(null===(r=e.users)||void 0===r||null===(s=r.email)||void 0===s?void 0:s.toLowerCase().includes(y.toLowerCase()))||(null===(n=e.real_name)||void 0===n?void 0:n.toLowerCase().includes(y.toLowerCase()))})),C&&(e=e.filter(e=>e.verify_status===C)),S&&(e=e.filter(e=>{var r;return new Date(null===(r=e.users)||void 0===r?void 0:r.created_at)>=new Date(S)})),F&&(e=e.filter(e=>{var r;return new Date(null===(r=e.users)||void 0===r?void 0:r.created_at)<=new Date(F)})),D(e)},[b,y,C,S,F]);const te=e=>{switch(e){case"approved":return(0,p.jsx)(t.A,{bg:"success",children:v("approved")});case"pending":return(0,p.jsx)(t.A,{bg:"warning",children:v("pending_review")});case"rejected":return(0,p.jsx)(t.A,{bg:"danger",children:v("rejected")});case"under_review":return(0,p.jsx)(t.A,{bg:"info",children:v("under_review")});default:return(0,p.jsx)(t.A,{bg:"secondary",children:e||v("not_submitted")})}},ie=async e=>{if(z){q(!0),M(""),O("");try{const r=(0,_.b)();if(!r)throw new Error("Database connection failed");const{data:s,error:n}=await r.from("customer_profiles").update({verify_status:e}).eq("user_id",z.user_id).select();if(n)throw console.error("Database error:",n),n;if(!s||0===s.length){const{data:e,error:s}=await r.from("customer_profiles").select("*").eq("user_id",z.user_id);if(s)throw console.error("Error checking existing record:",s),s;if(!e||0===e.length)throw new Error("Customer profile not found")}f(r=>r.map(r=>r.user_id===z.user_id?{...r,verify_status:e}:r)),R(r=>({...r,verify_status:e})),O(v("approved"===e?"kyc_approved_success":"kyc_rejected_success")),setTimeout(()=>{G(!1),R(null)},1500)}catch(r){console.error("Error updating KYC status:",r),M(r.message||v("kyc_update_error"))}finally{q(!1)}}},ae=()=>{G(!1),R(null),M(""),O("")},de=async e=>{$(e),re(""),ne(""),W(""),await(async()=>{const e=(0,_.b)();if(e)try{const{data:{user:r}}=await e.auth.getUser();if(!r)return;const{data:s,error:n}=await e.from("agent_profiles").select("\n                    user_id,\n                    brand_name,\n                    commission_pct,\n                    users:user_id (\n                        email\n                    )\n                ").neq("user_id",r.id);if(n)return void console.error("Error fetching agents:",n);P(s||[])}catch(r){console.error("Error in fetchAvailableAgents:",r)}})(),Q(!0)},le=()=>{Q(!1),$(null),W(""),re(""),ne("")};return A?(0,p.jsx)("div",{children:v("loading_members")}):(0,p.jsxs)(i.A,{children:[(0,p.jsx)("h2",{className:"mb-4",children:v("member_list")}),(0,p.jsx)(a.A,{className:"mb-4",children:(0,p.jsx)(d.A,{children:(0,p.jsx)(l.A,{children:(0,p.jsx)(l.A.Body,{children:(0,p.jsxs)(a.A,{className:"align-items-end",children:[(0,p.jsx)(d.A,{md:2,children:(0,p.jsxs)(c.A,{variant:"primary",onClick:()=>{alert(v("add_member_coming_soon"))},className:"mb-2",children:[(0,p.jsx)(j.OiG,{className:"me-1"}),v("add_member")]})}),(0,p.jsx)(d.A,{md:3,children:(0,p.jsxs)(o.A.Group,{children:[(0,p.jsx)(o.A.Label,{children:v("search_username")}),(0,p.jsx)(u.A,{children:(0,p.jsx)(o.A.Control,{type:"text",placeholder:v("please_enter_username"),value:y,onChange:e=>k(e.target.value)})})]})}),(0,p.jsx)(d.A,{md:2,children:(0,p.jsxs)(o.A.Group,{children:[(0,p.jsx)(o.A.Label,{children:v("status_filter")}),(0,p.jsxs)(o.A.Select,{value:C,onChange:e=>N(e.target.value),children:[(0,p.jsx)("option",{value:"",children:v("please_select_status")}),(0,p.jsx)("option",{value:"pending",children:v("pending_review")}),(0,p.jsx)("option",{value:"approved",children:v("approved")}),(0,p.jsx)("option",{value:"rejected",children:v("rejected")}),(0,p.jsx)("option",{value:"under_review",children:v("under_review")})]})]})}),(0,p.jsx)(d.A,{md:2,children:(0,p.jsxs)(o.A.Group,{children:[(0,p.jsx)(o.A.Label,{children:v("start_date")}),(0,p.jsx)(o.A.Control,{type:"date",value:S,onChange:e=>E(e.target.value)})]})}),(0,p.jsx)(d.A,{md:2,children:(0,p.jsxs)(o.A.Group,{children:[(0,p.jsx)(o.A.Label,{children:v("end_date")}),(0,p.jsx)(o.A.Control,{type:"date",value:F,onChange:e=>L(e.target.value)})]})}),(0,p.jsx)(d.A,{md:1,children:(0,p.jsx)(c.A,{variant:"outline-primary",onClick:()=>{console.log("Search triggered")},className:"mb-2",children:(0,p.jsx)(j.KSO,{})})})]})})})})}),(0,p.jsx)(a.A,{children:(0,p.jsx)(d.A,{children:(0,p.jsx)(l.A,{children:(0,p.jsx)(l.A.Body,{children:(0,p.jsxs)(h.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,p.jsx)("thead",{children:(0,p.jsxs)("tr",{children:[(0,p.jsx)("th",{children:v("username")}),(0,p.jsx)("th",{children:v("real_name")}),(0,p.jsx)("th",{children:v("id_number")}),(0,p.jsx)("th",{children:v("id_front_image")}),(0,p.jsx)("th",{children:v("id_back_image")}),(0,p.jsx)("th",{children:v("status")}),(0,p.jsx)("th",{children:v("registration_time")}),(0,p.jsx)("th",{children:v("actions")})]})}),(0,p.jsx)("tbody",{children:0===B.length?(0,p.jsx)("tr",{children:(0,p.jsx)("td",{colSpan:"9",className:"text-center",children:v("no_members_found")})}):B.map(e=>{var r,s;return(0,p.jsxs)("tr",{children:[(0,p.jsx)("td",{children:(null===(r=e.users)||void 0===r?void 0:r.email)||"-"}),(0,p.jsx)("td",{children:e.real_name||"-"}),(0,p.jsx)("td",{children:e.id_number||"-"}),(0,p.jsx)("td",{children:e.id_img_front?(0,p.jsx)("img",{src:e.id_img_front,alt:"ID Front",style:{width:"60px",height:"40px",objectFit:"cover",borderRadius:"4px",cursor:"pointer"},onClick:()=>window.open(e.id_img_front,"_blank")}):(0,p.jsx)("span",{className:"text-muted",children:"-"})}),(0,p.jsx)("td",{children:e.id_img_back?(0,p.jsx)("img",{src:e.id_img_back,alt:"ID Back",style:{width:"60px",height:"40px",objectFit:"cover",borderRadius:"4px",cursor:"pointer"},onClick:()=>window.open(e.id_img_back,"_blank")}):(0,p.jsx)("span",{className:"text-muted",children:"-"})}),(0,p.jsx)("td",{children:te(e.verify_status)}),(0,p.jsx)("td",{children:null!==(s=e.users)&&void 0!==s&&s.created_at?new Date(e.users.created_at).toLocaleString():"-"}),(0,p.jsx)("td",{children:(0,p.jsxs)("div",{className:"d-flex justify-content-between",children:[(0,p.jsx)(c.A,{size:"sm",variant:"outline-primary",onClick:()=>(e=>{R(e),G(!0),M(""),O("")})(e),title:v("kyc_review"),children:(0,p.jsx)(j.BAG,{})}),(0,p.jsx)(c.A,{size:"sm",variant:"outline-warning",onClick:()=>de(e),title:v("change_agent"),children:(0,p.jsx)(j.yk7,{})})]})})]},e.user_id)})})]})})})})}),(0,p.jsxs)(m.A,{show:H,onHide:ae,size:"lg",children:[(0,p.jsx)(m.A.Header,{closeButton:!0,className:"custom-modal-header",children:(0,p.jsx)(m.A.Title,{children:v("kyc_review")})}),(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n                    .custom-modal-header .btn-close {\n                    background: none !important;\n                    border: none !important;\n                    opacity: 0.8 !important;\n                    background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 16 16'%3E%3Cpath d='M2.146 2.146a.5.5 0 0 1 .708 0L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E\") !important;\n                    background-size: 1em 1em !important;\n                    background-repeat: no-repeat !important;\n                    background-position: center !important;\n                    }\n                    .custom-modal-header .btn-close:hover {\n                    opacity: 1 !important;\n                    background-color: rgba(255, 255, 255, 0.1) !important;\n                    }\n                "}}),(0,p.jsx)(m.A.Body,{children:z&&(0,p.jsxs)(p.Fragment,{children:[I&&(0,p.jsx)(x.A,{variant:"danger",className:"mb-3",children:I}),K&&(0,p.jsx)(x.A,{variant:"success",className:"mb-3",children:K}),(0,p.jsxs)(a.A,{children:[(0,p.jsx)(d.A,{md:6,children:(0,p.jsxs)(l.A,{className:"mb-3",children:[(0,p.jsx)(l.A.Header,{children:(0,p.jsx)("strong",{children:v("customer_info")})}),(0,p.jsxs)(l.A.Body,{children:[(0,p.jsxs)("p",{children:[(0,p.jsxs)("strong",{children:[v("username"),":"]})," ",(null===(e=z.users)||void 0===e?void 0:e.email)||"-"]}),(0,p.jsxs)("p",{children:[(0,p.jsxs)("strong",{children:[v("real_name"),":"]})," ",z.real_name||"-"]}),(0,p.jsxs)("p",{children:[(0,p.jsxs)("strong",{children:[v("id_number"),":"]})," ",z.id_number||"-"]}),(0,p.jsxs)("p",{children:[(0,p.jsxs)("strong",{children:[v("current_status"),":"]})," ",te(z.verify_status)]}),(0,p.jsxs)("p",{children:[(0,p.jsxs)("strong",{children:[v("registration_time"),":"]})," ",null!==(r=z.users)&&void 0!==r&&r.created_at?new Date(z.users.created_at).toLocaleString():"-"]})]})]})}),(0,p.jsx)(d.A,{md:6,children:(0,p.jsxs)(l.A,{className:"mb-3",children:[(0,p.jsx)(l.A.Header,{children:(0,p.jsx)("strong",{children:v("id_documents")})}),(0,p.jsxs)(l.A.Body,{children:[(0,p.jsxs)("div",{className:"mb-3",children:[(0,p.jsxs)("strong",{children:[v("id_front_image"),":"]}),(0,p.jsx)("div",{className:"mt-2",children:z.id_img_front?(0,p.jsx)("img",{src:z.id_img_front,alt:"ID Front",style:{width:"100%",maxHeight:"150px",objectFit:"contain",borderRadius:"4px",cursor:"pointer",border:"1px solid #dee2e6"},onClick:()=>window.open(z.id_img_front,"_blank")}):(0,p.jsx)("div",{className:"text-muted text-center py-3",style:{border:"1px dashed #dee2e6",borderRadius:"4px"},children:v("no_image_uploaded")})})]}),(0,p.jsxs)("div",{className:"mb-3",children:[(0,p.jsxs)("strong",{children:[v("id_back_image"),":"]}),(0,p.jsx)("div",{className:"mt-2",children:z.id_img_back?(0,p.jsx)("img",{src:z.id_img_back,alt:"ID Back",style:{width:"100%",maxHeight:"150px",objectFit:"contain",borderRadius:"4px",cursor:"pointer",border:"1px solid #dee2e6"},onClick:()=>window.open(z.id_img_back,"_blank")}):(0,p.jsx)("div",{className:"text-muted text-center py-3",style:{border:"1px dashed #dee2e6",borderRadius:"4px"},children:v("no_image_uploaded")})})]})]})]})})]})]})}),(0,p.jsxs)(m.A.Footer,{children:[(0,p.jsx)(c.A,{variant:"secondary",onClick:ae,disabled:T,children:v("cancel")}),(0,p.jsx)(c.A,{variant:"danger",onClick:()=>ie("rejected"),disabled:T||"rejected"===(null===z||void 0===z?void 0:z.verify_status),children:T?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),v("processing")]}):(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(j.QCr,{className:"me-1"}),v("reject")]})}),(0,p.jsx)(c.A,{variant:"success",onClick:()=>ie("approved"),disabled:T||"approved"===(null===z||void 0===z?void 0:z.verify_status),children:T?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),v("processing")]}):(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(j.CMH,{className:"me-1"}),v("approve")]})})]})]}),(0,p.jsxs)(m.A,{show:U,onHide:le,size:"md",children:[(0,p.jsx)(m.A.Header,{closeButton:!0,children:(0,p.jsx)(m.A.Title,{children:v("change_agent")})}),(0,p.jsxs)(m.A.Body,{children:[ee&&(0,p.jsx)(x.A,{variant:"danger",className:"mb-3",children:ee}),se&&(0,p.jsx)(x.A,{variant:"success",className:"mb-3",children:se}),Y&&(0,p.jsx)("div",{className:"mb-4",children:(0,p.jsxs)(l.A,{children:[(0,p.jsx)(l.A.Header,{children:(0,p.jsx)("strong",{children:v("customer_info")})}),(0,p.jsxs)(l.A.Body,{children:[(0,p.jsxs)("p",{children:[(0,p.jsxs)("strong",{children:[v("username"),":"]})," ",(null===(s=Y.users)||void 0===s?void 0:s.email)||"-"]}),(0,p.jsxs)("p",{children:[(0,p.jsxs)("strong",{children:[v("real_name"),":"]})," ",Y.real_name||"-"]}),(0,p.jsxs)("p",{children:[(0,p.jsxs)("strong",{children:[v("current_status"),":"]})," ",te(Y.verify_status)]})]})]})}),(0,p.jsxs)(o.A.Group,{className:"mb-3",children:[(0,p.jsx)(o.A.Label,{children:(0,p.jsx)("strong",{children:v("select_new_agent")})}),(0,p.jsxs)(o.A.Select,{value:V,onChange:e=>W(e.target.value),disabled:X,children:[(0,p.jsx)("option",{value:"",children:v("please_select_agent")}),J.map(e=>{var r;return(0,p.jsxs)("option",{value:e.user_id,children:[e.brand_name||(null===(r=e.users)||void 0===r?void 0:r.email)||e.user_id,e.commission_pct&&` (${e.commission_pct}%)`]},e.user_id)})]}),0===J.length&&(0,p.jsx)(o.A.Text,{className:"text-muted",children:v("no_available_agents")})]})]}),(0,p.jsxs)(m.A.Footer,{children:[(0,p.jsx)(c.A,{variant:"secondary",onClick:le,disabled:X,children:v("cancel")}),(0,p.jsx)(c.A,{variant:"primary",onClick:async()=>{if(Y&&V){Z(!0),re(""),ne("");try{const e=(0,_.b)();if(!e)throw new Error("Database connection failed");const{data:r,error:s}=await e.from("customer_profiles").update({agent_id:V}).eq("user_id",Y.user_id).select();if(s)throw console.error("Database error:",s),s;if(!r||0===r.length)throw new Error("Failed to update agent assignment");f(e=>e.filter(e=>e.user_id!==Y.user_id)),ne(v("agent_changed_successfully")),setTimeout(()=>{Q(!1),$(null),W("")},1500)}catch(e){console.error("Error changing agent:",e),re(e.message||v("agent_change_error"))}finally{Z(!1)}}else re(v("please_select_agent"))},disabled:X||!V||0===J.length,children:X?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),v("processing")]}):(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(j.yk7,{className:"me-1"}),v("confirm_change")]})})]})]})]})}}}]);
//# sourceMappingURL=138.3f7bf6e3.chunk.js.map