"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[738],{1719:(e,s,a)=>{a.d(s,{A:()=>y});var t=a(8139),r=a.n(t),i=a(5043),n=a(1969),l=a(6618),d=a(7852),o=a(4488),c=a(579);const u=(0,o.A)("h4");u.displayName="DivStyledAsH4";const m=i.forwardRef((e,s)=>{let{className:a,bsPrefix:t,as:i=u,...n}=e;return t=(0,d.oU)(t,"alert-heading"),(0,c.jsx)(i,{ref:s,className:r()(a,t),...n})});m.displayName="AlertHeading";const p=m;var f=a(7071);const _=i.forwardRef((e,s)=>{let{className:a,bsPrefix:t,as:i=f.A,...n}=e;return t=(0,d.oU)(t,"alert-link"),(0,c.jsx)(i,{ref:s,className:r()(a,t),...n})});_.displayName="AlertLink";const g=_;var h=a(8072),b=a(5632);const x=i.forwardRef((e,s)=>{const{bsPrefix:a,show:t=!0,closeLabel:i="Close alert",closeVariant:o,className:u,children:m,variant:p="primary",onClose:f,dismissible:_,transition:g=h.A,...x}=(0,n.Zw)(e,{show:"onClose"}),y=(0,d.oU)(a,"alert"),j=(0,l.A)(e=>{f&&f(!1,e)}),v=!0===g?h.A:g,A=(0,c.jsxs)("div",{role:"alert",...v?void 0:x,ref:s,className:r()(u,y,p&&`${y}-${p}`,_&&`${y}-dismissible`),children:[_&&(0,c.jsx)(b.A,{onClick:j,"aria-label":i,variant:o}),m]});return v?(0,c.jsx)(v,{unmountOnExit:!0,...x,ref:void 0,in:t,children:A}):t?A:null});x.displayName="Alert";const y=Object.assign(x,{Link:g,Heading:p})},2738:(e,s,a)=>{a.r(s),a.d(s,{default:()=>m});var t=a(5043),r=a(3519),i=a(8628),n=a(1719),l=a(3722),d=a(4282),o=a(4312),c=a(4117),u=a(579);const m=()=>{const{t:e}=(0,c.Bd)(),[s,a]=(0,t.useState)(""),[m,p]=(0,t.useState)(""),[f,_]=(0,t.useState)(null),[g,h]=(0,t.useState)(null),[b,x]=(0,t.useState)(null),[y,j]=(0,t.useState)(!0),[v,A]=(0,t.useState)(!1),[w,k]=(0,t.useState)({type:"",text:""});(0,t.useEffect)(()=>{(async()=>{const s=(0,o.b)();if(!s)return;const{data:{user:t}}=await s.auth.getUser();if(!t)return void j(!1);const{data:r,error:i}=await s.from("customer_profiles").select("real_name, id_number, id_img_front, id_img_back, verify_status").eq("user_id",t.id).single();i&&"PGRST116"!==i.code?(console.error("Error fetching KYC status:",i),k({type:"danger",text:e("failed_to_load_kyc_status")})):r&&(a(r.real_name||""),p(r.id_number||""),_(r.id_img_front||null),h(r.id_img_back||null),x(r.verify_status||null)),j(!1)})()},[]);const N=(e,s)=>{e.target.files&&e.target.files[0]&&s(e.target.files[0])};return y?(0,u.jsx)("div",{children:e("loading_kyc_status")}):(0,u.jsxs)(r.A,{children:[(0,u.jsx)("h2",{className:"mb-4",children:e("kyc_verification")}),(0,u.jsx)(i.A,{children:(0,u.jsxs)(i.A.Body,{children:[w.text&&(0,u.jsx)(n.A,{variant:w.type,children:w.text}),"approved"===b&&(0,u.jsx)(n.A,{variant:"success",children:e("kyc_approved")}),"pending"===b&&(0,u.jsx)(n.A,{variant:"info",children:e("kyc_pending_review")}),"rejected"===b&&(0,u.jsx)(n.A,{variant:"danger",children:e("kyc_rejected")}),null===b&&(0,u.jsx)(n.A,{variant:"warning",children:e("kyc_not_submitted")}),(0,u.jsxs)(l.A,{onSubmit:async a=>{a.preventDefault(),A(!0),k({type:"",text:""});try{const a=(0,o.b)();if(!a)throw new Error("Supabase not initialized");const{data:{user:t}}=await a.auth.getUser();if(!t)throw new Error("User not authenticated");const r=new FormData;r.append("real_name",s),r.append("id_number",m),r.append("user_id",t.id),f instanceof File&&r.append("id_img_front",f),g instanceof File&&r.append("id_img_back",g);const i=await fetch(`${window.wpData.apiUrl}submit-kyc`,{method:"POST",body:r,headers:{"X-WP-Nonce":window.wpData.nonce}}),n=await i.json();if(!n.success)throw new Error(n.message||"Failed to submit KYC");n.files&&n.files.id_img_front&&_(n.files.id_img_front.url),n.files&&n.files.id_img_back&&h(n.files.id_img_back.url),k({type:"success",text:e("kyc_submit_success")}),x("pending"),n.errors&&n.errors.length>0&&console.warn("Upload warnings:",n.errors)}catch(t){console.error("KYC submission error:",t),k({type:"danger",text:e("failed_to_submit_kyc")+": "+t.message})}A(!1)},children:[(0,u.jsxs)(l.A.Group,{className:"mb-3",children:[(0,u.jsx)(l.A.Label,{children:e("real_name")}),(0,u.jsx)(l.A.Control,{type:"text",value:s,onChange:e=>a(e.target.value),required:!0,disabled:"pending"===b||"approved"===b})]}),(0,u.jsxs)(l.A.Group,{className:"mb-3",children:[(0,u.jsx)(l.A.Label,{children:e("id_number")}),(0,u.jsx)(l.A.Control,{type:"text",value:m,onChange:e=>p(e.target.value),required:!0,disabled:"pending"===b||"approved"===b})]}),(0,u.jsxs)(l.A.Group,{className:"mb-3",children:[(0,u.jsx)(l.A.Label,{children:e("id_front")}),(0,u.jsx)(l.A.Control,{type:"file",onChange:e=>N(e,_),accept:"image/*",disabled:"pending"===b||"approved"===b}),f&&"string"===typeof f&&(0,u.jsx)("img",{src:f,alt:"ID Front",className:"img-thumbnail mt-2",style:{maxWidth:"200px"}})]}),(0,u.jsxs)(l.A.Group,{className:"mb-3",children:[(0,u.jsx)(l.A.Label,{children:e("id_back")}),(0,u.jsx)(l.A.Control,{type:"file",onChange:e=>N(e,h),accept:"image/*",disabled:"pending"===b||"approved"===b}),g&&"string"===typeof g&&(0,u.jsx)("img",{src:g,alt:"ID Back",className:"img-thumbnail mt-2",style:{maxWidth:"200px"}})]}),(0,u.jsx)(d.A,{variant:"primary",type:"submit",disabled:v||"pending"===b||"approved"===b,children:e(v?"submitting":"submit_review")})]})]})})]})}}}]);
//# sourceMappingURL=738.16c574d4.chunk.js.map