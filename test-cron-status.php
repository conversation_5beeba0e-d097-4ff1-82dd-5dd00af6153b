<?php
/**
 * Simple Cron Status Test
 * 
 * Run this script to check the current status of the filfox scraper cron job
 */

// This script should be run from the WordPress root directory
// or you need to adjust the path to wp-config.php

if (file_exists('wp-config.php')) {
    require_once('wp-config.php');
} elseif (file_exists('../wp-config.php')) {
    require_once('../wp-config.php');
} else {
    die("Cannot find wp-config.php. Please run this script from your WordPress root directory.\n");
}

require_once('wp-load.php');

echo "=== FIL Platform Cron Status Check ===\n";
echo "Current time: " . date('Y-m-d H:i:s') . " (Server time)\n";
echo "Current UTC time: " . gmdate('Y-m-d H:i:s') . "\n";
echo "Current JST time: " . gmdate('Y-m-d H:i:s', time() + 9 * 3600) . "\n\n";

// Check if WP Cron is disabled
if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
    echo "❌ WP Cron is DISABLED\n";
    echo "You need to set up a system cron job to run: wget -q -O - " . site_url('wp-cron.php') . "\n\n";
} else {
    echo "✅ WP Cron is enabled\n\n";
}

// Check for scheduled filfox scraper
$next_scheduled = wp_next_scheduled('fil_platform_filfox_scraper');
if ($next_scheduled) {
    echo "✅ Filfox scraper is scheduled\n";
    echo "Next run (UTC): " . date('Y-m-d H:i:s', $next_scheduled) . "\n";
    echo "Next run (JST): " . date('Y-m-d H:i:s', $next_scheduled + 9 * 3600) . "\n";
    
    $time_diff = $next_scheduled - time();
    if ($time_diff > 0) {
        $hours = floor($time_diff / 3600);
        $minutes = floor(($time_diff % 3600) / 60);
        echo "Time until next run: {$hours}h {$minutes}m\n";
    } else {
        echo "⚠️ WARNING: Scheduled time has passed! Job may be stuck.\n";
    }
} else {
    echo "❌ Filfox scraper is NOT scheduled\n";
}

echo "\n";

// Check last run times
$last_run = get_option('fil_platform_last_scrape_run', 'Never');
$last_success = get_option('fil_platform_last_scrape_success', 'Never');
echo "Last run attempt: $last_run\n";
echo "Last successful run: $last_success\n\n";

// Check plugin status
$active_plugins = get_option('active_plugins', []);
$plugin_active = false;
foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'fil-platform-plugin') !== false) {
        $plugin_active = true;
        echo "✅ FIL Platform plugin is active\n";
        break;
    }
}

if (!$plugin_active) {
    echo "❌ FIL Platform plugin is NOT active\n";
}

// Check Supabase config
$supabase_url = get_option('fil_platform_supabase_url');
$supabase_service_key = get_option('fil_platform_supabase_service_key');

echo "\nSupabase Configuration:\n";
if ($supabase_url && $supabase_url !== 'YOUR_SUPABASE_URL') {
    echo "✅ Supabase URL is configured\n";
} else {
    echo "❌ Supabase URL is not configured\n";
}

if ($supabase_service_key && $supabase_service_key !== 'YOUR_SUPABASE_SERVICE_KEY') {
    echo "✅ Supabase Service Key is configured\n";
} else {
    echo "❌ Supabase Service Key is not configured\n";
}

echo "\n=== Recommendations ===\n";

if (!$next_scheduled) {
    echo "🔧 Try deactivating and reactivating the FIL Platform plugin\n";
    echo "🔧 Or visit the admin page and click 'Reschedule Cron Job'\n";
}

if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
    echo "🔧 Set up system cron: */15 * * * * wget -q -O - " . site_url('wp-cron.php') . " >/dev/null 2>&1\n";
}

if (!$supabase_service_key || $supabase_service_key === 'YOUR_SUPABASE_SERVICE_KEY') {
    echo "🔧 Configure Supabase Service Key in WordPress admin\n";
}

echo "\n=== Manual Test ===\n";
echo "To manually test the scraper, you can:\n";
echo "1. Go to WordPress Admin > FIL Platform Settings\n";
echo "2. Click 'Run Manual Scrape'\n";
echo "3. Or run: wp cron event run fil_platform_filfox_scraper (if WP-CLI is available)\n";

echo "\n=== End Status Check ===\n";
?>
