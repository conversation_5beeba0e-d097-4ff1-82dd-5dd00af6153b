{"ast": null, "code": "import React,{Suspense,useEffect,useState}from'react';import{initSupabase}from'./supabaseClient';import{HashRouter,Routes,Route,Link,Navigate,useLocation}from'react-router-dom';import{Container,Navbar,Nav,NavDropdown,Button}from'react-bootstrap';import{useTranslation}from'react-i18next';import{FaSignOutAlt,FaTachometerAlt,FaHardHat,FaUser,FaGlobe,FaCoins,FaChartBar,FaFileInvoiceDollar,FaUsers,FaShoppingBag,FaYenSign,FaBuffer}from'react-icons/fa';// Lazy load components for better performance\nimport{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=/*#__PURE__*/React.lazy(()=>import('./pages/LoginPage'));const CustomerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/customer/Dashboard'));const ProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/ProductListPage'));const OrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/OrderListPage'));const MyAccountPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyAccountPage'));const MyGainsPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyGainsPage'));const KycPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/KycPage'));const RecommendPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/RecommendPage'));const ChangeLoginPass=/*#__PURE__*/React.lazy(()=>import('./pages/customer/ChangeLoginPass'));const AgentDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Dashboard'));const Members=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Members'));const Recommend=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Recommend'));const AgentProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/agent/AgentProductListPage'));const DebugAgent=/*#__PURE__*/React.lazy(()=>import('./pages/agent/DebugAgent'));const MakerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/maker/Dashboard'));const MakerProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerProductListPage'));const MakerOrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerOrderListPage'));const MakerFacilityListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerFacilities'));const MakerMinerListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerMiners'));const MinerEarnings=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MinerEarnings'));const MinerSnapshots=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MinerSnapshots'));const Transactions=/*#__PURE__*/React.lazy(()=>import('./pages/maker/Transactions'));const CoinBatches=/*#__PURE__*/React.lazy(()=>import('./pages/maker/CoinBatches'));const NetworkStats=/*#__PURE__*/React.lazy(()=>import('./pages/maker/NetworkStats'));const CustomerAssets=/*#__PURE__*/React.lazy(()=>import('./pages/maker/CustomerAssets'));const OrderReports=/*#__PURE__*/React.lazy(()=>import('./pages/maker/OrderReports'));const OrderDistributions=/*#__PURE__*/React.lazy(()=>import('./pages/maker/OrderDistributions'));const CapacityRequest=/*#__PURE__*/React.lazy(()=>import('./pages/maker/CapacityRequest'));const ManualDeposits=/*#__PURE__*/React.lazy(()=>import('./pages/maker/ManualDeposits'));const AgentCapacityRequest=/*#__PURE__*/React.lazy(()=>import('./pages/agent/CapacityRequest'));const AgentNetworkStats=/*#__PURE__*/React.lazy(()=>import('./pages/agent/NetworkStats'));const WalletFlow=/*#__PURE__*/React.lazy(()=>import('./pages/agent/WalletFlow'));const AgentOrderReports=/*#__PURE__*/React.lazy(()=>import('./pages/agent/OrderReports'));const WithdrawList=/*#__PURE__*/React.lazy(()=>import('./pages/agent/WithdrawList'));const CustomerFilfox=/*#__PURE__*/React.lazy(()=>import('./pages/customer/Filfox'));function App(){const{t,i18n}=useTranslation();const[supabase,setSupabase]=useState(null);const[session,setSession]=useState(null);const[loading,setLoading]=useState(true);const role=localStorage.getItem('user_role');// 从 localStorage 读取用户角色\nuseEffect(()=>{const initialize=async()=>{const supa=await initSupabase();setSupabase(supa);const{data:{session}}=await supa.auth.getSession();setSession(session);supa.auth.onAuthStateChange((_event,newSession)=>{setSession(newSession);if(!newSession){localStorage.removeItem('user_role');}});setLoading(false);};initialize();},[]);const changeLanguage=lng=>{i18n.changeLanguage(lng);};// Logout function\nconst handleLogout=async()=>{// Clear localStorage\nlocalStorage.clear();// Sign out from Supabase\nif(supabase){await supabase.auth.signOut();}// Redirect to login page\nwindow.location.href='#/login';};// Debug: Log current URL and hash\nReact.useEffect(()=>{console.log('App mounted. Current URL:',window.location.href);console.log('Hash:',window.location.hash);},[]);// Require login to access protected pages\nconst RequireAuth=_ref=>{let{children}=_ref;const location=useLocation();if(!session){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}return children;};// Auto redirect from \"/\" based on role\nconst RoleRedirect=()=>{const role=localStorage.getItem('user_role');if(role==='maker')return/*#__PURE__*/_jsx(Navigate,{to:\"/maker\",replace:true});if(role==='agent')return/*#__PURE__*/_jsx(Navigate,{to:\"/agent\",replace:true});if(role==='customer')return/*#__PURE__*/_jsx(Navigate,{to:\"/customer\",replace:true});return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});// default to login page\n};return/*#__PURE__*/_jsx(HashRouter,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Navbar,{bg:\"dark\",variant:\"dark\",expand:\"lg\",children:/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(Navbar.Toggle,{\"aria-controls\":\"basic-navbar-nav\"}),/*#__PURE__*/_jsxs(Navbar.Collapse,{id:\"basic-navbar-nav\",children:[/*#__PURE__*/_jsxs(Nav,{className:\"me-auto\",children:[role==='maker'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaHardHat,{className:\"me-1\"}),t('miner_management')]}),id:\"maker-miner-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/miners\",children:t('miner_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/facilities\",children:t('facility_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/earnings\",children:t('earnings_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/transfers\",children:t('transfer_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/snapshots\",children:t('daily_snapshot')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaGlobe,{className:\"me-1\"}),t('operations_management')]}),id:\"maker-operations-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/capacity\",children:t('capacity_expansion_request')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/orders\",children:t('maker_orders')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/manual-deposits\",children:t('manual_deposit')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaCoins,{className:\"me-1\"}),t('coin_management')]}),id:\"maker-coin-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/coin-batches\",children:t('coin_batches')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/network-stats\",children:t('network_stats')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaChartBar,{className:\"me-1\"}),t('report_management')]}),id:\"maker-report-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/customer-assets\",children:t('customer_assets')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/order-reports\",children:t('order_reports')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/order-distributions\",children:t('order_distributions')})]})]}),role==='agent'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaGlobe,{className:\"me-1\"}),t('operational_settings')]}),id:\"agent-operational-dropdown\",children:/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/power\",children:t('power_records')})}),/*#__PURE__*/_jsx(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaFileInvoiceDollar,{className:\"me-1\"}),t('profit_management')]}),id:\"agent-profit-dropdown\",children:/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/profit\",children:t('profit_records')})}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaUsers,{className:\"me-1\"}),t('member_management')]}),id:\"agent-member-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/member-list\",children:t('member_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/recommendation\",children:t('recommendation')})]}),/*#__PURE__*/_jsx(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaShoppingBag,{className:\"me-1\"}),t('product_management')]}),id:\"agent-product-dropdown\",children:/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/products\",children:t('product_list')})}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaYenSign,{className:\"me-1\"}),t('finance_management')]}),id:\"agent-finance-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/wallet-flow-list\",children:t('wallet_flow')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/withdraw-list\",children:t('withdraw_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/order-list\",children:t('order_list')})]})]}),role==='customer'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Nav.Link,{as:Link,to:\"/customer/filfox\",children:[/*#__PURE__*/_jsx(FaGlobe,{className:\"me-1\"}),\"FilFox\"]}),/*#__PURE__*/_jsxs(Nav.Link,{as:Link,to:\"/my-gains\",children:[/*#__PURE__*/_jsx(FaBuffer,{className:\"me-1\"}),t('assets')]}),/*#__PURE__*/_jsxs(Nav.Link,{as:Link,to:\"/my\",children:[/*#__PURE__*/_jsx(FaUser,{className:\"me-1\"}),t('my_page')]})]}),/*#__PURE__*/_jsxs(Nav.Link,{href:\"#/\",children:[/*#__PURE__*/_jsx(FaTachometerAlt,{className:\"me-1\"}),t('dashboard')]})]}),/*#__PURE__*/_jsxs(Nav,{children:[/*#__PURE__*/_jsxs(NavDropdown,{title:t('language'),id:\"basic-nav-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('ja'),children:\"\\u65E5\\u672C\\u8A9E\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('zh'),children:\"\\u4E2D\\u6587\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('en'),children:\"English\"})]}),session&&/*#__PURE__*/_jsxs(Button,{variant:\"outline-light\",size:\"sm\",onClick:handleLogout,className:\"ms-2\",children:[/*#__PURE__*/_jsx(FaSignOutAlt,{className:\"me-1\"}),t('logout')]})]})]})]})}),/*#__PURE__*/_jsx(Container,{className:\"mt-4\",children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(\"div\",{children:t('loading')}),children:loading?/*#__PURE__*/_jsx(\"div\",{children:t('initializing_platform')}):!supabase?/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-danger\",children:t('backend_connection_failed')}):/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(RoleRedirect,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/customer\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CustomerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/customer/filfox\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CustomerFilfox,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(ProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/orders\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MyAccountPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my-gains\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MyGainsPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my/kyc\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(KycPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my/recommend\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(RecommendPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my/change-login-pass\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(ChangeLoginPass,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/member-list\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(Members,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/recommendation\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(Recommend,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/debug\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(DebugAgent,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/power\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentCapacityRequest,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/profit\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentNetworkStats,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/wallet-flow-list\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(WalletFlow,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/order-list\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentOrderReports,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/withdraw-list\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(WithdrawList,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/orders\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerOrderListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/facilities\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerFacilityListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/miners\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerMinerListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/earnings\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MinerEarnings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/transfers\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(Transactions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/snapshots\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MinerSnapshots,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/coin-batches\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CoinBatches,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/network-stats\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(NetworkStats,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/customer-assets\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CustomerAssets,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/order-reports\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderReports,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/order-distributions\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderDistributions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/capacity\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CapacityRequest,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/manual-deposits\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(ManualDeposits,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})]})})})]})});}export default App;", "map": {"version": 3, "names": ["React", "Suspense", "useEffect", "useState", "initSupabase", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Link", "Navigate", "useLocation", "Container", "<PERSON><PERSON><PERSON>", "Nav", "NavDropdown", "<PERSON><PERSON>", "useTranslation", "FaSignOutAlt", "FaTachometerAlt", "FaHardHat", "FaUser", "FaGlobe", "FaCoins", "FaChartBar", "FaFileInvoiceDollar", "FaUsers", "FaShoppingBag", "FaYenSign", "<PERSON>a<PERSON><PERSON><PERSON>", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "LoginPage", "lazy", "CustomerDashboard", "ProductListPage", "OrderListPage", "MyAccountPage", "MyGainsPage", "KycPage", "RecommendPage", "ChangeLoginPass", "AgentDashboard", "Members", "Recommend", "AgentProductListPage", "DebugAgent", "MakerDashboard", "MakerProductListPage", "MakerOrderListPage", "MakerFacilityListPage", "MakerMinerListPage", "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "MinerSnapshots", "Transactions", "CoinBatches", "NetworkStats", "CustomerAssets", "OrderReports", "OrderDistributions", "CapacityRequest", "ManualDeposits", "AgentCapacityRequest", "AgentNetworkStats", "WalletFlow", "AgentOrderReports", "WithdrawList", "CustomerFilfox", "App", "t", "i18n", "supabase", "set<PERSON><PERSON><PERSON><PERSON>", "session", "setSession", "loading", "setLoading", "role", "localStorage", "getItem", "initialize", "supa", "data", "auth", "getSession", "onAuthStateChange", "_event", "newSession", "removeItem", "changeLanguage", "lng", "handleLogout", "clear", "signOut", "window", "location", "href", "console", "log", "hash", "RequireAuth", "_ref", "children", "to", "state", "from", "replace", "RoleRedirect", "bg", "variant", "expand", "Toggle", "Collapse", "id", "className", "title", "<PERSON><PERSON>", "as", "onClick", "size", "fallback", "path", "element"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/App.js"], "sourcesContent": ["import React, { Suspense, useEffect, useState } from 'react';\nimport { initSupabase } from './supabaseClient';\nimport {\n  HashRouter,\n  Routes,\n  Route,\n  Link,\n  Navigate,\n  useLocation,\n} from 'react-router-dom';\nimport { Container, Navbar, Nav, NavDropdown, Button } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { FaSignOutAlt, FaTachometerAlt, FaHardHat, FaUser, FaGlobe, FaCoins, FaChartBar, FaFileInvoiceDollar, FaUsers, FaShoppingBag, FaYenSign, FaBuffer } from 'react-icons/fa';\n\n// Lazy load components for better performance\nconst LoginPage = React.lazy(() => import('./pages/LoginPage'));\nconst CustomerDashboard = React.lazy(() => import('./pages/customer/Dashboard'));\nconst ProductListPage = React.lazy(() => import('./pages/customer/ProductListPage'));\nconst OrderListPage = React.lazy(() => import('./pages/customer/OrderListPage'));\nconst MyAccountPage = React.lazy(() => import('./pages/customer/MyAccountPage'));\nconst MyGainsPage = React.lazy(() => import('./pages/customer/MyGainsPage'));\nconst KycPage = React.lazy(() => import('./pages/customer/KycPage'));\nconst RecommendPage = React.lazy(() => import('./pages/customer/RecommendPage'));\nconst ChangeLoginPass = React.lazy(() => import('./pages/customer/ChangeLoginPass'));\nconst AgentDashboard = React.lazy(() => import('./pages/agent/Dashboard'));\nconst Members = React.lazy(() => import('./pages/agent/Members'));\nconst Recommend = React.lazy(() => import('./pages/agent/Recommend'));\nconst AgentProductListPage = React.lazy(() => import('./pages/agent/AgentProductListPage'));\nconst DebugAgent = React.lazy(() => import('./pages/agent/DebugAgent'));\nconst MakerDashboard = React.lazy(() => import('./pages/maker/Dashboard'));\nconst MakerProductListPage = React.lazy(() => import('./pages/maker/MakerProductListPage'));\nconst MakerOrderListPage = React.lazy(() => import('./pages/maker/MakerOrderListPage'));\nconst MakerFacilityListPage = React.lazy(() => import('./pages/maker/MakerFacilities'));\nconst MakerMinerListPage = React.lazy(() => import('./pages/maker/MakerMiners'));\nconst MinerEarnings = React.lazy(() => import('./pages/maker/MinerEarnings'));\nconst MinerSnapshots = React.lazy(() => import('./pages/maker/MinerSnapshots'));\nconst Transactions = React.lazy(() => import('./pages/maker/Transactions'));\nconst CoinBatches = React.lazy(() => import('./pages/maker/CoinBatches'));\nconst NetworkStats = React.lazy(() => import('./pages/maker/NetworkStats'));\nconst CustomerAssets = React.lazy(() => import('./pages/maker/CustomerAssets'));\nconst OrderReports = React.lazy(() => import('./pages/maker/OrderReports'));\nconst OrderDistributions = React.lazy(() => import('./pages/maker/OrderDistributions'));\nconst CapacityRequest = React.lazy(() => import('./pages/maker/CapacityRequest'));\nconst ManualDeposits = React.lazy(() => import('./pages/maker/ManualDeposits'));\nconst AgentCapacityRequest = React.lazy(() => import('./pages/agent/CapacityRequest'));\nconst AgentNetworkStats = React.lazy(() => import('./pages/agent/NetworkStats'));\nconst WalletFlow = React.lazy(() => import('./pages/agent/WalletFlow'));\nconst AgentOrderReports = React.lazy(() => import('./pages/agent/OrderReports'));\nconst WithdrawList = React.lazy(() => import('./pages/agent/WithdrawList'));\nconst CustomerFilfox = React.lazy(() => import('./pages/customer/Filfox'));\n\nfunction App() {\n  const { t, i18n } = useTranslation();\n  const [supabase, setSupabase] = useState(null);\n  const [session, setSession] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const role = localStorage.getItem('user_role'); // 从 localStorage 读取用户角色\n\n  useEffect(() => {\n    const initialize = async () => {\n      const supa = await initSupabase();\n      setSupabase(supa);\n\n      const { data: { session } } = await supa.auth.getSession();\n      setSession(session);\n\n      supa.auth.onAuthStateChange((_event, newSession) => {\n        setSession(newSession);\n        if (!newSession) {\n          localStorage.removeItem('user_role');\n        }\n      });\n\n      setLoading(false);\n    };\n    initialize();\n  }, []);\n\n  const changeLanguage = (lng) => {\n    i18n.changeLanguage(lng);\n  };\n\n  // Logout function\n  const handleLogout = async () => {\n    // Clear localStorage\n    localStorage.clear();\n    \n    // Sign out from Supabase\n    if (supabase) {\n      await supabase.auth.signOut();\n    }\n    \n    // Redirect to login page\n    window.location.href = '#/login';\n  };\n\n  // Debug: Log current URL and hash\n  React.useEffect(() => {\n    console.log('App mounted. Current URL:', window.location.href);\n    console.log('Hash:', window.location.hash);\n  }, []);\n\n  // Require login to access protected pages\n  const RequireAuth = ({ children }) => {\n    const location = useLocation();\n    if (!session) {\n      return <Navigate to=\"/login\" state={{ from: location }} replace />;\n    }\n    return children;\n  };\n\n  // Auto redirect from \"/\" based on role\n  const RoleRedirect = () => {\n    const role = localStorage.getItem('user_role');\n    if (role === 'maker') return <Navigate to=\"/maker\" replace />;\n    if (role === 'agent') return <Navigate to=\"/agent\" replace />;\n    if (role === 'customer') return <Navigate to=\"/customer\" replace />;\n    return <Navigate to=\"/login\" replace />; // default to login page\n  };\n\n  return (\n    <HashRouter>\n      <div>\n        <Navbar bg=\"dark\" variant=\"dark\" expand=\"lg\">\n          <Container>\n            <Navbar.Toggle aria-controls=\"basic-navbar-nav\" />\n            <Navbar.Collapse id=\"basic-navbar-nav\">\n              <Nav className=\"me-auto\">\n                {/* ===== ★ Maker 导航开始 ★ ===== */}\n                {role === 'maker' && (\n                  <>\n                    {/* Miner Management 下拉 */}\n                    <NavDropdown\n                      title={\n                        <>\n                          <FaHardHat className=\"me-1\" />\n                          {t('miner_management')}\n                        </>\n                      }\n                      id=\"maker-miner-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/miners\">\n                        {t('miner_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/facilities\">\n                        {t('facility_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/earnings\">\n                        {t('earnings_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/transfers\">\n                        {t('transfer_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/snapshots\">\n                        {t('daily_snapshot')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaGlobe className=\"me-1\" />\n                          {t('operations_management')}\n                        </>\n                      }\n                      id=\"maker-operations-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/capacity\">\n                        {t('capacity_expansion_request')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/orders\">\n                        {t('maker_orders')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/manual-deposits\">\n                        {t('manual_deposit')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaCoins className=\"me-1\" />\n                          {t('coin_management')}\n                        </>\n                      }\n                      id=\"maker-coin-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/coin-batches\">\n                        {t('coin_batches')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/network-stats\">\n                        {t('network_stats')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaChartBar className=\"me-1\" />\n                          {t('report_management')}\n                        </>\n                      }\n                      id=\"maker-report-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/customer-assets\">\n                        {t('customer_assets')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/order-reports\">\n                        {t('order_reports')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/order-distributions\">\n                        {t('order_distributions')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n                  </>\n                )}\n                {/* ===== ★ Maker 导航结束 ★ ===== */}\n\n                {/* ===== ★ Agent 导航开始 ★ ===== */}\n                {role === 'agent' && (\n                  <>\n                    {/* Agent Management 下拉 */}\n                    <NavDropdown\n                      title={\n                        <>\n                          <FaGlobe className=\"me-1\" />\n                          {t('operational_settings')}\n                        </>\n                      }\n                      id=\"agent-operational-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/power\">\n                        {t('power_records')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaFileInvoiceDollar className=\"me-1\" />\n                          {t('profit_management')}\n                        </>\n                      }\n                      id=\"agent-profit-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/profit\">\n                        {t('profit_records')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaUsers className=\"me-1\" />\n                          {t('member_management')}\n                        </>\n                      }\n                      id=\"agent-member-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/member-list\">\n                        {t('member_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/agent/recommendation\">\n                        {t('recommendation')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaShoppingBag className=\"me-1\" />\n                          {t('product_management')}\n                        </>\n                      }\n                      id=\"agent-product-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/products\">\n                        {t('product_list')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaYenSign className=\"me-1\" />\n                          {t('finance_management')}\n                        </>\n                      }\n                      id=\"agent-finance-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/wallet-flow-list\">\n                        {t('wallet_flow')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/agent/withdraw-list\">\n                        {t('withdraw_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/agent/order-list\">\n                        {t('order_list')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n                  </>\n                )}\n                {/* ===== ★ Agent 导航结束 ★ ===== */}\n\n                {/* ===== ★ Customer 导航开始 ★ ===== */}\n                {role === 'customer' && (\n                  <>\n                    {/* Customer Management 下拉 */}\n                    <Nav.Link as={Link} to=\"/customer/filfox\">\n                      <FaGlobe className=\"me-1\" />\n                      FilFox\n                    </Nav.Link>\n                    <Nav.Link as={Link} to=\"/my-gains\">\n                      <FaBuffer className=\"me-1\" />\n                      {t('assets')}\n                    </Nav.Link>\n                    <Nav.Link as={Link} to=\"/my\">\n                      <FaUser className=\"me-1\" />\n                      {t('my_page')}\n                    </Nav.Link>\n                  </>\n                )}\n                {/* ===== ★ Customer 导航结束 ★ ===== */}\n\n                <Nav.Link href=\"#/\">\n                  <FaTachometerAlt className=\"me-1\" />\n                  {t('dashboard')}\n                </Nav.Link>\n                {/* Add other nav links based on role later */}\n              </Nav>\n              <Nav>\n                <NavDropdown title={t('language')} id=\"basic-nav-dropdown\">\n                  <NavDropdown.Item onClick={() => changeLanguage('ja')}>日本語</NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => changeLanguage('zh')}>中文</NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => changeLanguage('en')}>English</NavDropdown.Item>\n                </NavDropdown>\n                {/* Logout Button */}\n                {session && (\n                  <Button \n                    variant=\"outline-light\" \n                    size=\"sm\" \n                    onClick={handleLogout}\n                    className=\"ms-2\"\n                  >\n                    <FaSignOutAlt className=\"me-1\" />\n                    {t('logout')}\n                  </Button>\n                )}\n              </Nav>\n            </Navbar.Collapse>\n          </Container>\n        </Navbar>\n\n        <Container className=\"mt-4\">\n          <Suspense fallback={<div>{t('loading')}</div>}>\n            {loading ? (\n              <div>{t('initializing_platform')}</div>\n            ) : !supabase ? (\n              <div className=\"alert alert-danger\">{t('backend_connection_failed')}</div>\n            ) : (\n              <Routes>\n                {/* Public Route */}\n                <Route path=\"/login\" element={<LoginPage />} />\n\n                {/* Root path → redirect by role */}\n                <Route path=\"/\" element={<RequireAuth><RoleRedirect /></RequireAuth>} />\n\n                {/* Customer Routes */}\n                <Route path=\"/customer\" element={<RequireAuth><CustomerDashboard /></RequireAuth>} />\n                <Route path=\"/customer/filfox\" element={<RequireAuth><CustomerFilfox /></RequireAuth>} />\n                <Route path=\"/products\" element={<RequireAuth><ProductListPage /></RequireAuth>} />\n                <Route path=\"/orders\" element={<RequireAuth><OrderListPage /></RequireAuth>} />\n                <Route path=\"/my\" element={<RequireAuth><MyAccountPage /></RequireAuth>} />\n                <Route path=\"/my-gains\" element={<RequireAuth><MyGainsPage /></RequireAuth>} />\n                <Route path=\"/my/kyc\" element={<RequireAuth><KycPage /></RequireAuth>} />\n                <Route path=\"/my/recommend\" element={<RequireAuth><RecommendPage /></RequireAuth>} />\n                <Route path=\"/my/change-login-pass\" element={<RequireAuth><ChangeLoginPass /></RequireAuth>} />\n\n                {/* Agent Routes */}\n                <Route path=\"/agent\" element={<RequireAuth><AgentDashboard /></RequireAuth>} />\n                <Route path=\"/agent/member-list\" element={<RequireAuth><Members /></RequireAuth>} />\n                <Route path=\"/agent/recommendation\" element={<RequireAuth><Recommend /></RequireAuth>} />\n                <Route path=\"/agent/products\" element={<RequireAuth><AgentProductListPage /></RequireAuth>} />\n                <Route path=\"/agent/debug\" element={<RequireAuth><DebugAgent /></RequireAuth>} />\n                <Route path=\"/agent/power\" element={<RequireAuth><AgentCapacityRequest /></RequireAuth>} />\n                <Route path=\"/agent/profit\" element={<RequireAuth><AgentNetworkStats /></RequireAuth>} />\n                <Route path=\"/agent/wallet-flow-list\" element={<RequireAuth><WalletFlow /></RequireAuth>} />\n                <Route path=\"/agent/order-list\" element={<RequireAuth><AgentOrderReports /></RequireAuth>} />\n                <Route path=\"/agent/withdraw-list\" element={<RequireAuth><WithdrawList /></RequireAuth>} />\n\n                {/* Maker Routes */}\n                  <Route path=\"/maker\" element={<RequireAuth><MakerDashboard /></RequireAuth>} />\n                  <Route path=\"/maker/products\" element={<RequireAuth><MakerProductListPage /></RequireAuth>} />\n                  <Route path=\"/maker/orders\" element={<RequireAuth><MakerOrderListPage /></RequireAuth>} />\n                  <Route path=\"/maker/facilities\" element={<RequireAuth><MakerFacilityListPage /></RequireAuth>} />\n                  <Route path=\"/maker/miners\" element={<RequireAuth><MakerMinerListPage /></RequireAuth>} />\n                  <Route path=\"/maker/earnings\" element={<RequireAuth><MinerEarnings /></RequireAuth>} />\n                  <Route path=\"/maker/transfers\" element={<RequireAuth><Transactions /></RequireAuth>} />\n                  <Route path=\"/maker/snapshots\" element={<RequireAuth><MinerSnapshots /></RequireAuth>} />\n                  <Route path=\"/maker/coin-batches\" element={<RequireAuth><CoinBatches /></RequireAuth>} />\n                  <Route path=\"/maker/network-stats\" element={<RequireAuth><NetworkStats /></RequireAuth>} />\n                  <Route path=\"/maker/customer-assets\" element={<RequireAuth><CustomerAssets /></RequireAuth>} />\n                  <Route path=\"/maker/order-reports\" element={<RequireAuth><OrderReports /></RequireAuth>} />\n                  <Route path=\"/maker/order-distributions\" element={<RequireAuth><OrderDistributions /></RequireAuth>} />\n                  <Route path=\"/maker/capacity\" element={<RequireAuth><CapacityRequest /></RequireAuth>} />\n                  <Route path=\"/maker/manual-deposits\" element={<RequireAuth><ManualDeposits /></RequireAuth>} />\n                \n                {/* Fallback */}\n                <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n              </Routes>\n            )}\n          </Suspense>\n        </Container>\n      </div>\n    </HashRouter>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC5D,OAASC,YAAY,KAAQ,kBAAkB,CAC/C,OACEC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,WAAW,KACN,kBAAkB,CACzB,OAASC,SAAS,CAAEC,MAAM,CAAEC,GAAG,CAAEC,WAAW,CAAEC,MAAM,KAAQ,iBAAiB,CAC7E,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,YAAY,CAAEC,eAAe,CAAEC,SAAS,CAAEC,MAAM,CAAEC,OAAO,CAAEC,OAAO,CAAEC,UAAU,CAAEC,mBAAmB,CAAEC,OAAO,CAAEC,aAAa,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,gBAAgB,CAEjL;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,SAAS,cAAGnC,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAC/D,KAAM,CAAAC,iBAAiB,cAAGrC,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAChF,KAAM,CAAAE,eAAe,cAAGtC,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACpF,KAAM,CAAAG,aAAa,cAAGvC,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAI,aAAa,cAAGxC,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAK,WAAW,cAAGzC,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC5E,KAAM,CAAAM,OAAO,cAAG1C,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CACpE,KAAM,CAAAO,aAAa,cAAG3C,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAQ,eAAe,cAAG5C,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACpF,KAAM,CAAAS,cAAc,cAAG7C,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAU,OAAO,cAAG9C,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC,CACjE,KAAM,CAAAW,SAAS,cAAG/C,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CACrE,KAAM,CAAAY,oBAAoB,cAAGhD,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAa,UAAU,cAAGjD,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CACvE,KAAM,CAAAc,cAAc,cAAGlD,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAe,oBAAoB,cAAGnD,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAgB,kBAAkB,cAAGpD,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACvF,KAAM,CAAAiB,qBAAqB,cAAGrD,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACvF,KAAM,CAAAkB,kBAAkB,cAAGtD,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAChF,KAAM,CAAAmB,aAAa,cAAGvD,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAC7E,KAAM,CAAAoB,cAAc,cAAGxD,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAAqB,YAAY,cAAGzD,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAsB,WAAW,cAAG1D,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC,CACzE,KAAM,CAAAuB,YAAY,cAAG3D,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAwB,cAAc,cAAG5D,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAAyB,YAAY,cAAG7D,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAA0B,kBAAkB,cAAG9D,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACvF,KAAM,CAAA2B,eAAe,cAAG/D,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACjF,KAAM,CAAA4B,cAAc,cAAGhE,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAA6B,oBAAoB,cAAGjE,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACtF,KAAM,CAAA8B,iBAAiB,cAAGlE,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAChF,KAAM,CAAA+B,UAAU,cAAGnE,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CACvE,KAAM,CAAAgC,iBAAiB,cAAGpE,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAChF,KAAM,CAAAiC,YAAY,cAAGrE,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAkC,cAAc,cAAGtE,KAAK,CAACoC,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAE1E,QAAS,CAAAmC,GAAGA,CAAA,CAAG,CACb,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGzD,cAAc,CAAC,CAAC,CACpC,KAAM,CAAC0D,QAAQ,CAAEC,WAAW,CAAC,CAAGxE,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACyE,OAAO,CAAEC,UAAU,CAAC,CAAG1E,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2E,OAAO,CAAEC,UAAU,CAAC,CAAG5E,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAA6E,IAAI,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAE;AAEhDhF,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiF,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAhF,YAAY,CAAC,CAAC,CACjCuE,WAAW,CAACS,IAAI,CAAC,CAEjB,KAAM,CAAEC,IAAI,CAAE,CAAET,OAAQ,CAAE,CAAC,CAAG,KAAM,CAAAQ,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,CAAC,CAC1DV,UAAU,CAACD,OAAO,CAAC,CAEnBQ,IAAI,CAACE,IAAI,CAACE,iBAAiB,CAAC,CAACC,MAAM,CAAEC,UAAU,GAAK,CAClDb,UAAU,CAACa,UAAU,CAAC,CACtB,GAAI,CAACA,UAAU,CAAE,CACfT,YAAY,CAACU,UAAU,CAAC,WAAW,CAAC,CACtC,CACF,CAAC,CAAC,CAEFZ,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CACDI,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAS,cAAc,CAAIC,GAAG,EAAK,CAC9BpB,IAAI,CAACmB,cAAc,CAACC,GAAG,CAAC,CAC1B,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B;AACAb,YAAY,CAACc,KAAK,CAAC,CAAC,CAEpB;AACA,GAAIrB,QAAQ,CAAE,CACZ,KAAM,CAAAA,QAAQ,CAACY,IAAI,CAACU,OAAO,CAAC,CAAC,CAC/B,CAEA;AACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,SAAS,CAClC,CAAC,CAED;AACAnG,KAAK,CAACE,SAAS,CAAC,IAAM,CACpBkG,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEJ,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAC9DC,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEJ,MAAM,CAACC,QAAQ,CAACI,IAAI,CAAC,CAC5C,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC/B,KAAM,CAAAN,QAAQ,CAAGxF,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACkE,OAAO,CAAE,CACZ,mBAAO9C,IAAA,CAACrB,QAAQ,EAACiG,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAEV,QAAS,CAAE,CAACW,OAAO,MAAE,CAAC,CACpE,CACA,MAAO,CAAAJ,QAAQ,CACjB,CAAC,CAED;AACA,KAAM,CAAAK,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAA9B,IAAI,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAC9C,GAAIF,IAAI,GAAK,OAAO,CAAE,mBAAOlD,IAAA,CAACrB,QAAQ,EAACiG,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAC7D,GAAI7B,IAAI,GAAK,OAAO,CAAE,mBAAOlD,IAAA,CAACrB,QAAQ,EAACiG,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAC7D,GAAI7B,IAAI,GAAK,UAAU,CAAE,mBAAOlD,IAAA,CAACrB,QAAQ,EAACiG,EAAE,CAAC,WAAW,CAACG,OAAO,MAAE,CAAC,CACnE,mBAAO/E,IAAA,CAACrB,QAAQ,EAACiG,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAAE;AAC3C,CAAC,CAED,mBACE/E,IAAA,CAACzB,UAAU,EAAAoG,QAAA,cACTvE,KAAA,QAAAuE,QAAA,eACE3E,IAAA,CAAClB,MAAM,EAACmG,EAAE,CAAC,MAAM,CAACC,OAAO,CAAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAAR,QAAA,cAC1CvE,KAAA,CAACvB,SAAS,EAAA8F,QAAA,eACR3E,IAAA,CAAClB,MAAM,CAACsG,MAAM,EAAC,gBAAc,kBAAkB,CAAE,CAAC,cAClDhF,KAAA,CAACtB,MAAM,CAACuG,QAAQ,EAACC,EAAE,CAAC,kBAAkB,CAAAX,QAAA,eACpCvE,KAAA,CAACrB,GAAG,EAACwG,SAAS,CAAC,SAAS,CAAAZ,QAAA,EAErBzB,IAAI,GAAK,OAAO,eACf9C,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eAEEvE,KAAA,CAACpB,WAAW,EACVwG,KAAK,cACHpF,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eACE3E,IAAA,CAACX,SAAS,EAACkG,SAAS,CAAC,MAAM,CAAE,CAAC,CAC7B7C,CAAC,CAAC,kBAAkB,CAAC,EACtB,CACH,CACD4C,EAAE,CAAC,sBAAsB,CAAAX,QAAA,eAEzB3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3CjC,CAAC,CAAC,YAAY,CAAC,CACA,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,mBAAmB,CAAAD,QAAA,CAC/CjC,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7CjC,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,kBAAkB,CAAAD,QAAA,CAC9CjC,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,kBAAkB,CAAAD,QAAA,CAC9CjC,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEdtC,KAAA,CAACpB,WAAW,EAACwG,KAAK,cACdpF,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eACE3E,IAAA,CAACT,OAAO,EAACgG,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B7C,CAAC,CAAC,uBAAuB,CAAC,EAC3B,CACH,CACD4C,EAAE,CAAC,2BAA2B,CAAAX,QAAA,eAE9B3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7CjC,CAAC,CAAC,4BAA4B,CAAC,CAChB,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3CjC,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,wBAAwB,CAAAD,QAAA,CACpDjC,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEdtC,KAAA,CAACpB,WAAW,EAACwG,KAAK,cACdpF,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eACE3E,IAAA,CAACR,OAAO,EAAC+F,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B7C,CAAC,CAAC,iBAAiB,CAAC,EACrB,CACH,CACD4C,EAAE,CAAC,qBAAqB,CAAAX,QAAA,eAExB3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,qBAAqB,CAAAD,QAAA,CACjDjC,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,sBAAsB,CAAAD,QAAA,CAClDjC,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,EACR,CAAC,cAEdtC,KAAA,CAACpB,WAAW,EAACwG,KAAK,cACdpF,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eACE3E,IAAA,CAACP,UAAU,EAAC8F,SAAS,CAAC,MAAM,CAAE,CAAC,CAC9B7C,CAAC,CAAC,mBAAmB,CAAC,EACvB,CACH,CACD4C,EAAE,CAAC,uBAAuB,CAAAX,QAAA,eAE1B3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,wBAAwB,CAAAD,QAAA,CACpDjC,CAAC,CAAC,iBAAiB,CAAC,CACL,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,sBAAsB,CAAAD,QAAA,CAClDjC,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,4BAA4B,CAAAD,QAAA,CACxDjC,CAAC,CAAC,qBAAqB,CAAC,CACT,CAAC,EACR,CAAC,EACd,CACH,CAIAQ,IAAI,GAAK,OAAO,eACf9C,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eAEE3E,IAAA,CAAChB,WAAW,EACVwG,KAAK,cACHpF,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eACE3E,IAAA,CAACT,OAAO,EAACgG,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B7C,CAAC,CAAC,sBAAsB,CAAC,EAC1B,CACH,CACD4C,EAAE,CAAC,4BAA4B,CAAAX,QAAA,cAE/B3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,cAAc,CAAAD,QAAA,CAC1CjC,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,CACR,CAAC,cAEd1C,IAAA,CAAChB,WAAW,EAACwG,KAAK,cACdpF,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eACE3E,IAAA,CAACN,mBAAmB,EAAC6F,SAAS,CAAC,MAAM,CAAE,CAAC,CACvC7C,CAAC,CAAC,mBAAmB,CAAC,EACvB,CACH,CACD4C,EAAE,CAAC,uBAAuB,CAAAX,QAAA,cAE1B3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3CjC,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,CACR,CAAC,cAEdtC,KAAA,CAACpB,WAAW,EAACwG,KAAK,cACdpF,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eACE3E,IAAA,CAACL,OAAO,EAAC4F,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B7C,CAAC,CAAC,mBAAmB,CAAC,EACvB,CACH,CACD4C,EAAE,CAAC,uBAAuB,CAAAX,QAAA,eAE1B3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,oBAAoB,CAAAD,QAAA,CAChDjC,CAAC,CAAC,aAAa,CAAC,CACD,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,uBAAuB,CAAAD,QAAA,CACnDjC,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEd1C,IAAA,CAAChB,WAAW,EAACwG,KAAK,cACdpF,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eACE3E,IAAA,CAACJ,aAAa,EAAC2F,SAAS,CAAC,MAAM,CAAE,CAAC,CACjC7C,CAAC,CAAC,oBAAoB,CAAC,EACxB,CACH,CACD4C,EAAE,CAAC,wBAAwB,CAAAX,QAAA,cAE3B3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7CjC,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,CACR,CAAC,cAEdtC,KAAA,CAACpB,WAAW,EAACwG,KAAK,cACdpF,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eACE3E,IAAA,CAACH,SAAS,EAAC0F,SAAS,CAAC,MAAM,CAAE,CAAC,CAC7B7C,CAAC,CAAC,oBAAoB,CAAC,EACxB,CACH,CACD4C,EAAE,CAAC,wBAAwB,CAAAX,QAAA,eAE3B3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,yBAAyB,CAAAD,QAAA,CACrDjC,CAAC,CAAC,aAAa,CAAC,CACD,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,sBAAsB,CAAAD,QAAA,CAClDjC,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnB1C,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACC,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,mBAAmB,CAAAD,QAAA,CAC/CjC,CAAC,CAAC,YAAY,CAAC,CACA,CAAC,EACR,CAAC,EACd,CACH,CAIAQ,IAAI,GAAK,UAAU,eAClB9C,KAAA,CAAAF,SAAA,EAAAyE,QAAA,eAEEvE,KAAA,CAACrB,GAAG,CAACL,IAAI,EAACgH,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,kBAAkB,CAAAD,QAAA,eACvC3E,IAAA,CAACT,OAAO,EAACgG,SAAS,CAAC,MAAM,CAAE,CAAC,SAE9B,EAAU,CAAC,cACXnF,KAAA,CAACrB,GAAG,CAACL,IAAI,EAACgH,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,WAAW,CAAAD,QAAA,eAChC3E,IAAA,CAACF,QAAQ,EAACyF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC5B7C,CAAC,CAAC,QAAQ,CAAC,EACJ,CAAC,cACXtC,KAAA,CAACrB,GAAG,CAACL,IAAI,EAACgH,EAAE,CAAEhH,IAAK,CAACkG,EAAE,CAAC,KAAK,CAAAD,QAAA,eAC1B3E,IAAA,CAACV,MAAM,EAACiG,SAAS,CAAC,MAAM,CAAE,CAAC,CAC1B7C,CAAC,CAAC,SAAS,CAAC,EACL,CAAC,EACX,CACH,cAGDtC,KAAA,CAACrB,GAAG,CAACL,IAAI,EAAC2F,IAAI,CAAC,IAAI,CAAAM,QAAA,eACjB3E,IAAA,CAACZ,eAAe,EAACmG,SAAS,CAAC,MAAM,CAAE,CAAC,CACnC7C,CAAC,CAAC,WAAW,CAAC,EACP,CAAC,EAER,CAAC,cACNtC,KAAA,CAACrB,GAAG,EAAA4F,QAAA,eACFvE,KAAA,CAACpB,WAAW,EAACwG,KAAK,CAAE9C,CAAC,CAAC,UAAU,CAAE,CAAC4C,EAAE,CAAC,oBAAoB,CAAAX,QAAA,eACxD3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACE,OAAO,CAAEA,CAAA,GAAM7B,cAAc,CAAC,IAAI,CAAE,CAAAa,QAAA,CAAC,oBAAG,CAAkB,CAAC,cAC7E3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACE,OAAO,CAAEA,CAAA,GAAM7B,cAAc,CAAC,IAAI,CAAE,CAAAa,QAAA,CAAC,cAAE,CAAkB,CAAC,cAC5E3E,IAAA,CAAChB,WAAW,CAACyG,IAAI,EAACE,OAAO,CAAEA,CAAA,GAAM7B,cAAc,CAAC,IAAI,CAAE,CAAAa,QAAA,CAAC,SAAO,CAAkB,CAAC,EACtE,CAAC,CAEb7B,OAAO,eACN1C,KAAA,CAACnB,MAAM,EACLiG,OAAO,CAAC,eAAe,CACvBU,IAAI,CAAC,IAAI,CACTD,OAAO,CAAE3B,YAAa,CACtBuB,SAAS,CAAC,MAAM,CAAAZ,QAAA,eAEhB3E,IAAA,CAACb,YAAY,EAACoG,SAAS,CAAC,MAAM,CAAE,CAAC,CAChC7C,CAAC,CAAC,QAAQ,CAAC,EACN,CACT,EACE,CAAC,EACS,CAAC,EACT,CAAC,CACN,CAAC,cAET1C,IAAA,CAACnB,SAAS,EAAC0G,SAAS,CAAC,MAAM,CAAAZ,QAAA,cACzB3E,IAAA,CAAC7B,QAAQ,EAAC0H,QAAQ,cAAE7F,IAAA,QAAA2E,QAAA,CAAMjC,CAAC,CAAC,SAAS,CAAC,CAAM,CAAE,CAAAiC,QAAA,CAC3C3B,OAAO,cACNhD,IAAA,QAAA2E,QAAA,CAAMjC,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,CACrC,CAACE,QAAQ,cACX5C,IAAA,QAAKuF,SAAS,CAAC,oBAAoB,CAAAZ,QAAA,CAAEjC,CAAC,CAAC,2BAA2B,CAAC,CAAM,CAAC,cAE1EtC,KAAA,CAAC5B,MAAM,EAAAmG,QAAA,eAEL3E,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE/F,IAAA,CAACK,SAAS,GAAE,CAAE,CAAE,CAAC,cAG/CL,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACgF,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGxEhF,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACO,iBAAiB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACrFP,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACwC,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFxC,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACQ,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACnFR,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,SAAS,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACS,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/ET,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,KAAK,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACU,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3EV,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACW,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/EX,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,SAAS,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACY,OAAO,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzEZ,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACa,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACrFb,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,uBAAuB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACc,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAG/Fd,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACe,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/Ef,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,oBAAoB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACgB,OAAO,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACpFhB,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,uBAAuB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACiB,SAAS,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFjB,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACkB,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC9FlB,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,cAAc,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACmB,UAAU,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACjFnB,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,cAAc,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACmC,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3FnC,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACoC,iBAAiB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFpC,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACqC,UAAU,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC5FrC,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACsC,iBAAiB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC7FtC,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACuC,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGzFvC,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACoB,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/EpB,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACqB,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC9FrB,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACsB,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC1FtB,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACuB,qBAAqB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACjGvB,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACwB,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC1FxB,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACyB,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvFzB,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAAC2B,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvF3B,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAAC0B,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzF1B,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,qBAAqB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAAC4B,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzF5B,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAAC6B,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3F7B,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAAC8B,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/F9B,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAAC+B,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3F/B,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,4BAA4B,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACgC,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvGhC,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACiC,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFjC,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAAE/F,IAAA,CAACyE,WAAW,EAAAE,QAAA,cAAC3E,IAAA,CAACkC,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGjGlC,IAAA,CAACvB,KAAK,EAACqH,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE/F,IAAA,CAACrB,QAAQ,EAACiG,EAAE,CAAC,GAAG,CAACG,OAAO,MAAE,CAAE,CAAE,CAAC,EAClD,CACT,CACO,CAAC,CACF,CAAC,EACT,CAAC,CACI,CAAC,CAEjB,CAEA,cAAe,CAAAtC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}