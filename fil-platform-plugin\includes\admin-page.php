<?php
// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin page for FIL Platform Filfox Scraper
 */
class FIL_Platform_Admin {

    public function __construct() {
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'options-general.php',
            'FIL Platform Scraper',
            'FIL Platform Scraper',
            'manage_options',
            'fil-platform-scraper',
            [$this, 'admin_page']
        );
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'settings_page_fil-platform-scraper') {
            return;
        }

        wp_enqueue_script('jquery');
        wp_localize_script('jquery', 'fil_platform_ajax', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('fil_platform_scraper_nonce')
        ]);
    }

    /**
     * Admin page content
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>FIL Platform Filfox Scraper</h1>
            
            <div class="card" style="max-width: 800px;">
                <h2 class="title">Scraper Status</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">Next Scheduled Run</th>
                        <td id="next-scheduled">Loading...</td>
                    </tr>
                    <tr>
                        <th scope="row">Next Scheduled (JST)</th>
                        <td id="next-scheduled-jst">Loading...</td>
                    </tr>
                    <tr>
                        <th scope="row">Last Run</th>
                        <td id="last-run">Loading...</td>
                    </tr>
                    <tr>
                        <th scope="row">Last Success</th>
                        <td id="last-success">Loading...</td>
                    </tr>
                    <tr>
                        <th scope="row">WP Cron Status</th>
                        <td id="cron-enabled">Loading...</td>
                    </tr>
                </table>
                
                <p class="submit">
                    <button type="button" id="refresh-status" class="button">Refresh Status</button>
                    <button type="button" id="manual-scrape" class="button button-primary">Run Manual Scrape</button>
                    <button type="button" id="reschedule-cron" class="button button-secondary">Reschedule Cron Job</button>
                </p>
                
                <div id="scraper-result" style="margin-top: 20px;"></div>
            </div>

            <div class="card" style="max-width: 800px; margin-top: 20px;">
                <h2 class="title">Configuration</h2>
                <p>The scraper is configured to run daily at <strong>2:00 AM JST (17:00 UTC)</strong>.</p>
                <p>It will scrape data from <a href="https://filfox.info/en" target="_blank">filfox.info</a> and store:</p>
                <ul>
                    <li><strong>24h Average Mining Reward</strong> - Average FIL per TiB reward</li>
                </ul>
                <p>Data is stored in the <code>network_stats</code> table in your Supabase database.</p>
                
                <?php if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON): ?>
                <div class="notice notice-warning">
                    <p><strong>Warning:</strong> WP Cron is disabled. You need to set up a system cron job to trigger WordPress cron events.</p>
                    <p>Add this to your server's crontab:</p>
                    <code>*/15 * * * * wget -q -O - <?php echo site_url('wp-cron.php'); ?> >/dev/null 2>&1</code>
                </div>
                <?php endif; ?>
            </div>

            <div class="card" style="max-width: 800px; margin-top: 20px;">
                <h2 class="title">Recent Network Stats</h2>
                <div id="recent-stats">Loading recent data...</div>
            </div>
        </div>

        <script type="text/javascript">
        jQuery(document).ready(function($) {
            
            function loadStatus() {
                $.post(fil_platform_ajax.ajax_url, {
                    action: 'fil_platform_scraper_status',
                    nonce: fil_platform_ajax.nonce
                }, function(response) {
                    if (response.success) {
                        $('#next-scheduled').text(response.data.next_scheduled);
                        $('#next-scheduled-jst').text(response.data.next_scheduled_jst);
                        $('#last-run').text(response.data.last_run);
                        $('#last-success').text(response.data.last_success);
                        $('#cron-enabled').text(response.data.cron_enabled ? 'Enabled' : 'Disabled');
                        
                        if (!response.data.cron_enabled) {
                            $('#cron-enabled').append(' <span style="color: red;">(Warning: WP Cron is disabled)</span>');
                        }
                    } else {
                        $('#scraper-result').html('<div class="notice notice-error"><p>Failed to load status: ' + (response.data ? response.data.message : 'Unknown error') + '</p></div>');
                    }
                }).fail(function() {
                    $('#scraper-result').html('<div class="notice notice-error"><p>Failed to load status: Network error</p></div>');
                });
            }

            function loadRecentStats() {
                // This would require additional endpoint to fetch recent stats
                $('#recent-stats').html('<p>Recent stats display would require additional API endpoint.</p>');
            }

            // Load initial status
            loadStatus();
            loadRecentStats();

            // Refresh status button
            $('#refresh-status').click(function() {
                loadStatus();
            });

            // Manual scrape button
            $('#manual-scrape').click(function() {
                var $button = $(this);
                $button.prop('disabled', true).text('Running...');
                $('#scraper-result').html('<div class="notice notice-info"><p>Running manual scrape...</p></div>');

                $.post(fil_platform_ajax.ajax_url, {
                    action: 'fil_platform_manual_scrape',
                    nonce: fil_platform_ajax.nonce
                }, function(response) {
                    if (response.success) {
                        $('#scraper-result').html('<div class="notice notice-success"><p>' + response.data.message + '</p><pre>' + JSON.stringify(response.data.data, null, 2) + '</pre></div>');
                        // Refresh status after successful scrape
                        setTimeout(loadStatus, 1000);
                    } else {
                        $('#scraper-result').html('<div class="notice notice-error"><p>Scrape failed: ' + (response.data ? response.data.message : 'Unknown error') + '</p></div>');
                    }
                }).fail(function() {
                    $('#scraper-result').html('<div class="notice notice-error"><p>Scrape failed: Network error</p></div>');
                }).always(function() {
                    $button.prop('disabled', false).text('Run Manual Scrape');
                });
            });

            // Reschedule cron button
            $('#reschedule-cron').click(function() {
                var $button = $(this);
                $button.prop('disabled', true).text('Rescheduling...');
                $('#scraper-result').html('<div class="notice notice-info"><p>Rescheduling cron job...</p></div>');

                $.post(fil_platform_ajax.ajax_url, {
                    action: 'fil_platform_reschedule_cron',
                    nonce: fil_platform_ajax.nonce
                }, function(response) {
                    if (response.success) {
                        $('#scraper-result').html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                        // Refresh status to show new schedule
                        setTimeout(loadStatus, 1000);
                    } else {
                        $('#scraper-result').html('<div class="notice notice-error"><p>Reschedule failed: ' + (response.data ? response.data.message : 'Unknown error') + '</p></div>');
                    }
                }).fail(function() {
                    $('#scraper-result').html('<div class="notice notice-error"><p>Reschedule failed: Network error</p></div>');
                }).always(function() {
                    $button.prop('disabled', false).text('Reschedule Cron Job');
                });
            });
        });
        </script>

        <style>
        .card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .card h2.title {
            margin-top: 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        #scraper-result pre {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        </style>
        <?php
    }
}

// Initialize admin
new FIL_Platform_Admin();
