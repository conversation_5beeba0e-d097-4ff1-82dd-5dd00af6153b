<!DOCTYPE html>
<html>
<head>
    <title>Test Supabase Auth API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin-top: 20px; padding: 15px; background: #f9f9f9; border: 1px solid #ddd; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Supabase Auth API</h1>
        <p>This page tests the Supabase Auth API to debug the user creation issue.</p>
        
        <button onclick="testAuth()">Test Auth API</button>
        
        <div id="result" class="result" style="display: none;">
            <h3>Result:</h3>
            <pre id="resultContent"></pre>
        </div>
    </div>

    <script>
        async function testAuth() {
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            try {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result';
                resultContent.textContent = 'Testing...';
                
                const response = await fetch('/wp-json/fil-platform/v1/test-auth', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': window.wpData ? window.wpData.nonce : ''
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultContent.textContent = JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultContent.textContent = 'Error: ' + data.message;
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultContent.textContent = 'Network Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
